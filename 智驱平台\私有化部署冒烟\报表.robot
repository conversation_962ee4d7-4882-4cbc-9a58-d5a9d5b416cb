*** Settings ***
Documentation     高伟
Test Setup        Run Keywords    环境设定
Test Teardown     Close Browser
Library           SeleniumLibrary
Resource          ../关键字/业务关键字/Athena平台控件.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/公共方法.robot
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/私有化部署关键字/报表巡检.robot
Resource          ../配置/全局参数.robot

*** Variables ***
${USERNAME}       qcuser006
${PASSWORD}       qcuser006

*** Test Cases ***
打开报表（TBB）
    [Setup]    Run Keywords    环境设定
    ${username}    Set Variable If    '${ENV}'=='private.test'    default
    ${password}    Set Variable If    '${ENV}'=='private.test'    1qaz@WSX
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    报表
    ABI报表    问题处理进度查询
    刷新页面
    TBB报表    计划变更原因明细表
    关闭浏览器
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    报表
    定制报表    hl_test_abi报表
