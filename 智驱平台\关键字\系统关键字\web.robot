*** Settings ***
Library           SeleniumLibrary
Resource          ../../配置/全局参数.robot
Resource          ../../元素/开发平台元素.robot
Library           Collections
Library           OperatingSystem
Library           BrowserMobProxyLibrary

*** Keywords ***
打开网页
    [Arguments]    ${url}    ${brower}=gc    ${alias}=None
    ## 启动代理服务
    # 浏览器别名给一个唯一值默认值，避免重复操作同一个别名的浏览器
    ${unique}    Evaluate    str(uuid.uuid4())    modules=uuid
    ${alias}    Set Variable If    '${alias}'=='None'    ${unique}    ${alias}
    Run Keyword If    '${apiScan}'=='true'    使用代理浏览器    ${url}    ${browser}    ${alias}
    ...    ELSE    不使用代理浏览器    ${url}    ${browser}    ${alias}

切换浏览器
    [Arguments]    ${alias}
    log    ${alias}
    Switch Browser    ${alias}
    #先注释，后续看使用情况
#    Go To    ${ATHENA_ENV}

使用代理浏览器
    [Arguments]    ${url}    ${browser}    ${alias}
    Open Browser    ${url}    ${browser}    ${alias}    options=add_argument('--lang=zh-CN');add_argument('--no-sandbox');add_argument('--disable-gpu');add_argument('--disable-dev-shm-usage');add_argument("--proxy-server=${BrowserMob_Proxy.proxy}"); add_argument("--ignore-certificate-errors")
    Run Keyword If    '${browser}'=='headlesschrome'    Set Window Size    1920    1080
    ...    ELSE    Maximize Browser Window

不使用代理浏览器
    [Arguments]    ${url}    ${browser}    ${alias}
    ${EXPORT_PATH}=    Set Variable    ${EXECDIR}\\ExportFile
    Log    Original Path: ${EXPORT_PATH}
    # 替换单个反斜杠为四个反斜杠
    ${PATH}=    Replace String    ${EXPORT_PATH}    \\    \\\\
    Log    Escaped Path: ${PATH}
    Open Browser    ${url}    ${browser}    ${alias}    options=add_argument('--lang=zh-CN');add_argument('--no-sandbox');add_argument('--disable-gpu');add_argument('--disable-dev-shm-usage');add_experimental_option('prefs',{'download.default_directory':'${PATH}'})
    Run Keyword If    '${browser}'=='headlesschrome'    Set Window Size    1920    1080
    ...    ELSE    Maximize Browser Window

跳转网页
    [Arguments]    ${url}
    Sleep    10
    Go To    ${ATHENA_ENV}${url}
    Sleep    10
    Wait Until Location Is    ${ATHENA_ENV}${url}
    当前页面不可见元素    //span[@class='ant-spin-dot ant-spin-dot-spin ng-star-inserted']    

点击
    [Arguments]    ${loc}    ${timeout}=${timeout}
    出现指定字符则刷新页面    一站式问题排查
    Sleep    0.5
    Log    ${loc}
    Wait Until Element Is Visible    ${loc}    ${timeout}
    Click Element    ${loc}
    Sleep    0.5

出现指定字符则刷新页面
    [Arguments]    ${text}
    ${current}    Set Variable    0
    WHILE    ${current} < 1
        ${visible}    Run Keyword And Return Status    当前页面可见字符    ${text}    0.1
        Run Keyword If    ${visible}    Run Keywords    Sleep    30    AND  Reload Page
        ...    ELSE    Exit For Loop
        ${current}    Evaluate    ${current} + 1
    END

点击(元素不一定可见)
    [Arguments]    ${loc}
    Sleep    0.5
    Log    ${loc}
    Wait Until Page Contains Element    ${loc}    ${timeout}
    Click Element    ${loc}
    Sleep    0.5

双击
    [Arguments]    ${loc}
    Sleep    0.5
    Wait Until Element Is Visible    ${loc}    ${timeout}
    Double Click Element    ${loc}
    Sleep    0.5

获取元素字符
    [Arguments]    ${loc}
    Wait Until Element Is Visible    ${loc}    ${timeout}
    ${text}    Get Text    ${loc}
    RETURN    ${text}

鼠标悬停
    [Arguments]    ${loc}    ${timeout}=${timeout}
    Wait Until Element Is Visible    ${loc}    ${timeout}
    Mouse Over    ${loc}

输入
    [Arguments]    ${loc}    ${text}
    Log    ${text}
    Wait Until Element Is Visible    ${loc}    ${timeout}
    Input Text    ${loc}    ${text}    clear=True


当元素可用
    [Arguments]    ${loc}
    Wait Until Element Is Enabled    ${loc}    ${timeout}

当前页面可见
    [Arguments]    ${loc}    ${timeout}=${timeout}
    出现指定字符则刷新页面    一站式问题排查
    Log    ${loc}
    Wait Until Element Is Visible    ${loc}    ${timeout}

当前页面包含元素
    [Arguments]    ${loc}    ${timeout}=${timeout}
    Log    ${loc}
    Wait Until Page Contains Element    ${loc}    ${timeout}

当前页面可见字符
    [Arguments]    ${text}    ${timeout}=${timeout}
    Log    ${text}
    Wait Until Page Contains    ${text}    ${timeout}

当前页面不可见字符
    [Arguments]    ${text}    ${timeout}=${timeout}
    Sleep    3
    Wait Until Page Does Not Contain    ${text}    ${timeout}

当前页面不存在元素
    [Arguments]    ${loc}    ${timeout}=10
    Wait Until Page Does Not Contain Element    ${loc}    ${timeout}

关闭浏览器
    Run Keyword If    '${apiScan}'=='true'    获取接口信息
    Close All Browsers

获取接口信息
    ${har}=    Get Har As JSON
    #    log    ${har}
    ${res}    json_handle    ${har}
    Log Many    ${res}
    #日志只写错误的接口信息，如果返回的json不为空，则说明发生了接口错误
    Should Be Empty    ${res}
    #    create file    \    ${EXECDIR}${/}file.har    ${har}
    #    log to console    \    Browsermob Proxy HAR file saved as ${EXECDIR}${/}file.har
    Stop Local Server

选择浏览器窗体
    [Arguments]    ${index}
    [Documentation]    选择浏览器中的窗体,按打开的的顺序
    ...    -1为最新打开
    ${handles}    Get Window Handles
    Log Many    ${handles}
    ${newWindow}    Get From List    ${handles}    ${index}
    Switch Window    ${newWindow}
    Sleep    5
    Run Keyword If    '${browser}'=='headlesschrome'    Set Window Size    1920    1080
    ...    ELSE    Maximize Browser Window

上传附件
    ${file}    Set Variable    ${CURDIR}${/}..${/}..${/}..${/}123.txt
    Comment    click element    //span[contains(text(),'请上传')]
    Sleep    10
    Choose File    //div[@class='ant-upload']//ath-upload-entrance-form[@class='ath-upload-entrance-form ng-star-inserted']//span[@class='ath-upload-text'][contains(text(),'请上传')]    ${file}
    Sleep    10

鼠标滑动到底部
    Comment    Set Focus To Element    //span[contains(text(),'${mouse}')]
    Comment    ${scrollable_element}    Get WebElement    //span[contains(text(),'${mouse}')]
    Execute JavaScript    window.scrollTo(0, document.body.scrollHeight)
    Sleep    10

滑动元素到可见区域
    [Arguments]    ${loc}    ${timeout}=${timeout}
    Wait Until Page Contains Element    ${loc}    ${timeout}
    Scroll Element Into View    ${loc}

按顺序获取元素
    [Arguments]    ${loc}    ${index}
    ${elements}    获取元素集合    ${loc}
    ${element}    Get From List    ${elements}    ${index}
    RETURN    ${element}


获取元素数量
    [Arguments]    ${loc}    ${timeout}=${timeout}
    Wait Until Element Is Visible    ${loc}    ${timeout}
    ${count}    get_element_count    ${loc}
    RETURN    ${count}

获取元素集合
    [Arguments]    ${loc}    ${timeout}=${timeout}
    Wait Until Element Is Visible    ${loc}    ${timeout}
    ${collect}    Get WebElements    ${loc}
    RETURN    ${collect}

选择窗体
    [Arguments]    ${window}
    Run Keyword If    '${ENV}'=='microsoft.prod'    Sleep    30
    Sleep    30
    Switch Window    ${window}
    Run Keyword If    '${browser}'=='headlesschrome'    Set Window Size    1920    1080
    ...    ELSE    Maximize Browser Window

iframe选择
    [Arguments]    ${iframe}=//iframe
    Wait Until Element Is Visible    ${iframe}    ${timeout}
    Select Frame    ${iframe}

判断元素是否可见
    [Arguments]    ${loc}    ${timeout}=3
    ${element_exists}    Run Keyword And Return Status    当前页面可见    ${loc}    ${timeout}
    RETURN    ${element_exists}

判断字符是否可见
    [Arguments]    ${loc}    ${timeout}=${timeout}
    ${element_exists}    Run Keyword And Return Status    当前页面可见字符    ${loc}    ${timeout}
    RETURN    ${element_exists}

回到上一页
    Go Back

点击（文本）
    [Arguments]    ${text}
    点击    //*[contains(text(),'${text}')]

元素存在则点击
    [Arguments]    ${loc}    ${timeout}=5
    ${element_exists}    Run Keyword And Return Status    当前页面可见    ${loc}    ${timeout}
    Run Keyword If    ${element_exists}    Click Element    ${loc}

刷新页面
    Reload Page

当前页面不可见元素
    [Arguments]    ${loc}    ${timeout}=${timeout}
    Sleep    3
    Wait Until Element Is Not Visible    ${loc}    ${timeout}

js点击
    [Arguments]    ${loc}
    当前页面可见    ${loc}
    Execute Javascript    document.evaluate("${loc}", document, null, XPathResult.ANY_TYPE, null).iterateNext().click()

当前页面若文本不存在自动刷新页面
    [Arguments]    ${max_refresh_count}    ${TEXT_TO_CHECK}
    ${refresh_count}    Set Variable    0
    WHILE    ${refresh_count} < ${max_refresh_count}
        ${text_exists}    Run Keyword And Return Status    当前页面可见字符    ${TEXT_TO_CHECK}    30
        Log    ${text_exists}
        Run Keyword If    ${text_exists}    Exit For Loop
        Log    Text not found, refreshing page ${refresh_count}
        Reload Page
        sleep    5
        ${refresh_count}    Evaluate    ${refresh_count} + 1
    END

当前页面若文本存在自动刷新页面
    [Arguments]    ${max_refresh_count}    ${TEXT_TO_CHECK}
    ${refresh_count}    Set Variable    0
    WHILE    ${refresh_count} < ${max_refresh_count}
        ${text_exists}    Run Keyword And Return Status    当前页面不可见字符    ${TEXT_TO_CHECK}    30
        Log    ${text_exists}
        Run Keyword If    ${text_exists}    Exit For Loop
        Log    Text not found, refreshing page ${refresh_count}
        Reload Page
        sleep    5
        ${refresh_count}    Evaluate    ${refresh_count} + 1
    END

获取元素值
    [Arguments]    ${loc}    ${timeout}=${timeout}
    Wait Until Element Is Visible    ${loc}    ${timeout}
    ${value}    Get Value    ${loc}
    RETURN    ${value}

当元素不可见则刷新页面
    [Arguments]    ${refreshCount}    ${targetLoc}    ${timeout}=${timeout}
    ${current}    Set Variable    0
    WHILE    ${current} < ${refreshCount}
        ${visible}    Run Keyword And Return Status    当前页面可见    ${targetLoc}    ${timeout}
        IF    ${visible}    Exit For Loop
        ...    ELSE    Reload Page
        ${current}    Evaluate    ${current} + 1
    END

获取元素属性值
    [Arguments]    ${loc}    ${attribute}    ${timeout}=${timeout}
    Wait Until Element Is Visible    ${loc}    ${timeout}
    ${value}    Get Element Attribute    ${loc}    ${attribute}
    RETURN    ${value}





