*** Settings ***
Suite Setup       Run Keywords    环境设定
Suite Teardown    Run Keywords    关闭浏览器
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/业务关键字/Athena平台控件.robot
Resource          ../关键字/业务关键字/智能入口.robot
Resource          ../关键字/业务关键字/Athena平台.robot


*** Variables ***
${simplified_text}    友情链接
${traditional_text}    友情鏈接
${english_text}    LINK

*** Test Cases ***
语言设定（识别切换）
    [Documentation]
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='paas'    TestAthenaAutoTestAi001
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='paas'    TestAthenaAutoTestAi001
    登录Athena平台    ${ownerUsername}    ${ownerPassword}
    ${title}    获取元素值    (//span[@class='title'])[3]
    Run Keyword If    '${title}' == '${simplified_text}'    语言设定为繁體     #如果是 简体 则设置为 繁體
    Run Keyword If    '${title}' == '${traditional_text}'    语言设定为简体    #如果是 繁体 则设置为 简体
    Run Keyword If    '${title}' == '${english_text}'    语言设定为繁體    #如果是 英文 则设置为 繁體
    关闭浏览器

发送方式
    [Documentation]
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='paas'    TestAthenaAutoTestAi001
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='paas'    TestAthenaAutoTestAi001
    登录Athena平台    ${username}    ${password}
    ${title}    获取元素值    (//span[@class='title'])[3]
    #如果是 简体 则设置为 "两者同时提醒"
    Run Keyword If    '${title}' == '${simplified_text}'    发送方式设定-简体    两者同时提醒
    #如果是 繁体 则设置为 "兩者同時提醒"
    Run Keyword If    '${title}' == '${traditional_text}'    发送方式设定-繁体    兩者同時提醒
    关闭浏览器

查看升级说明
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${check_text}    Set Variable If    '${ENV}'=='huawei.test'    更新日期    '${ENV}'=='huawei.prod'    更新公告    '${ENV}'=='microsoft.prod'    更新公告
    登录Athena平台    ${ownerUsername}    ${ownerPassword}
    点击    ${首页.问号}
    点击    ${问号.升级说明}
    当前页面可见字符    更新公告
    关闭浏览器

经典门户巡检
#    ${username}    Set Variable    HL18271405997
#    ${password}    Set Variable    HuangL0920
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${tenant}    Set Variable If    '${ENV}'=='pressure'    自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    ${key}    Set Variable If    '${ENV}'=='huawei.test'    项目PCC发起    '${ENV}'=='huawei.prod'    搜索测试1012    '${ENV}'=='microsoft.prod'    搜索测试1012
    ${work}    Set Variable If    '${ENV}'=='huawei.test'    签核    '${ENV}'=='huawei.prod'    搜索测试1012手动任务    '${ENV}'=='microsoft.prod'    搜索测试1012手动任务
    ${type}    Set Variable    经典门户
    登录Athena平台    ${username}    ${password}    tenant=${tenant}    type=${type}
    模版切换    通用模板
    一级菜单校验
    二级菜单校验
    项目/任务搜索    全部    ${key}    ${key}
    项目/任务搜索    项目    ${key}    ${key}
    项目/任务搜索    任务    ${work}    ${work}
    搜索    友情链接应用    鼎捷云控制台    鼎捷云控制台
    鼎捷云打开判断
    发起项目/基础资料/报表搜索    发起项目    五要素    五要素
    发起项目/基础资料/报表搜索    业务数据录入    项目大事记    项目大事记
    发起项目/基础资料/报表搜索    业务数据录入    维护工作历信息        维护工作历信息
    发起项目/基础资料/报表搜索    报表    异常明细检讨    异常明细检讨
    #全局搜索切换TAB    全部    项目    项目    业务数据录入    报表
    [Teardown]    Run Keywords    关闭浏览器

自定义菜单
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${tenant}    Set Variable If    '${ENV}'=='pressure'    自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    ${type}    Set Variable    经典门户
    登录Athena平台    ${username}    ${password}    tenant=${tenant}    type=${type}
    模版切换    自动化测试门户
    点击右侧菜单    自动化测试一级菜单
    点击右侧菜单    自动化测试二级菜单
    点击右侧菜单    项目中控台
    当前页面可见    //span[contains(text(),'提交')]
    [Teardown]    Run Keywords    关闭浏览器