*** Settings ***
Library           SeleniumLibrary
#Library           AutoItLibrary
#Library           RFLib/Base.py
#Library           ExcelLibrary
#Library           Collections
Library           RPA.Excel.Files
Library           OperatingSystem
Resource          ../../元素/开发平台元素.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/web.robot
Resource          公共方法.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../系统关键字/web.robot
Resource          Athena平台控件.robot
Resource          Athena平台.robot
Resource          首页.robot
Resource          待办.robot


*** Keywords ***

校验Excel文件结构
    [Documentation]    校验 Excel 文件的 sheet 数量和每个 sheet 的数据行数
    ...
    ...    参数说明：
    ...    ${file_path} - Excel 文件路径
    ...    ${expected_sheet_count} - 预期的 sheet 数量
    ...    ${expected_row_counts} - 预期的各 sheet 行数（字典格式，如 {"Sheet1": 10, "Sheet2": 5}）
    [Arguments]    ${file_name}    ${expected_sheet_count}    ${expected_row_counts}
    ${filepath}=    Join Path    ${EXECDIR}    ExportFile    ${filename}.xlsx
#    传入的字典可能会被认为是str,这里将传入的内容转化为字典格式
    ${dict} =    Evaluate    ast.literal_eval('''${expected_row_counts}''')    modules=ast
    # 打开 Excel 文件
    Open Workbook    ${file_path}
    # 获取所有 sheet 名称
    ${sheets}=    List Worksheets
    ${actual_sheet_count}=    Get Length    ${sheets}
    # 校验 sheet 数量
    Should Be Equal As Numbers    ${actual_sheet_count}    ${expected_sheet_count}
    ...    msg=Sheet 数量不匹配。预期 ${expected_sheet_count} 个，实际 ${actual_sheet_count} 个
#     校验每个 sheet 的行数
    FOR    ${sheet_name}    IN    @{sheets}
        # 切换到当前 sheet
        Set Active Worksheet    ${sheet_name}
        # 获取实际行数（跳过表头）
        ${table}=    Read Worksheet As Table    header=True
        ${actual_row_count}=    Get Length    ${table}
        Log    实际sheet页行数为: ${actual_row_count}
        # 从预期值中获取当前 sheet 的预期行数
        ${expected_row_count}=    Get From Dictionary    ${dict}    ${sheet_name}
        ...    default=${None}
        Log    预期sheet页行数为: ${expected_row_count}
        # 校验行数
        Run Keyword If    ${expected_row_count} != ${None}
        ...    Should Be Equal As Numbers    ${actual_row_count}    ${expected_row_count}
        ...    msg=Sheet "${sheet_name}" 行数不匹配。预期 ${expected_row_count} 行，实际 ${actual_row_count} 行
    END
    Close Workbook
#    校验完成删除指定目录的文件
    ${exists} =    Run Keyword And Return Status    File Should Exist    ${filepath}
    Run Keyword If      ${exists}    Remove File    ${filepath}



后端分页,部分导出后下载文件
#    选择三条数据
    [Arguments]    ${export_file_name}
    点击    (//span[@class='ant-checkbox'])[2]
    点击    (//span[@class='ant-checkbox'])[3]
    点击    (//span[@class='ant-checkbox'])[4]
    点击    //span[contains(text(),'数据导出')]
    点击    //*[contains(text(),'导出选中')]
    输入   //input[@formcontrolname='fileName']    ${export_file_name}
    点击    //span[contains(text(),'确定')]
    点击    //span[contains(text(),'跳转')]
    点击    //span[contains(text(), '${export_file_name}')]/following::span[contains(text(), '下载文件')][1]
    Sleep    5


后端分页,跨页选择部分数据导出后下载文件
    [Arguments]    ${export_file_name}
#    第一页选择数据
    点击    (//span[@class='ant-checkbox'])[2]
#    第二页选择数据
    点击    //a[normalize-space()='2']
    点击    (//span[@class='ant-checkbox'])[2]
    点击    (//span[@class='ant-checkbox'])[3]
    点击    //span[contains(text(),'数据导出')]
    点击    //*[contains(text(),'导出选中')]
    输入   //input[@formcontrolname='fileName']    ${export_file_name}
    点击    //span[contains(text(),'确定')]
    点击    //span[contains(text(),'跳转')]
    点击    //span[contains(text(), '${export_file_name}')]/following::span[contains(text(), '下载文件')][1]
    Sleep    5



表头筛选
    [Documentation]    参数说明
    ...    ${TitleName} - 表示需要筛选的表头字段
    ...    ${index} - 表示第一个筛选的按钮顺位，例如你要筛选表头第一个字段，那么index就是 1,以此类推
    ...    ${key} - 表示你要输入什么关键字进行筛选
    [Arguments]    ${TitleName}    ${index}    ${key}
    鼠标悬停    //span[@title='${TitleName}']
    点击    (//span[@class='ag-icon ag-icon-filter'])[${index}]
    输入    //input[@placeholder='请输入关键词']    ${key}
    点击    //span[contains(text(),'确定')]
    Sleep    3
    

多条件排序-升序
#    点击排序按钮
    [Documentation]    参数说明
    ...    ${SelectedField} - 筛选所选择的字段    
    [Arguments]    ${SelectedField}
    Js点击     //span[@class='ag-icon ag-icon-paixu1']
    ${exist}    判断元素是否可见    //span[@class='ng-star-inserted'][contains(text(),'${SelectedField}')]
    IF    ${exist}
        点击    //span[@class='ag-icon ag-icon-delete']
        点击    //div[contains(text(),"添加条件")]
    ELSE
        Log    nothing
    END
    点击    (//*[@class="ant-select-selection-search ng-star-inserted"])[2]
    点击    //*[@class='option-item-label'][contains(text(),'${SelectedField}')]
    点击    (//*[@class="ant-select-selection-search ng-star-inserted"])[3]
    点击    //span[contains(text(),'升序')]
    点击    //span[contains(text(),'确定')]


快速查询+排序
    [Arguments]    ${SelectedField}
    Js点击    //span[@class='advanced-query-icon ng-star-inserted']
    点击    //span[contains(text(),'重置')]
    Sleep    2
    Js点击    //span[@class='advanced-query-icon ng-star-inserted']
    输入    (//input[@placeholder="最小值"])[1]    2
    输入    (//input[@placeholder="最大值"])[1]    10
    点击    (//*[@class="ant-select-selection-search ng-star-inserted"])[2]
    点击    //*[@class='option-item-label'][contains(text(),'${SelectedField}')]
    点击    (//*[@class="ant-select-selection-search ng-star-inserted"])[3]
    点击    //span[contains(text(),'升序')]
    Sleep    1
    点击    //span[contains(text(),'确定')]
    Sleep    2


高级查询+排序
    [Arguments]    ${SelectedField}
    Js点击    //span[@class='advanced-query-icon ng-star-inserted']
    点击    //span[contains(text(),'重置')]
    Sleep    2
    Js点击    //span[@class='advanced-query-icon ng-star-inserted']
    点击    //*[@class='header-title']//span[contains(text(),'高级查询')]
    点击    (//ath-select//*[@athtooltipplacement="bottom"])[2]
    点击    //*[@class='option-item-label'][contains(text(),'${SelectedField}')]
    点击    (//ath-select//*[@athtooltipplacement="bottom"])[3]
    点击    (//*[contains(text(),'包含')])[1]
    输入    //input[@placeholder='请输入']    2
    点击    (//ath-select//*[@athtooltipplacement="bottom"])[4]
    点击    //*[@class='option-item-label'][contains(text(),'${SelectedField}')]
    点击    (//ath-select//*[@athtooltipplacement="bottom"])[5]
    点击    //span[contains(text(),'升序')]
    Sleep    1
    点击    //span[contains(text(),'确定')]
    Sleep    2
    