*** Settings ***
Documentation     高伟
Resource          ../关键字/业务关键字/作业授权.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/公共方法.robot
Test Setup        Run Keywords    环境设定
Test Teardown     Close Browser

*** Test Cases ***
用户应用授权后，运行态-基础资料检查
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02
    登录Athena平台     ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    判断应用应该存在(基础数据录入入口)    带图快采

用户应用未授权，运行态-基础资料检查
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    qcsupplier001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    supplier001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录Athena平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    判断应用应该不存在(基础数据录入入口)    带图快采
    判断应用应该存在(基础数据录入入口)    项目中控台

用户公共应用作业模组展示检查
    ${ownerUsername01}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    7
    ${ownerPassword01}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    Quality001
    ${ownerUsername02}    Set Variable If    '${ENV}'=='huawei.test'    qcsupplier001    '${ENV}'=='huawei.prod'    qcsupplier001    '${ENV}'=='microsoft.prod'    qcsupplier001
    ${ownerPassword02}    Set Variable If    '${ENV}'=='huawei.test'    supplier001    '${ENV}'=='huawei.prod'    supplier001    '${ENV}'=='microsoft.prod'    supplier001
    登录Athena平台     ${ownerUsername01}    ${ownerPassword01}
    进入业务数据录入菜单
    判断作业模组列表应该存在(基础数据录入入口)      维护工艺信息    维护工种信息    维护生产任务    业务数据
    退出当前登录,切换账号登录    ${ownerUsername02}    ${ownerPassword02}
    进入业务数据录入菜单
    判断作业模组列表不应该存在(基础数据录入入口)    维护工艺信息    维护工种信息    维护生产任务
    
