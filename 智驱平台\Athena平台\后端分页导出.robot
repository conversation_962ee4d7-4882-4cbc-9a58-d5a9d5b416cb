*** Settings ***
Documentation     高伟
Resource          ../关键字/业务关键字/作业授权.robot
Resource          ../关键字/业务关键字/基础数据录入.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/后端分页导出.robot
Resource          ../关键字/业务关键字/公共方法.robot
Resource          ../关键字/控件关键字/表格筛选.robot
Test Setup        Run Keywords    环境设定
Test Teardown     Run Keywords    错误处理    AND    关闭浏览器


*** Test Cases ***
#    -------------------------------------------------------单档--------------------------------------------------------------------------
后端分页-单档多栏选中部分数据后直接导出
    ${exportfilename}    Set Variable    单档多栏-后端分页${唯一标识}
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称    单档多栏-后端分页    后端分页1.0导出应用
    后端分页,部分导出后下载文件    ${exportfilename}
    校验Excel文件结构    ${exportfilename}    1    {"单档-后端分页(single_document)": 4}


后端分页-单档多栏筛选后部分数据导出
    ${exportfilename}    Set Variable    单档多栏-后端分页${唯一标识}
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称    单档多栏-后端分页    后端分页1.0导出应用
    表头筛选    需求编号     1    2
    后端分页,部分导出后下载文件    ${exportfilename}
    校验Excel文件结构    ${exportfilename}    1    {"单档-后端分页(single_document)": 4}


后端分页-单档多栏跨页选择数据后部分导出
    ${exportfilename}    Set Variable    单档多栏-后端分页${唯一标识}
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称    单档多栏-后端分页    后端分页1.0导出应用
    后端分页,跨页选择部分数据导出后下载文件    ${exportfilename}
    校验Excel文件结构    ${exportfilename}    1    {"单档-后端分页(single_document)": 4}

    
后端分页-单档多栏_多条件排序后部分导出
    ${exportfilename}    Set Variable    单档多栏-后端分页${唯一标识}
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称    单档多栏-后端分页    后端分页1.0导出应用
    多条件排序-升序    需求编号
    后端分页,部分导出后下载文件    ${exportfilename}
    校验Excel文件结构    ${exportfilename}    1    {"单档-后端分页(single_document)": 4}


后端分页-单档多栏_快速查询+排序后部分导出
    ${exportfilename}    Set Variable    单档多栏-后端分页${唯一标识}
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称    单档多栏-后端分页    后端分页1.0导出应用
    快速查询+排序    需求编号
    后端分页,部分导出后下载文件    ${exportfilename}
    校验Excel文件结构    ${exportfilename}    1    {"单档-后端分页(single_document)": 4}


后端分页-单档多栏_高级查询+排序后部分导出
    ${exportfilename}    Set Variable    单档多栏-后端分页${唯一标识}
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称    单档多栏-后端分页    后端分页1.0导出应用
    高级查询+排序    需求编号
    后端分页,部分导出后下载文件    ${exportfilename}
    校验Excel文件结构    ${exportfilename}    1    {"单档-后端分页(single_document)": 4}



# ---------------------------------------------------------双档---------------------------------------------------------------------------

后端分页-双档选中部分数据后直接导出
    ${exportfilename}    Set Variable    双档-后端分页${唯一标识}
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称    双档-后端分页    后端分页1.0导出应用
    后端分页,部分导出后下载文件    ${exportfilename}
    校验Excel文件结构    ${exportfilename}    2    {"双档_后端分页(double_document)": 4,"子表(double_document_child)": 4}
    

后端分页-双档筛选后部分数据导出
    ${exportfilename}    Set Variable    双档-后端分页${唯一标识}
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称     双档-后端分页    后端分页1.0导出应用
    表头筛选    清洗需求编号    1    2
    后端分页,部分导出后下载文件    ${exportfilename}
    校验Excel文件结构    ${exportfilename}    2    {"双档_后端分页(double_document)": 4,"子表(double_document_child)": 4}

后端分页-双档跨页选择数据后部分导出
    ${exportfilename}    Set Variable    双档-后端分页${唯一标识}
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称    双档-后端分页    后端分页1.0导出应用
    后端分页,跨页选择部分数据导出后下载文件    ${exportfilename}
    校验Excel文件结构    ${exportfilename}    2     {"双档_后端分页(double_document)": 4,"子表(double_document_child)": 4}


后端分页-双档_多条件排序后部分导出
    ${exportfilename}    Set Variable    双档-后端分页${唯一标识}
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称    双档-后端分页    后端分页1.0导出应用
    多条件排序-升序    清洗需求编号
    后端分页,部分导出后下载文件    ${exportfilename}
    校验Excel文件结构    ${exportfilename}    2    {"双档_后端分页(double_document)": 4,"子表(double_document_child)": 4}


后端分页-双档_快速查询+排序后部分导出
    ${exportfilename}    Set Variable    双档-后端分页${唯一标识}
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称    双档-后端分页    后端分页1.0导出应用
    快速查询+排序    清洗需求编号
    后端分页,部分导出后下载文件    ${exportfilename}
    校验Excel文件结构    ${exportfilename}    2    {"双档_后端分页(double_document)": 4,"子表(double_document_child)": 4}


后端分页-双档_高级查询+排序后部分导出
    ${exportfilename}    Set Variable    单档多栏-后端分页${唯一标识}
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称    双档-后端分页    后端分页1.0导出应用
    高级查询+排序    清洗需求编号
    后端分页,部分导出后下载文件    ${exportfilename}
    校验Excel文件结构    ${exportfilename}    2    {"双档_后端分页(double_document)": 4,"子表(double_document_child)": 4}


