*** Settings ***
Library           SeleniumLibrary
Resource          ../系统关键字/web.robot
Resource          ../../元素/Athena平台元素.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../系统关键字/接口.robot
Resource          Athena平台控件.robot
Resource          Athena平台.robot
Resource          待办.robot
Resource          ../../配置/Athena平台.robot
Resource          待办.robot

*** Keywords ***
报表模板创建
    [Arguments]    ${key}
    点击    //span[contains(text(),'[001]自动化')]
    js点击    //span[contains(text(),'新增模板')]
    输入    //div[contains(text(),'编号')]/following::input[1]    ${key}
    输入    //div[contains(text(),' 名称： ')]/following::input[1]    ${key}
    点击    //*[contains(text(),'未生效')]
    点击    //ath-option-item[@class='ant-select-item ant-select-item-option athena-select-item ng-star-inserted']
    输入    //div[contains(text(),' 版本号： ')]/following::input[1]    ${key}
    点击    //div[contains(text(),'类型：')]/following::i[1]
    点击    //span[contains(text(),'[12]利润表')]
    点击    //div[@class="ant-table-body ng-star-inserted"]/table/tbody/tr[3]/td[1]/label/span[1]
    点击    //span[contains(text(),'保存')]
    Sleep    5
    点击    //div[@class="ag-root ag-unselectable ag-layout-normal"]/div[1]/div[1]/div/div
    点击    //span[contains(text(),'发布')]
    点击    //span[contains(text(),'确认')]

财报查阅新建
    [Arguments]    ${key}
    点击    //span[contains(text(),'0001自动化')]
    点击    //span[contains(text(),'新增报表')]
    输入    //p[contains(text(),'报表编号')]/following::input[1]    ${key}
    输入    //p[contains(text(),'报表名称')]/following::input[1]    ${key}
    点击    //div[@class="ant-tabs-content ant-tabs-content-top"]/div[2]/div/dw-add-report/div/ath-spin/nz-spin/div/div[1]/div[1]/div/div/dynamic-form-list/div/div/div[3]/div[2]/div/dynamic-operation-editor/div/div[1]/div/span[2]
    输入    //div[@class="ath-input-content-box ng-star-inserted"]//input    ${key}
    点击    //button[@class="ath-btn ant-btn ant-btn-primary ant-btn-sm ant-btn-icon"]/span
    点击    //div[@class="body"]/label[1]
    点击    //button[@class="ath-btn ant-btn ant-btn-primary ng-star-inserted active"]/span
    Sleep    2
    点击    //button[@class="ath-btn ant-btn ant-btn-primary ant-btn-lg"]/span
    点击    //div[contains(text(),'报表参数')]
    点击    //span[contains(text(),'确定')]
    #勾稽设定
    Comment    点击    //div[contains(text(),'勾稽设定')]
    Comment    点击    //span[contains(text(),'新增页面')]
    Comment    输入    //div[@class="rule-dynamic ng-star-inserted"]/div/dynamic-form-list/div/div/div[1]/div/div//input    ${key}
    Comment    输入    //div[@class="rule-dynamic ng-star-inserted"]/div/dynamic-form-list/div/div/div[2]//textarea    ${key}
    Comment    点击    //div[@class="rule-dynamic ng-star-inserted"]/div/dynamic-form-list/div/div/div[3]/div/div/dynamic-form-list/div/div/div[1]/div/div/dynamic-ant-select
    Comment    点击    //span[contains(text(),'!=')]
    Comment    输入    //div[@class="rule-dynamic ng-star-inserted"]/div/dynamic-form-list/div/div/div[3]/div/div/dynamic-form-list/div/div/div[2]//input    ${key}
    Comment    输入    //div[@class="rule-dynamic ng-star-inserted"]/div/dynamic-form-list/div/div/div[4]//textarea    ${key}
    Comment    输入    //div[@class="rule-dynamic ng-star-inserted"]/div/dynamic-form-list/div/div/div[5]//textarea    ${key}
    Comment    点击    //div[@class="rule-dynamic ng-star-inserted"]/div/dynamic-form-list/div/div/div[5]
    Comment    点击    //div[@class="cdk-overlay-container"]/div[10]/div/nz-modal-container/div/div/div[2]/button[2]
    Comment    点击    //div[@class="cdk-overlay-container"]/div[8]/div/nz-modal-container/div/div/div[3]/button[2]
    点击    //div[contains(text(),'勾稽检查')]
    Comment    当前页面可见    检查通过

删除模板
    点击    //span[contains(text(),'[001]自动化')]
    点击    //div[@class="ag-pinned-right-cols-container"]/div/div/div/span/cell-renderer/div/div/div/app-dynamic-spread-table-operate/button[4]
    点击    //span[contains(text(),'确定')]
    点击    //span[contains(text(),'确认')]

删除财报
    点击    //span[contains(text(),'0001自动化')]
    点击    //div[@class="ag-pinned-right-cols-container"]/div/div/div/span/cell-renderer/div/div/div/app-dynamic-spread-table-operate/button[3]
    点击    //span[contains(text(),'确定')]

报表模板设计
    [Arguments]    ${a}
    #文件
    点击    //span[contains(text(),'[001]自动化')]
    点击    //div[@class="ag-pinned-right-cols-container"]/div/div/div/span/cell-renderer/div/div/div/app-dynamic-spread-table-operate/button[3]
    Sleep    30
    点击    //span[contains(text(),'文件')]
    点击    //div[@class="gc-file-menu-category gc-container"]//div[@title="导出"]
    #导出excel文件
    点击    //div[@class="sub-category-content file-menu-setting-container gc-container"]//div[@title="导出 Excel 文件"]
    输入    //div[@class="gc-sjs-designer-dialog gc-designer-root cn gc-sjs-dialog-show"]//input    ${a}
    点击    //span[contains(text(),'确定')]
    Comment    当前页面可见字符    完成
    #财务报表功能
    #报表参数
    点击    //*[contains(text(),'财务报表功能')]
#    点击    //*[contains(text(),'报表参数')]
#    当前页面可见字符    报表参数
#    当前页面可见字符    确定
#    点击    //span[contains(text(),'确定')]
    Comment    #勾稽设定
    Comment    点击    //*[contains(text(),'勾稽设定')]
    Comment    点击    //*[contains(text(),'新增页面')]
    #勾稽检查
    点击    //*[contains(text(),'勾稽检查')]
    当前页面可见字符    检查通过
