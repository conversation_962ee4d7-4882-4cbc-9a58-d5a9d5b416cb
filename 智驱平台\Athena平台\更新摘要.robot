*** Settings ***
Library           RequestsLibrary
Library           Collections

*** Test Cases ***
    ${IAM_BASE_URL}    Set Variable If    '${ENV}'=='huawei.test'    https://iam-test.digiwincloud.com.cn    '${ENV}'=='huawei.prod'    https://iam.digiwincloud.com.cn    '${ENV}'=='microsoft.prod'    https://iam.digiwincloud.com
    ${BASE_URL}    Set Variable If  '${ENV}=='huawei.test' https://taskengine-test.apps.digiwincloud.com.cn/restful/standard/taskengine/api/project/create
    
*** Variables ***
${IAM_BASE_URL}    https://iam-test.digiwincloud.com.cn
${LOGIN_ENDPOINT}  /api/iam/v2/identity/login/internal
${AUTH_APP}       eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6IkF0aGVuYSIsInNpZCI6MTYzNjc3NzI1NzgyNTkyfQ.3QLTPVKsk2Mp3j_aQ3X8bQW1wCJMNWeCkL6VPoK352c
${TENANT_ID}      AthenaQCTestE
${USER_ID}        integration
${PASSWORD_HASH}  6826CC688C4AF1BD0A8DDA2DBDF8897B
${COOKIE}         HWWAFSESID=e1eb791a0813e0bae0; HWWAFSESTIME=1751881332072

*** Test Cases ***
IAM Login Test
    [Documentation]    测试IAM登录接口并获取token
    ${token}=    Get IAM Token
    Should Not Be Empty    ${token}    Failed to get token from IAM
    Log    Successfully obtained token: ${token}    level=INFO

*** Keywords ***
Get IAM Token
    [Documentation]    获取IAM登录token

    # 创建HTTP会话（只使用基础URL）
    Create Session    iam_session    ${IAM_BASE_URL}    verify=True

    # 准备请求头
    &{headers}=    Create Dictionary
    ...    digi-middleware-auth-app=${AUTH_APP}
    ...    Content-Type=application/json
    ...    Cookie=${COOKIE}

    # 准备请求体
    &{login_data}=    Create Dictionary
    ...    tenantId=${TENANT_ID}
    ...    userId=${USER_ID}
    ...    passwordHash=${PASSWORD_HASH}

    # 发送POST请求（只使用端点路径）
    ${response}=    POST On Session    iam_session
    ...    ${LOGIN_ENDPOINT}
    ...    json=${login_data}
    ...    headers=${headers}

    # 验证响应状态码
    Should Be Equal As Strings    ${response.status_code}    200

    # 提取token
    ${response_json}=    Set Variable    ${response.json()}
    Dictionary Should Contain Key    ${response_json}    token
    ${token}=    Get From Dictionary    ${response_json}    token

    # 清理会话
    Delete All Sessions

    # 返回token
    RETURN    ${token}


*** Test Cases ***
Create Project API Test
    [Documentation]    Test creating a project through API

    # Prepare headers
    ${headers}=    Create Dictionary
    ...    Routerkey=AthenaQCProdW
    ...    token=${TOKEN}
    ...    Content-Type=application/json

    # Prepare request body
    ${body}=    Create Dictionary
    ...    projectCode=PM_5bd31ec2100000dd
    ...    process_EOC=${{ {"eoc_company_id": "Cm01", "eoc_site_id": ""} }}
    ...    variables=${{ {
    ...        "classification_mode": "1",
    ...        "from_inquiry": "false",
    ...        "is_inquiry": "false",
    ...        "personInCharge": "qcuser001",
    ...        "Executor": "qcuser001"
    ...    } }}
    ...    dispatchData=${{ [{
    ...        "user_no": "AB004",
    ...        "username": "1ld"
    ...    }] }}

    # Create session and send POST request
    Create Session    taskengine    ${BASE_URL}
    ${response}=    Post Request    taskengine    /    json=${body}    headers=${headers}

    # Validate response
    Should Be Equal As Strings    ${response.status_code}    200
    Log    ${response.json()}
    
    ${response_json}=    Set Variable    ${response.json()}
    Dictionary Should Contain Key    ${response_json}    serial_number
    ${serial_number}=    Get From Dictionary    ${response_json}    serial_number
    
    # Cleanup
    Delete All Sessions