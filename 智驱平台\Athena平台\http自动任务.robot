*** Settings ***
Documentation     郭洒洒
Suite Setup       Run Keywords    环境设定
Suite Teardown    Run Keywords    关闭浏览器
Resource          ../关键字/业务关键字/http自动任务.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/系统关键字/公共方法.robot
#Resource          ../关键字/业务关键字/PCC.robot

*** Test Cases ***
http自动任务
    [Documentation]    郭洒洒
    #发起项目后自动调用业务数据录入新增接口
    ${username}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${password}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${tenant}    Set Variable If    '${ENV}'=='pressure'    自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    ${story_code}  生成秒时间戳
    ${story_name}  生成随机字符
    登录Athena平台    ${username}    ${password}    tenant=${tenant}
    打开业务数据录入
    发起项目  ${story_code}  ${story_name}
    切换业务数据录入页签
    刷新作业页签并断言表格数据项增加1
    关闭浏览器