#!/usr/local/bin/python3
# -*- coding: utf8 -*-
# Author: cjm
import requests
import time
import uuid
from Crypto.Cipher import AES
import base64

def aesEncrypt(text, secretKey, iv):
    BS = AES.block_size  # 这个等于16
    mode = AES.MODE_CBC
    def pad(s): return s + (BS - len(s) % BS) * \
                       chr(BS - len(s) % BS)
    cipher = AES.new(secretKey.encode('UTF-8'), mode, iv.encode('UTF-8'))
    encrypted = cipher.encrypt(pad(text).encode('UTF-8'))
    # 通过aes加密后，再base64加密
    b_encrypted = base64.b64encode(encrypted)
    return b_encrypted

def setHeaders(s,accessKey,secretKey):
    timeStamp = int(round(time.time() * 1000))
    combox_key = accessKey + '|' + str(uuid.uuid4()) + '|' + str(timeStamp)
    signature = aesEncrypt(combox_key, secretKey, accessKey)
    print(signature.decode('UTF-8'))
    header = {'Content-Type': 'application/json', 'ACCEPT': 'application/json', 'accessKey': accessKey,
               'signature': signature.decode('UTF-8'),'Connection': 'close'}
    s.headers.update(header)
    return s

#获取运行环境id
def get_test_plan_env(s,host,projectId,name):
    url = host + "/api/environment/list/{}".format(projectId)
    r = s.get(url)
    data = r.json().get("data")
    if data:
        for dat in data:
            if dat.get("name") == name:
                return dat.get("id")
    else:
        print("没有data值")

#测试报告
def get_report_url(s,host,customData):
    url = host + "/track/share/info/generateShareInfoWithExpired"
    body = {"customData":customData,"shareType":"PLAN_DB_REPORT","lang":None}
    r = s.post(url,json=body)
    if r:
        data = r.json().get("data")
        if data:
            shareUrl=data.get("shareUrl")
            report_url = host + "/sharePlanReport" + shareUrl
            return report_url
    else:
        print("没有返回值...")

def test_plan_run(s, host, testPlanId, projectId, envId, userId="chenjme"):
    url = host + "/track/test/plan/run"
    body = {
        "mode": "serial",
        "reportType": "iddReport",
        "onSampleError": True,
        "runWithinResourcePool": False,
        "resourcePoolId": None,
        "envMap": {
        },
        "testPlanId": testPlanId,
        "projectId": projectId,
        "userId": userId,
        "triggerMode": "MANUAL",
        "environmentType": "JSON",
        "environmentGroupId": "",
        "requestOriginator": "TEST_PLAN"
    }
    r = s.post(url, json=body)
    return r

def exec_run(accessKey, secretKey, host, projectId, envName=""):
    s = requests.session()
    s = setHeaders(s, accessKey, secretKey)  # 设置请求头
    testPlanId = "c4c136fe-ba80-4b11-ab42-79135e6c1315"
    envId = get_test_plan_env(s, host, projectId, envName)  # 获取运行环境id,这里采用调Api获取的形式
    r = test_plan_run(s, host, testPlanId, projectId, envId)
    print(r.json())
    if r.json().get("success") == True:
        data = r.json().get("data")
        if data:
            report_url = get_report_url(s, host, data)  # 获取测试报告
            print("测试计划接口调用成功,测试计划报告地址为: {}".format(report_url))


exec_run('v53gxTbfJZbhY5Aw', 'HW2fO4P7MmW5lrCe', 'http://************:8081', '6802d5a5-401f-4cf3-b64b-d8d6c9e91a44')
