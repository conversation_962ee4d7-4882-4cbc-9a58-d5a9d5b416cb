*** Settings ***
Library           SeleniumLibrary
Resource          ../系统关键字/web.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../系统关键字/接口.robot
Resource          Athena平台控件.robot
Resource          ../../元素/开发平台元素.robot
Resource          ../../配置/域名.robot
Resource          智能入口.robot
Resource          首页.robot

*** Variables ***
${首页.问号}          //*[@class='icon font-entrance iconbangzhu']
${问号.升级说明}        //*[contains(text(),'升级说明')]

*** Keywords ***
常用设置
    [Arguments]    ${set}
    点击    //span[contains(text(),'${set}')]
    点击    //ath-switch[@id='openAgent']//button[@type='button']/span[1]
    Sleep    3
    点击    //a[contains(text(),'设置')]
    点击    //i[@class="anticon athena-select-arrow-down ng-star-inserted"]
    点击    // span[contains(text(),'黄蕾')]
    点击    //input[@placeholder='开始时间']
    输入    //input[@placeholder='开始时间']    2024/09/30 00:00
    Sleep    3
    点击    //input[@placeholder='结束时间']
    输入    //input[@placeholder='结束时间']    2024/09/30 18:00
    Sleep    3
    点击    //div[@style='min-height: 84px;']
    Press Keys    //div[@style='min-height: 84px;']    Enter
    Sleep    10
    点击    //span[contains(text(),'确定')]
    Sleep    3

优化设置
    [Arguments]    ${Set}
    点击    //span[contains(text(),'${Set}')]
    点击    //ath-switch[@id='openAgent']//button[@type='button']/span[1]
    Sleep    3
    ${Keywords}    Get Element Count    //a[contains(text(),'设置')]
    Run Keyword If    '${Keywords}'=='0'
    ...    Run Keywords    点击    //ath-switch[@id='openAgent']//button[@type='button']/span[1]
    ...    AND    Sleep    3
    ...    AND    点击    //a[contains(text(),'设置')]
    ...    AND    Sleep    3
    ...    AND    代理人设置    设置
    Run Keyword If    '${Keywords}'=='1'
    ...    Run Keywords    点击    //a[contains(text(),'设置')]
    ...    AND    Sleep    3
    ...    AND    代理人设置    设置

代理人设置
    [Arguments]    ${set}
    Sleep    2
    点击    //i[@class="anticon athena-select-arrow-down ng-star-inserted"]
    点击    // span[contains(text(),'黄蕾')]
    点击    //div[@style='min-height: 84px;']
    点击    //input[@placeholder='开始时间']
    输入    //input[@placeholder='开始时间']    2024/09/30 00:00
    Sleep    3
    点击    //input[@placeholder='结束时间']
    输入    //input[@placeholder='结束时间']    2024/09/30 18:00
    Sleep    3
    点击    //div[@style='min-height: 84px;']
    Press Keys    //div[@style='min-height: 84px;']    Enter
    Sleep    10
    点击    //span[contains(text(),'确定')]
    Sleep    3

设置代理人
    [Arguments]
    Sleep    3
    ${Keywords}    get_element_count    //a[@class='ng-star-inserted'][contains(text(),'重新设置')]
    Run Keyword If    '${Keywords}'=='1'    点击    //ath-switch[@id='openAgent']//button[@type='button']
    点击    //ath-switch[@id='openAgent']//button[@type='button']/span[1]
    Sleep    3
    代理人弹框设置


选择代理人
    [Arguments]    ${user}
    Sleep    2
    点击    //i[@class="anticon athena-select-arrow-down ng-star-inserted"]
    点击    // span[contains(text(),'${user}')]
    Comment    Press Keys    //div[@style='min-height: 84px;']    Enter
    Sleep    3
    点击    // span[contains(text(),'代理人设置')]
    Sleep    3

选择业务类型
    Sleep    2
    点击    (//i[@class="anticon athena-select-arrow-down ng-star-inserted"])[2]
    Sleep    1
    点击    //span[contains(text(),'回复任务')]
    Sleep    2



输入开始结束时间
    [Arguments]    ${StartTime}    ${EndTime}
    点击    //input[@placeholder='开始时间']
    输入    //input[@placeholder='开始时间']    ${StartTime}
    Sleep    3
    点击    //input[@placeholder='结束时间']
    输入    //input[@placeholder='结束时间']    ${EndTime}
    Sleep    3
    Comment    点击    //div[@style='min-height: 84px;']
    Comment    Press Keys    //div[@style='min-height: 84px;']    Enter
    Comment    点击    //*[contains(text(),'')]
    Sleep    10

代理人弹框设置
    选择代理人    豆渣
    输入开始结束时间    2098/09/30 00:10    2099/09/30 18:00
    Sleep    2
    点击    //span[contains(text(),'代理人设置')]
    Sleep    3
    #优化健壮性--缺少搜索的用例
    输入    //input[@formcontrolname='taskDefName']   回复任务
    选择业务类型
    Sleep  3
    #点击搜索
    Js点击  //button[@class='ath-btn ant-btn ant-btn-primary ant-btn-sm ant-btn-icon']
    Sleep  3
    点击    //div[text()='回复任务']/preceding::label[1]
    Sleep  3
    点击    //span[contains(text(),'确定')]
    当前页面可见字符    代理成功

常用设置左侧菜单栏
    [Arguments]    ${agr}
    点击    //span[contains(text(),'${agr}')]

代理变更历程展示
    [Arguments]    ${log}    ${act}
    点击    //a[contains(text(),'${log}')]
    Sleep    10
    ${action}    Get Text    //span[contains(text(),'${act}')]
    Log    $（action）
    Should Be Equal    ${action}    新增
    点击    //i[@class='anticon ant-modal-close-icon anticon-close ng-star-inserted']

取消代理人设置
    点击    //ath-switch[@id='openAgent']//button[@type='button']

语言设定
    [Arguments]    ${language}=简体
    ${title}    获取元素字符    //li[@class='ath-tab-menus-li ng-star-inserted ath-tab-menus-left-active']
    IF    '${title}'=="首頁" and '${language}'=='简体'
        语言设定为简体
    ELSE IF    '${title}'=="首頁" and '${language}'=='繁体'
        Log    当前语言已经是繁体
    ELSE IF    '${title}'=="首页" and '${language}'=='简体'
        Log    当前语言已经是简体
    ELSE IF    '${title}'=="首页" and '${language}'=='繁体'
        语言设定为繁體
    ELSE
        Log    未知错误
    END
    关闭标签

#语言设定为
#    [Arguments]    ${language}
#    鼠标悬停    //i[@class='icon font-entrance iconrenyuan1']    5
#    sleep    1
#    点击    //*[contains(text(),'設定')]
#    Sleep    1
#    点击    //span[contains(text(),'個性化設定')]
#    Sleep    1
#    点击    //ath-select[@id='setLanguage']
#    Sleep    1
#    点击    //span[contains(text(),'简体')]
#    Sleep    10
#    关闭标签

语言设定为简体
    鼠标悬停   //i[contains(@class,'icon font-entrance iconrenyuan1')]
    Sleep    1
    点击    //span[text()='設定']
    Sleep    1
    点击    //span[contains(text(),'個性化設定')]
    Sleep    1
    点击    //ath-select[@id='setLanguage']
    Sleep    1
    点击    //span[contains(text(),'简体')]
    Sleep    10
    #关闭标签

语言设定为繁體
    鼠标悬停    //i[contains(@class,'icon font-entrance iconrenyuan1')]
    Sleep    1
    点击    //span[text()='设置']
    Sleep    1
    点击    //span[contains(text(),'个性化设定')]
    Sleep    1
    点击    //ath-select[@id='setLanguage']
    Sleep    1
    点击    //span[contains(text(),'繁體')]
    Sleep    10

门户主题
    [Arguments]    ${type}
    Log    ${type}
#    ${status}    Run Keyword And Return Status    鼠标悬停    //i[contains(@class,'iconyonghu')]    20
#    IF    '${status}'=='False'
#        鼠标悬停    //i[@class='icon font-entrance iconrenyuan1']    20
#    END
#    #sleep    3
#    ${text}    获取元素字符    //li[@apphideelement="portalSwitch"]
#    Run Keyword If    '${text}'=='${type}'    点击    //*[contains(text(),'${type}')]    ELSE    鼠标悬停    //img[@class='new-logo']
    ${status}    Run Keyword And Return Status    当前页面可见    //body[@class='ath-default-theme']    10
    ${title}    获取元素字符    //li[@class='ath-tab-menus-li ng-star-inserted ath-tab-menus-left-active']
    IF   '${status}'=='True' and '${type}'=='经典门户'
        鼠标悬停    //i[@class='icon font-entrance iconrenyuan1']    10
        IF    '${title}'=="首頁"
            点击    //*[contains(text(),'經典門戶')]
        ELSE
            点击    //*[contains(text(),'经典门户')]
        END
        当前页面可见    //body[@class='ath-default-theme tradition-portal']
    ELSE IF    '${status}'=='False' and '${type}'=='经典门户'
        log    当前已经是经典门户
    ELSE IF    '${status}'=='Ture' and '${type}'=='极简门户'
        Log    当前已经是极简门户
    ELSE IF    '${status}'=='False' and '${type}'=='极简门户'
        鼠标悬停    //i[@class='icon font-entrance iconrenyuan1']    10
        IF    '${title}'=="首頁"
            点击    //*[contains(text(),'極簡門戶')]
        ELSE
            点击    //*[contains(text(),'极简门户')]
        END
    END

发送方式
    [Arguments]    ${name}
    点击    //ath-select[@id='selectTransmit']
    Sleep    4
    点击    //span[contains(text(),'两者同时提醒')]

发送方式设定-简体
    [Arguments]    ${name}
    鼠标悬停   //i[contains(@class,'icon font-entrance iconrenyuan1')]
    Sleep    1
    点击    //*[contains(text(),'设置')]
    Sleep    1
    点击    //span[contains(text(),'个性化设定')]
    Sleep    1
    点击    //ath-select[@id='selectTransmit']
    Sleep    4
    点击    //span[@class='option-item-label'][contains(text(),'${name}')]
    sleep    5

发送方式设定-繁体
    [Arguments]    ${name}
    鼠标悬停   //i[contains(@class,'icon font-entrance iconrenyuan1')]
    Sleep    1
    点击    //*[contains(text(),'設定')]
    Sleep    1
    点击    //span[contains(text(),'個性化設定')]
    Sleep    1
    点击    //ath-select[@id='selectTransmit']
    Sleep    4
    点击    //span[@class='option-item-label'][contains(text(),'${name}')]
    sleep    6

执行人代理他人
    [Arguments]    ${agent}
    点击    //div[contains(@class,'ag-cell ag-cell-not-inline-editing ag-cell-normal-height ag-cell-wrap-text cell-not-disable ag-cell-focus')]//div[contains(@class,'ag-cell-wrapper')]
    点击    //span[contains(text(),'')]

个性化设置下拉
    点击    //app-main-toolbar[@class="ng-star-inserted"]/div[2]/i[2]

不设置代理
    点击    //a[contains(text(),'重新设置')]
    Sleep    3
    点击    //span[contains(text(),'确定')]

切租户
    [Arguments]    ${tenantA}    ${tenantB}
    Mouse Over    //span[@class='title'][contains(text(),'${tenantA}')]
    点击    //span[contains(text(),'${tenantB}')]
    Sleep    5

一级菜单校验
    当前页面可见    //div[contains(text(),'首页')]
    点击    //div[@class='title ellipsis ng-star-inserted'][contains(text(),'待办')]
    当前页面可见    //li[@class='ath-tab-menus-li ng-star-inserted ath-tab-menus-active'][contains(text(),'待办')]
    当前页面可见    //div[@class='title ellipsis ng-star-inserted'][contains(text(),'常用')]
    点击    //div[@class='title ellipsis ng-star-inserted'][contains(text(),'行事历')]
    当前页面可见    //li[@class='ath-tab-menus-li ng-star-inserted ath-tab-menus-active'][contains(text(),'行事历')]
    点击    //div[contains(text(),'发起项目')]
    当前页面可见    //li[@class='ath-tab-menus-li ng-star-inserted ath-tab-menus-active'][contains(text(),'发起项目')]
    点击    //div[contains(text(),'业务数据录入')]
    当前页面可见    //li[@class='ath-tab-menus-li ng-star-inserted ath-tab-menus-active'][contains(text(),'业务数据录入')]
    点击    //div[@class='title ellipsis ng-star-inserted'][contains(text(),'报表')]
    当前页面可见    //li[@class='ath-tab-menus-li ng-star-inserted ath-tab-menus-active'][contains(text(),'报表')]
    当前页面可见    //div[contains(text(),'数据查询')]
    当前页面可见    //div[contains(text(),'管理模组')]
#    当前页面可见    //div[contains(text(),'导入导出中心')]
    当前页面可见    //div[contains(text(),'用户工具')]
#    IF    '${ENV}' == 'huawei.prod' or '${ENV}' == 'microsoft.prod'
#        当前页面可见    //div[contains(text(),'数字员工看板')]
#    END
    当前页面可见    //div[contains(text(),'财报管理')]
    点击    //div[@class='title ellipsis ng-star-inserted'][contains(text(),'收藏')]
    当前页面可见    //li[@class='ath-tab-menus-li ng-star-inserted ath-tab-menus-active'][contains(text(),'收藏')]

二级菜单校验
    点击    //div[contains(text(),'数据查询')]
    当前页面可见    //div[contains(text(),'进度查询')]
    当前页面可见    //div[contains(text(),'历史项目/任务')]
    点击    //div[contains(text(),'管理模组')]
    当前页面可见    //div[contains(text(),'伙伴授权管理')]
    当前页面可见    //div[contains(text(),'需辅助名单管理')]
    当前页面可见    //div[contains(text(),'传送方式管理')]
    当前页面可见    //div[contains(text(),'数据来源设定')]
#    点击    //div[contains(text(),'导入导出中心')]
#    当前页面可见    //div[contains(text(),'导入记录')]
#    当前页面可见    //div[contains(text(),'导出记录')]
    点击    //div[contains(text(),'用户工具')]
    当前页面可见    //div[contains(text(),'交付设计器')]
    当前页面可见    //div[contains(text(),'邮件管理')]

    
