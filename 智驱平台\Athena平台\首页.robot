*** Settings ***
Documentation     黄蕾
Suite Setup       Run Keywords    环境设定
Test Teardown     Run Keywords    错误处理    AND    关闭浏览器
Resource          ../关键字/业务关键字/智能入口.robot
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/业务关键字/首页.robot
Resource          ../关键字/业务关键字/Athena平台.robot

*** Variables ***
${username}       TestAthenaAutoTestAi001
${password}       TestAthenaAutoTestAi001

*** Test Cases ***
常用设置-个人代理人设置
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${meuusername}    Set Variable If    '${ENV}'=='huawei.test'    测试环境自动化测试小AI入口    '${ENV}'=='huawei.prod'    生产华为环境自动化测试小AI平台    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试小AI平台    '${ENV}'=='muihuawei.test'    测试环境自动化测试小AI平台
    登录ATHENA平台    ${username}    ${password}
    用户下拉菜单展示    ${meuusername}
    常用设置左侧菜单栏    设置
    设置代理人
    代理变更历程展示    代理变更历程    新增
    取消代理人设置
    [Teardown]    Run Keywords    关闭浏览器

首页全局搜索
    [Documentation]    陈金明
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${key}    Set Variable If    '${ENV}'=='huawei.test'    项目PCC发起    '${ENV}'=='huawei.prod'    搜索测试1012    '${ENV}'=='microsoft.prod'    搜索测试1012
    ${work}    Set Variable If    '${ENV}'=='huawei.test'    签核    '${ENV}'=='huawei.prod'    搜索测试1012手动任务    '${ENV}'=='microsoft.prod'    搜索测试1012手动任务
    登录ATHENA平台    ${username}    ${password}
    项目/任务搜索    全部    ${key}    ${key}
    项目/任务搜索    项目    ${key}    ${key}
    项目/任务搜索    任务    ${work}    ${work}
    搜索    友情链接应用    鼎捷云控制台    鼎捷云控制台
    鼎捷云打开判断
    发起项目/基础资料/报表搜索    发起项目    五要素    五要素
    发起项目/基础资料/报表搜索    业务数据录入    项目大事记    项目大事记
    发起项目/基础资料/报表搜索    业务数据录入    维护工作历信息        维护工作历信息
    发起项目/基础资料/报表搜索    报表    异常明细检讨    异常明细检讨
    #    全局搜索切换TAB    全部    项目    项目    业务数据录入    报表
    [Teardown]    Run Keywords    关闭浏览器

友情链接
    [Documentation]    陈金明
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    友情链接
    [Teardown]    Run Keywords    关闭浏览器
