*** Settings ***
Suite Setup       Run Keywords    环境设定
Suite Teardown    Run Keywords    关闭浏览器
Library           SeleniumLibrary
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/业务关键字/Athena平台控件.robot
Resource          ../关键字/业务关键字/智能入口.robot
Resource          ../关键字/业务关键字/Athena平台.robot

*** Variables ***

*** Test Cases ***
待办页面检查
    [Documentation]    顾冬冬
    ${username}    Set Variable If    '${ENV}'=='private.test'    default
    ${password}    Set Variable If    '${ENV}'=='private.test'    1qaz@WSX
    登录Athena平台    ${username}    ${password}
    sleep    5
    点击    //div[contains(text(),'待办')]
    sleep    5
    点击    //div[contains(text(),'我的任务')]
    当前页面包含元素    xpath=//*[contains(text(),'搜索')]
    当前页面包含元素    xpath=//*[contains(text(),'分组')]
    当前页面包含元素    xpath=//*[contains(text(),'筛选')]
    当前页面包含元素    xpath=//*[contains(text(),'排序')]
    当前页面包含元素    xpath=//*[contains(text(),'显示')]
    当前页面包含元素    xpath=//*[contains(text(),'列表')]
    当前页面包含元素    xpath=//*[contains(text(),'更多')]
    ${task_element_exists}    判断元素是否可见    xpath=//div[@class='todo-card-item-container ng-star-inserted']
    Run Keyword If    ${task_element_exists}    点击    //div[@class='todo-card-item-container ng-star-inserted']
    Run Keyword If    ${task_element_exists}    sleep    3
    Run Keyword If    ${task_element_exists}    当前页面包含元素    xpath=//div[@id="task-detail-body"]/div
    Run Keyword If    ${task_element_exists}    点击    //li[contains(text(),'项目/任务详情')]/i
    点击    //div[contains(text(),'我的项目')]
    sleep    5
    ${project_element_exists}    判断元素是否可见    xpath=//div[@class='todo-card-item-container ng-star-inserted']
    Run Keyword If    ${project_element_exists}    点击    //div[@class='todo-card-item-container ng-star-inserted']
    Run Keyword If    ${project_element_exists}    sleep    3
    Run Keyword If    ${project_element_exists}    当前页面包含元素    xpath=//span[contains(text(),'数据来源：')]
    Run Keyword If    ${project_element_exists}    点击    //li[contains(text(),'项目/任务详情')]/i
    点击    //div[contains(text(),'团队任务')]
    sleep    5
    ${team_task_element_exists}    判断元素是否可见    xpath=//div[@class='todo-card-item-container ng-star-inserted']
    Run Keyword If    ${team_task_element_exists}    点击    //div[@class='todo-card-item-container ng-star-inserted']
    Run Keyword If    ${team_task_element_exists}    sleep    3
    Run Keyword If    ${team_task_element_exists}    当前页面包含元素    xpath=//div[@id="task-detail-body"]/div
    Run Keyword If    ${team_task_element_exists}    点击    //li[contains(text(),'项目/任务详情')]/i
    点击    //div[contains(text(),'团队项目')]
    ${team_project_element_exists}    判断元素是否可见    xpath=//div[@class='todo-card-item-container ng-star-inserted']
    Run Keyword If    ${team_project_element_exists}    点击    //div[@class='todo-card-item-container ng-star-inserted']
    Run Keyword If    ${team_project_element_exists}    sleep    3
    Run Keyword If    ${team_project_element_exists}    当前页面包含元素    xpath=//span[contains(text(),'数据来源：')]
    Run Keyword If    ${team_project_element_exists}    点击    //li[contains(text(),'项目/任务详情')]/i
    sleep    5
    点击    //div[contains(text(),'三方待办')]
    当前页面包含元素    xpath=//*[contains(text(),'暂无数据')]
