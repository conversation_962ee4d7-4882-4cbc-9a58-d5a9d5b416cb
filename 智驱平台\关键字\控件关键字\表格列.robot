*** Settings ***
Library           SeleniumLibrary
Resource          ../系统关键字/web.robot
Resource          ../../元素/Athena平台元素.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../业务关键字/作业授权.robot
Resource          ../系统关键字/接口.robot

*** Keywords ***
操作列删除
     ${table_element_exists}    判断元素是否可见    xpath=//*[@col-id='test_emy_id']/div/span/cell-renderer/div/div/div/app-dynamic-input-display[@title!='']/ath-control-wrapper/div
     Run Keyword If    ${table_element_exists}   点击  //span[contains(text(),'刪除')]
     Run Keyword If    ${table_element_exists}   当前页面可见   //button/span[contains(text(),'确定')]
     Run Keyword If    ${table_element_exists}   点击  //button/span[contains(text(),'确定')]
     点击   //span[contains(text(),'新增')]
     当前页面可见     //div[contains(text(),'新增')]
     点击  //input[@name='test_emy_id']
     输入  //input[@name='test_emy_id']  20241111
     Press Keys   //input[@name='test_emy_id']   ENTER
     点击   //button/span[contains(text(),'新建')]
     鼠标悬停  //div[contains(text(),' 表格_双档作业测试 ')]
     点击  //div[contains(text(),' 表格_双档作业测试 ')]
     # 刷新页面
     # Sleep  10
     元素存在则点击    //span[contains(text(),'刪除')]
     当前页面可见    //button/span[contains(text(),'确定')]
     点击    //button/span[contains(text(),'确定')]
     Sleep     3
     当前页面不可见元素    //span[contains(text(),'刪除')]

操作列复制
     Element Should Be Visible   //button/span[contains(text(),'复制')]
     Mouse Over  //button/span[contains(text(),'复制')]
     点击  //button/span[contains(text(),'复制')]
     Element Should Be Visible     //span[@class='error-text-tip sync-errors-tip ng-star-inserted']
     Mouse Over  //div[@class='control-error-visible ng-star-inserted']/app-dynamic-input-display
     点击  //div[@class='control-error-visible ng-star-inserted']/app-dynamic-input-display
     Sleep  3
     Mouse Over  //i[@class="anticon ant-input-number-handler-down-inner"]
     点击  //i[@class="anticon ant-input-number-handler-down-inner"]
     Press Keys   //input[@placeholder='数字输入']   ENTER
     点击  //input[@name='test_sign_case_id']
     输入  //input[@name='test_sign_case_id']  1000
     #输入  //input[@placeholder='数字输入']  1008611
     Sleep  3
     当前页面可见   //button/span[contains(text(),'保存')]  3
     点击   //button/span[contains(text(),'保存')]
     Sleep  10
     当前页面不可见元素   //div[@class='ath-tag-inner']/span[text()='未保存']   3
     
操作列condition
     前端筛选  test_num     123
     当前页面可见   //dynamic-button-group/button[@disabled='true']   3
     重置筛选  test_num     123
     前端筛选  test_num     20240601
     当前页面可见    //dynamic-button-group/button[contains(@class,'ath-btn-operation')][1]    3

#操作列hidden
#操作列hidden功能做丢了，bug单已转需求，待需求上线后补充自动化场景









   



