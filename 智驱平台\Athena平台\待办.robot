*** Settings ***
Documentation     质量组
Suite Setup       Run Keywords    环境设定
Test Teardown     Run Keywords    错误处理    关闭浏览器
Resource          ../关键字/业务关键字/智能入口.robot
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/业务关键字/待办.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/顺序签核.robot
Resource          ../关键字/业务关键字/PCC.robot

*** Test Cases ***
待办-任务项目
    ${username}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${password}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${task}    Set Variable If    '${ENV}'=='pressure'    15062205158    '${ENV}'=='paas'    15062205158    '${ENV}'=='huawei.test'    1735802957    '${ENV}'=='huawei.prod'    1735264018    '${ENV}'=='microsoft.prod'    1735196439
    ${tasktype}    Set Variable If    '${ENV}'=='pressure'    职能签核    '${ENV}'=='paas'    采购签核    '${ENV}'=='huawei.test'    职能签核    '${ENV}'=='huawei.prod'    手动任务签核    '${ENV}'=='microsoft.prod'    手动任务签核
    ${tenant}    Set Variable If    '${ENV}'=='pressure'     自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'     自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境    
   
    Set Global Variable    ${username}
    Set Global Variable    ${password}
    登录ATHENA平台    ${username}    ${password}    tenant=${tenant}
    我的任务分组
    我的任务排序
    任务运营单元
    任务卡筛选
    Comment    任务显示    ${task}
    #我的项目
    我的项目分组
    我的项目筛选
    我的项目排序
    #团队任务
    团队任务分组
    团队任务排序
    团队任务筛选    ${tasktype}
    团队任务显示
    #团队项目
    团队项目分组
    团队项目筛选
    团队项目排序
    [Teardown]    Run Keywords    关闭浏览器

#bugid 168952
#仅覆盖测试区
#cjm
工时回报任务图标显示
    [Tags]    工时回报任务图标显示
    Comment    #当责者账号
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    pur002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    pur002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${tenant}    Set Variable If    '${ENV}'=='pressure'     自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    E10_6003版本测试租户    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    登录Athena平台    ${username}    ${password}    tenant=${tenant}
    跳转网页    /todo/task
    任务/项目名称搜索    HYLX-202507030002
    
    ${element_exists}    判断元素是否可见    //div[@class='reshuffle-state ng-star-inserted']
    IF    '${element_exists}'=='False'
        点击卡片    HYLX-202507030002
        人员上线
    END
    跳转网页    /todo/task
    任务/项目名称搜索    HYLX-202507030002
    当前页面可见    //div[@class='reshuffle-state ng-star-inserted']
    [Teardown]    Run Keywords    关闭浏览器