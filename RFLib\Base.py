#!/usr/local/bin/python3
# -*- coding: utf8 -*-
# Author: cjm
import json
import pyautogui
import pyperclip
import time
import platform
from datetime import datetime
from selenium import webdriver
from robot.libraries.BuiltIn import BuiltIn
# from seleniumwire import webdriver
# from selenium.webdriver.common.by import By
# from selenium.webdriver.chrome.options import Options
# import time
# from urllib.parse import urljoin  # 新增URL处理模块



class Base:
    ROBOT_LIBRARY_SCOPE = 'TEST CASE'
    ROBOT_LIBRARY_VERSION = '0.1'

    def uploadFile(self, filepath):
        system_type = platform.system()
        if system_type == 'Windows':
            pyperclip.copy(filepath)
            time.sleep(2)
            pyautogui.hotkey('ctrl', 'v')
            pyautogui.press('enter', presses=2)
        elif system_type == 'Darwin':
            pyperclip.copy(filepath)
            time.sleep(2)
            pyautogui.hotkey('Command', 'Shift', 'G')
            pyautogui.hotkey('ctrl', 'v')
            pyautogui.press('enter', presses=2)
        elif system_type == 'Linux':
            return '未做适配'
        else:
            return 'Unknown OS'

    def getSystemType(self):
        system_type = platform.system()
        return system_type

    # def enteText(self, text_to_type):
    #     for char in text_to_type:
    #         pyautogui.typewrite(char)
    #         time.sleep(0.1)
    # def sendKey(self,key):
    #     pyautogui.press(key)

    def json_handle(self, content):
        # 定义错误编码，使用集合存储，提高查找效率
        # errorCode = {100, 101, 400, 401, 403, 404, 405, 408, 410, 413, 414, 415, 422, 429, 500, 501, 502, 503, 504, 505,
        #              506, 507, 508}
        errorCode = {405, 408, 410, 413, 414, 415, 422, 429, 500, 501, 502, 503, 504, 505,
                     506, 507, 508}
        # 解析 JSON 数据
        try:
            parsed_data = json.loads(content)
        except json.JSONDecodeError:
            print("Invalid JSON content")
            return []
        # 使用列表推导式提取错误条目，将结果存储在 result 列表中
        result = [{"url": entry['request']['url'], "status": entry['response']['status']}
                  for entry in parsed_data.get('log', {}).get('entries', [])
                  if entry.get('response', {}).get('status') in errorCode]
        return result

    def get_clipboard_data(self):
        """获取剪贴板中的数据"""
        return pyperclip.paste()
    #
    # def test(self):
    #     test = BuiltIn().get_library_instance('SeleniumLibrary')
    #     test.input_text('xpath=//input[@name="userId"]', '测试')
    #     time.sleep(111)
    #
    #
    # def get_token(self,url, token_url, username, password):
    #     # 设置Chrome无头模式
    #     options = Options()
    #     options.add_argument("--headless")
    #     # 初始化WebDriver
    #     driver = webdriver.Chrome(options=options)
    #     # 打开指定网页，将URL替换成你实际要操作的页面地址
    #     login_url = urljoin(url, "/login")
    #     print(f"Accessing login page: {login_url}")  # 调试日志
    #     driver.get(login_url)
    #     # 通过name属性定位用户ID文本框，并输入内容
    #     user_id_textbox = driver.find_element(By.NAME, "userId")
    #     user_id_textbox.send_keys(username)
    #     # 通过name属性定位密码文本框，并输入内容
    #     password_textbox = driver.find_element(By.NAME, "password")
    #     password_textbox.send_keys(password)
    #     # 通过class属性定位按钮并点击，注意这里用By.CLASS_NAME时如果class有多个值，要使用其中一个唯一能区分的部分
    #     # 通过xpath定位按钮元素
    #     button = driver.find_element(By.XPATH, "//button[@class='ant-btn ant-btn-primary']")
    #     # 点击定位到的按钮
    #     button.click()
    #     time.sleep(15)
    #     target_url = urljoin(token_url, "/knowledgegraph/restful/service/knowledgegraph/package/tenantAll")
    #
    #     for request in driver.requests:
    #         if request.url == target_url:
    #             target_request = request
    #             reponse = request.response
    #             break
    #
    #     print(reponse)
    #     request_headers = target_request.headers
    #     token = request_headers.get('token', None)
    #     return token
    #     driver.quit()




    def datetime_format(self, date):
        # 定义原始日期时间字符串的格式
        original_format = '%Y年%m月%d日 %H:%M'
        # 定义目标日期时间字符串的格式
        target_format = '%Y-%m-%d %H:%M'
        # 将字符串解析为datetime对象
        dt = datetime.strptime(date, original_format)
        # 将datetime对象格式化为目标字符串
        formatted_date = dt.strftime(target_format)
        return  formatted_date



if __name__ == "__main__":
    pass
