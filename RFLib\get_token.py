from seleniumwire import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
import time

# 设置Chrome无头模式
options = Options()
options.add_argument("--headless")
# 初始化WebDriver
driver = webdriver.Chrome(options=options)
# 打开指定网页，将URL替换成你实际要操作的页面地址
driver.get("https://athena.digiwincloud.com.cn/login")
# 通过name属性定位用户ID文本框，并输入内容
user_id_textbox = driver.find_element(By.NAME, "userId")
user_id_textbox.send_keys("<EMAIL>")
# 通过name属性定位密码文本框，并输入内容
password_textbox = driver.find_element(By.NAME, "password")
password_textbox.send_keys("@cjm820412000")
# 通过class属性定位按钮并点击，注意这里用By.CLASS_NAME时如果class有多个值，要使用其中一个唯一能区分的部分
# 通过xpath定位按钮元素
button = driver.find_element(By.XPATH, "//button[@class='ant-btn ant-btn-primary']")
# 点击定位到的按钮
button.click()
time.sleep(15)
target_url = "https://isv-gateway.apps.digiwincloud.com.cn/knowledgegraph/restful/service/knowledgegraph/package/tenantAll"
for request in driver.requests:
    if request.url == target_url:
        target_request = request
        reponse=request.response
        break

print(reponse)
request_headers = target_request.headers
token = request_headers.get('token', None)
print(token)
if token:
    print(f"在请求头中找到token: {token}")
else:
    print("在请求头中未找到token")
# 操作完成后关闭浏览器
driver.quit()