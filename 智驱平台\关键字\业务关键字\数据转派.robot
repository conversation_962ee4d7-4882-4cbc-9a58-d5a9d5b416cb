*** Settings ***
Library           SeleniumLibrary
Library    DateTime
Resource          ../../元素/开发平台元素.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/web.robot
Resource          公共方法.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../系统关键字/web.robot
Resource          Athena平台控件.robot
Resource          Athena平台.robot
Resource          首页.robot
Resource          待办.robot
Resource          ../业务关键字/PCC.robot


*** Keywords ***
数据转派/提交-任务名称搜索
    [Arguments]    ${name}
    跳转到待办页    /todo/task
    视图模式设定    卡片
    清空筛选
    ${element_exists}    判断元素是否可见    //span[contains(text(),'搜索')]    3
    Run Keyword If    ${element_exists}    点击    //span[contains(text(),'搜索')]
    ...    ELSE    Log    Element does not exist.
    输入    //input[@placeholder='可用空格分隔关键词']    ${name}
    数据转派/提交-待办搜索轮询    ${name}


数据转派/提交-待办搜索轮询
    [Arguments]    ${tagname}
    点击    //span[@type='addon']
    FOR    ${index}    IN RANGE    6
        ${element_exists}    Run Keyword And Return Status    当前页面可见    //span[contains(text(),"${tagname}")]    10
        Run Keyword If    ${element_exists}    Exit For Loop    ELSE    刷新页面
    END
    

进入任务卡（数据转派）详情
    [Arguments]    ${date}
    Sleep    3
    点击    //*[contains(text(),'New')]
    
    
选中所有待转派数据-转派
    [Arguments]    ${assigname}    ${opinion}
    Sleep    3
    ${dataselect}    按顺序获取元素    //span[@class='ant-checkbox-inner']    0
    点击    ${dataselect}    
    Sleep    3
    点击    //span[contains(text(),'任务转派')]
    Sleep    2
    输入    //*[@athtooltipplacement="bottom"]//input[@class='ant-select-selection-search-input ng-untouched ng-pristine ng-valid']    ${assigname}
    Sleep    2
    点击    //span[@class='search-Highlight']
    Sleep    3
    输入    //textarea[@id='opinion']    ${opinion}
    Sleep    2
    点击    //span[contains(text(),"确定")]
    Sleep    2
    点击    //span[contains(text(),"确定")]


选中所有待办数据-提交
    Sleep    3
    ${datasubmit}    按顺序获取元素    //span[@class='ant-checkbox-inner']    0
    点击    ${datasubmit}
    Sleep    2
    点击    //span[contains(text(),"确认续签")]
    Sleep    2
    点击    //span[contains(text(),"确定")]


审批续签合同任务提交
    [Arguments]    ${taskname}
    Sleep    3
    数据转派/提交-任务名称搜索    ${taskname}
    进入任务卡（数据转派）详情
    点击    //span[contains(text(),"同意")]
    Sleep    3
    点击    //span[contains(text(),"确定")]


调用isv接口发起侦测项目
    [Arguments]    ${data}
    POST    ${isv_url}    /deliverydesigner/application/executeMonito    ${data}



获取明天日期
    ${current_date}=    Get Current Date    result_format=%Y-%m-%d
    ${tomorrow_date}=    Add Time To Date    ${current_date}    1 day    result_format=%Y-%m-%d
    ${formatted_date}=    Convert Date    ${tomorrow_date}    result_format=%m/%d
    RETURN    ${formatted_date}
    Log    ${formatted_date}