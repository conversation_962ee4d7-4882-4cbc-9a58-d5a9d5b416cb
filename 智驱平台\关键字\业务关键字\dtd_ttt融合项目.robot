*** Settings ***
Library           SeleniumLibrary
Resource          ../系统关键字/web.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../系统关键字/接口.robot
Resource          Athena平台控件.robot
Resource          ../../元素/开发平台元素.robot
Resource          ../../配置/域名.robot
Resource          Athena平台.robot

*** Keywords ***
融合任务卡提交
    [Arguments]    ${story_id}
    判断后跳转到目标页面    /todo/task
    点击    //div[contains(text(),'我的任务')]
    任务/项目名称搜索    ${story_id}
    点击  //span[contains(text(),'${story_id}')]
    #dtd卡点击提交
    点击  //span[contains(text(),'提交')]
    点击  //span[contains(text(),'确定')]
    关闭标签
融合任务卡同意
    [Arguments]    ${story_id}
    判断后跳转到目标页面    /todo/task
    点击    //div[contains(text(),'我的任务')]
    #点击    //div[contains(text(),'待办')]
    任务/项目名称搜索    ${story_id}
    点击  //span[contains(text(),'story_id：${story_id}')]
    点击  //*[@id="task-content-content-id"]//button[1]
    点击  (//span[contains(text(),'同意')])[1]
    点击  //span[contains(text(),'确定')]
    关闭标签

融合任务卡终止
    [Arguments]    ${story_id}
    判断后跳转到目标页面    /todo/task
    点击    //div[contains(text(),'我的任务')]
    任务/项目名称搜索    ${story_id}
    点击  //span[contains(text(),'${story_id}')]
    #dtd卡点击提交
    点击  //span[contains(text(),'终止任务')]
    点击  //span[contains(text(),'确定')]
    关闭标签

融合项目详情里程碑切换
    [Arguments]    ${story_id}   ${text}
    跳转网页    /todo/task
    点击    //div[contains(text(),'我的项目')]
    #搜索    ${taskname}
    任务/项目名称搜索    ${story_id}
    点击    //span[contains(text(),'story_id：${story_id}')]
    点击    //div[contains(text(),'dtd人工卡')]
    当前页面可见字符    ${text}
    点击    //div[contains(text(),'人工签核1')]
    当前页面可见字符    ${text}
    点击    //div[contains(text(),'人工签核2')]
    当前页面可见字符    ${text}
    点击    //div[contains(text(),'签核任务')]
    当前页面可见字符    ${text}
    关闭标签


融合项目被分享卡
     [Arguments]    ${story_id}   ${text}
    跳转网页    /todo/project
    任务/项目名称搜索    ${story_id}
    鼠标悬停    //span[contains(text(),'${story_id}')]
    当前页面可见字符    分享人
    Sleep    5
    #检查被分享的卡里程碑数据
    融合项目详情里程碑切换  ${story_id}    ${text}


















