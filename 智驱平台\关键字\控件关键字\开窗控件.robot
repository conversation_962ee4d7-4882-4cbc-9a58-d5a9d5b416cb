*** Settings ***
Library           SeleniumLibrary
Resource          ../系统关键字/web.robot
Resource          ../../元素/Athena平台元素.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../系统关键字/接口.robot

*** Keywords ***
单选开窗
    [Arguments]    ${searchkey}
    当前页面可见    //ath-open-window
    点击    //div[@class='ath-input-content-box ng-star-inserted']
    输入    //div[@class='ath-input-content-box ng-star-inserted']/input    ${searchkey}
    Press Keys    //div[@class='ath-input-content-box ng-star-inserted']/input      ENTER
    Sleep    5
    鼠标悬停    //ant-label/span[contains(text(),'${searchkey}')]
    点击      //ant-label/span[contains(text(),'${searchkey}')]
    当前页面可见    //button[@class='ath-btn ant-btn ant-btn-primary ng-star-inserted active']
    点击      //button[@class='ath-btn ant-btn ant-btn-primary ng-star-inserted active']

单选开窗1
    [Arguments]    ${searchkey}
    当前页面可见    //ath-open-window
    点击    //div[@class='ath-input-content-box ng-star-inserted']
    输入    //div[@class='ath-input-content-box ng-star-inserted']/input    ${searchkey}
    Press Keys    //div[@class='ath-input-content-box ng-star-inserted']/input      ENTER
    Sleep    5
    ${row_id}    获取元素属性值    //span[@title='${searchkey}']/ancestor::div[@role="row"]    row-id
    ${int_num}    Evaluate    int(${row_id})
    Log    ${int_num+1}
    点击    (//label[@class='ant-radio-wrapper ath-radio-wrapper ng-untouched ng-pristine ng-valid ng-star-inserted']//span[@class='ant-radio'])[${int_num+1}]
    #鼠标悬停    //ant-label/span[contains(text(),'${searchkey}')]
    #点击      //ant-label/span[contains(text(),'${searchkey}')]
    当前页面可见    //button[@class='ath-btn ant-btn ant-btn-primary ng-star-inserted active']
    点击      //button[@class='ath-btn ant-btn ant-btn-primary ng-star-inserted active']

打开单选开窗
    鼠标悬停    //div[contains(@class,'putSearch')]
    当前页面可见   //dynamic-operation-editor/div/div/div/span[@class='edit hidden-icon']/i    3
    点击   //dynamic-operation-editor/div/div/div/span[@class='edit hidden-icon']/i
    Sleep    3
    当前页面可见   //ath-open-window-table/div[@class='table-and-pagination']   3

搜索开窗数据
    [Arguments]    ${searchkey}
    元素存在则点击     //ath-input-group/div/section/div/input   3
    输入    //ath-input-group/div/section/div/input    ${searchkey}
    Press Keys  //ath-input-group/div/section/div/input    ENTER
    Sleep   3
    当前页面可见   //div[@row-index="0"]/div/div/span/dy-open-window-table-cell-renderer/div/div/dynamic-ant-label/ant-label/span[contains(@title,'${searchkey}')]   3

选择单选开窗数据
    [Arguments]    ${searchkey}
    搜索开窗数据    ${searchkey}
    元素存在则点击   //dynamic-ant-label/ant-label/span[contains(@title,'${searchkey}')]    3
    当前页面可见   //span[@class='ant-radio ant-radio-checked']   3
    提交开窗数据
    当前页面可见   //dynamic-operation-editor/div/span[contains(@title,'${searchkey}')]    3
    
选择多选开窗数据
    [Arguments]    ${searchkey}     ${owtype}
    搜索开窗数据  ${searchkey}
    点击     //ath-open-window-table/*//ath-patch-header-renderer/label/span[@class='ant-checkbox']
    当前页面可见     //ath-open-window-selected-list/div/cdk-virtual-scroll-viewport/div[@class='cdk-virtual-scroll-content-wrapper']    3
    鼠标悬停     //div[@class='cdk-virtual-scroll-content-wrapper']/div[1]/div[@class='item-detail']  
    当前页面可见    //div[@class="cdk-overlay-connected-position-bounding-box"]/div[@class='cdk-overlay-pane']   
    提交开窗数据
    Run Keyword If    '${owtype}'=='tableow'    当前页面可见   //dynamic-form-operation-editor/div/div/div[1]/div[contains(text(),'${searchkey}')]    3
    Run Keyword If    '${owtype}'=='formlistow'  当前页面可见      //div[@class='tag-list-item common-item ng-star-inserted'][1]/div[contains(text(),'${searchkey}')]   3
    Run Keyword If    '${owtype}'=='rowow'  当前页面可见   //div[@row-index='0']/div[@aria-colindex='1']/div/span/cell-renderer/following::input[contains(@title,'A001')]   3

回显数据检查
    [Arguments]    ${searchkey}
    当前页面可见   //dynamic-operation-editor/div/span[contains(@title,'${searchkey}')]   3
    打开单选开窗
    开窗分页检查
    滑动元素到可见区域  //span[@class='ant-radio ant-radio-checked']   3
    当前页面可见   //span[@class='ant-radio ant-radio-checked']   3
    关闭开窗窗口

表单开窗回显数据检查
    [Arguments]    ${searchkey}
    当前页面可见   //dynamic-operation-editor/div/span[contains(@title,'${searchkey}')]   3
    打开表单开窗    dynamic-operation-editor
    开窗分页检查
    滑动元素到可见区域  //span[@class='ant-radio ant-radio-checked']   3
    当前页面可见   //span[@class='ant-radio ant-radio-checked']   3
    关闭开窗窗口

多选开窗回显数据检查
    [Arguments]    ${searchkey}     ${owkey}
    当前页面可见    //dynamic-form-operation-editor/div/div/div[1]/div[contains(text(),'${searchkey}')]    3
    鼠标悬停    //div[@col-id="${owkey}"]/div[@class='ag-cell-wrapper']
    当前页面可见    //dynamic-form-operation-editor/div/div/div/div[@class='dynamic-form-operation-icon ng-star-inserted']/i[@class='anticon iconfont hidden-icon']    3
    点击    //dynamic-form-operation-editor/div/div/div/div[@class='dynamic-form-operation-icon ng-star-inserted']/i[@class='anticon iconfont hidden-icon']
    当前页面可见     //ath-open-window-selected-list/div/cdk-virtual-scroll-viewport/div[@class='cdk-virtual-scroll-content-wrapper']    3
    鼠标悬停     //div[@class='cdk-virtual-scroll-content-wrapper']/div[1]/div[@class='item-detail']  
    当前页面可见    //div[@class="cdk-overlay-connected-position-bounding-box"]/div[@class='cdk-overlay-pane']
    开窗分页检查
    关闭开窗窗口

清除回显数据
    [Arguments]    ${searchkey}     ${owtype}
    # 鼠标悬停    //div[@col-id="${owkey}"]/div[@class='ag-cell-wrapper']
    鼠标悬停    //div[contains(@class,'putSearch')]
    Run Keyword If    '${owtype}'=='dynamic-operation-editor'     当前页面可见    //dynamic-operation-editor/div/div/span[contains(@class,'name-btn--del')]/i    3
    Run Keyword If    '${owtype}'=='dynamic-operation-editor'     点击    //dynamic-operation-editor/div/div/span[contains(@class,'name-btn--del')]/i
    Run Keyword If    '${owtype}'=='dynamic-operation-editor'   当前页面不可见元素  //dynamic-operation-editor/div/span[contains(@title,'${searchkey}')]    3
    Run Keyword If    '${owtype}'=='dynamic-form-operation-editor'    当前页面可见    //dynamic-form-operation-editor/div/div/div/div[@class='dynamic-form-operation-icon icon-delete ng-star-inserted']/i
    Run Keyword If    '${owtype}'=='dynamic-form-operation-editor'    点击    //dynamic-form-operation-editor/div/div/div/div[@class='dynamic-form-operation-icon icon-delete ng-star-inserted']/i
    Run Keyword If    '${owtype}'=='dynamic-form-operation-editor'    当前页面不可见元素     //dynamic-form-operation-editor/div/div/div[1]/div[contains(text(),'${searchkey}')]

清除表单开窗回显数据
    [Arguments]    ${searchkey}     ${owtype}
    Run Keyword If    '${owtype}'=='dynamic-operation-editor'   鼠标悬停     //dynamic-operation-editor/div[contains(@class,'putSearch')]
    Run Keyword If    '${owtype}'=='dynamic-operation-editor'   当前页面可见     //dynamic-operation-editor/div/div/span[contains(@class,'name-btn--del')]/i    3
    Run Keyword If    '${owtype}'=='dynamic-operation-editor'   点击     //dynamic-operation-editor/div/div/span[contains(@class,'name-btn--del')]/i
    Run Keyword If    '${owtype}'=='dynamic-operation-editor'   当前页面不可见元素  //dynamic-operation-editor/div/span[contains(@title,'${searchkey}')]    3
    Run Keyword If    '${owtype}'=='dynamic-form-operation-editor'   鼠标悬停     //dynamic-form-operation-editor/div[contains(@class,'putSearch')]
    Run Keyword If    '${owtype}'=='dynamic-form-operation-editor'   当前页面可见     //div[@class='dynamic-form-operation-all-del ng-star-inserted']     3
    Run Keyword If    '${owtype}'=='dynamic-form-operation-editor'   点击     //div[@class='dynamic-form-operation-all-del ng-star-inserted']
    Run Keyword If    '${owtype}'=='dynamic-form-operation-editor'   当前页面不可见元素     //dynamic-form-operation-editor/div/div/div[1]/div[contains(text(),'${searchkey}')]

提交开窗数据
    元素存在则点击   //div[@class='ant-modal-footer']/button/span[contains(text(),'提交')]   3
    当前页面可见字符    操作成功

关闭开窗窗口
    元素存在则点击   //button/span[@class='ant-modal-close-x']/i    3
    当前页面不可见元素  //ath-open-window-table/div[@class='table-and-pagination']   3

开窗分页检查
    基础表格分页页码选择    20  10
    基础表格分页切换
    基础表格分页页码选择    10  40
    基础表格分页切换到页码   3

快捷输入开窗结果为空
    [Arguments]     ${searchkey}    ${owtype}
    元素存在则点击    //div[contains(@class,'putSearch')]    3
    Run Keyword If    '${owtype}'=='dynamic-operation-editor'   输入    //dynamic-operation-editor/div/input[@placeholder="case开窗关联表"]    ${searchkey}
    Run Keyword If    '${owtype}'=='dynamic-form-operation-editor'   输入   //dynamic-form-operation-editor/div/div/div/input[@placeholder="case开窗关联表"]    ${searchkey}
    Run Keyword If    '${owtype}'=='formlist-operation-editor'   输入   //dynamic-form-operation-editor/div/div/div/div/input[@placeholder="case开窗关联表"]    ${searchkey}
    Sleep   10
    当前页面可见    //div[@class='empty-formula empty-formula-simple ng-star-inserted']
    点击    //div[contains(@class,'putSearch')]
    当前页面不可见元素  //div[@class='empty-formula empty-formula-simple ng-star-inserted']
    

快捷输入开窗
    [Arguments]    ${searchkey}     ${owtype}
    元素存在则点击    //div[contains(@class,'putSearch')]    3
    #判断多选单选开窗
    Run Keyword If    '${owtype}'=='dynamic-operation-editor'  输入    //dynamic-operation-editor/div/input[@placeholder="case开窗关联表"]    ${searchkey}
    Run Keyword If    '${owtype}'=='dynamic-operation-editor'   Sleep  10
    Run Keyword If    '${owtype}'=='dynamic-operation-editor'   当前页面可见   //div[@row-index="0"]/div/div/span/dy-open-window-table-cell-renderer/div/div/dynamic-ant-label/ant-label/span[contains(@title,'${searchkey}')]
    Run Keyword If    '${owtype}'=='dynamic-operation-editor'   点击   //div[@row-index="0"]/div/div/span/dy-open-window-table-cell-renderer/div/div/dynamic-ant-label/ant-label/span[contains(@title,'${searchkey}')]
    Run Keyword If    '${owtype}'=='dynamic-operation-editor'   当前页面可见   //span[contains(@title,'${searchkey}')]
    Run Keyword If    '${owtype}'=='dynamic-form-operation-editor'     输入    //dynamic-form-operation-editor/div/div/div/input[@placeholder="case开窗关联表"]    ${searchkey}
    Run Keyword If    '${owtype}'=='dynamic-form-operation-editor'   当前页面可见    //div[@row-index="0"]/div/div/span/dy-open-window-table-cell-renderer/div/div/dynamic-ant-label/ant-label/span[contains(@title,'${searchkey}')]
    Run Keyword If    '${owtype}'=='dynamic-form-operation-editor'   点击   //ath-open-window-table/*//ath-patch-header-renderer/label/span[@class='ant-checkbox']
    Run Keyword If    '${owtype}'=='dynamic-form-operation-editor'   点击   //div[contains(@class,'putSearch')]
    Run Keyword If    '${owtype}'=='dynamic-form-operation-editor'   当前页面可见   //dynamic-form-operation-editor/div/div/div[1]/div[contains(text(),'${searchkey}')]
    #表单开窗快捷输入
    Run Keyword If    '${owtype}'=='formlist-operation-editor'     输入    //dynamic-form-operation-editor/div/div/div/div/input[@placeholder="case开窗关联表"]    ${searchkey}
    Run Keyword If    '${owtype}'=='formlist-operation-editor'   当前页面可见   //div[@row-index="0"]/div/div/span/dy-open-window-table-cell-renderer/div/div/dynamic-ant-label/ant-label/span[contains(@title,'${searchkey}')]
    Run Keyword If    '${owtype}'=='formlist-operation-editor'   点击   //ath-open-window-table/*//ath-patch-header-renderer/label/span[@class='ant-checkbox']
    Run Keyword If    '${owtype}'=='formlist-operation-editor'   点击   //div[contains(@class,'putSearch')]
    Run Keyword If    '${owtype}'=='formlist-operation-editor'   当前页面可见   //div[@class='dynamic-form-operation-edit']/div/div/div[contains(text(),'${searchkey}')]

打开多选开窗窗口
    [Arguments]    ${owkey}
    鼠标悬停   //div[@col-id="${owkey}"]/div[@class='ag-cell-wrapper']
    当前页面可见   //dynamic-form-operation-editor/div/div/div[@class='dynamic-form-operation-icon']   3
    点击   //dynamic-form-operation-editor/div/div/div[@class='dynamic-form-operation-icon']
    Sleep   3
    当前页面可见   //ath-open-window-table/div[@class='table-and-pagination multipleOpenWindow']   3

打开表单开窗
    [Arguments]    ${owtype}
    Run Keyword If    '${owtype}'=='dynamic-operation-editor'    点击   //dynamic-operation-editor/div/div/div/span[@class='edit']/i
    Sleep    3
    Run Keyword If    '${owtype}'=='dynamic-operation-editor'    当前页面可见   //ath-open-window-table/div[@class='table-and-pagination']   3
    Run Keyword If    '${owtype}'=='dynamic-form-operation-editor'    点击   //dynamic-form-operation-editor/div/div/div[@class='dynamic-form-operation-icon']/i
    Sleep    3
    Run Keyword If    '${owtype}'=='dynamic-form-operation-editor'   当前页面可见   //ath-open-window-table/div[@class='table-and-pagination multipleOpenWindow']   3

单选开窗默认选中第一条
    打开表单开窗    dynamic-operation-editor
    当前页面可见   //ath-patch-cell-renderer/div/label/span[@class='ant-radio ant-radio-checked']   3
    关闭开窗窗口

单选开窗根据输入关键字展示内容
    [Arguments]    ${keyword}
    当前页面可见   //dynamic-operation-editor/div/input[contains(@class,'ant-input')]   3
    点击   //dynamic-operation-editor/div/input[contains(@class,'ant-input')]
    输入    //dynamic-operation-editor/div/input[contains(@class,'ant-input')]     ${keyword}
    点击   //div[@class='create-project-des-box ng-star-inserted']
    Sleep   3
    点击   //dynamic-operation-editor/div[@class='editor show']/div/div/span[@class='edit']/i
    Sleep   10
    Run Keyword If    '${keyword}'=='null'  当前页面可见   //div/span[text()='暂无数据']   3
    ...    ELSE    当前页面可见   //div[@class='contain-explcit']   3

多选开窗根据输入关键字展示内容
    [Arguments]    ${keyword}
    当前页面可见   //dynamic-operation-editor/div/input[contains(@class,'ant-input')]   3
    点击   //dynamic-operation-editor/div/input[contains(@class,'ant-input')]
    输入    //dynamic-operation-editor/div/input[contains(@class,'ant-input')]    ${keyword}
    #点击   \ //div[@class='create-project-des-box ng-star-inserted']
    Sleep   3
    点击   //dynamic-operation-editor/div/div/div/span[@class='edit hidden-icon']/i
    Sleep   10
    Run Keyword If  '${keyword}'=='null'     当前页面可见   //div/span[text()='暂无数据']   3
    ...    ELSE    当前页面可见   //dy-open-window-table-cell-renderer/div/div/dynamic-ant-label/ant-label/span[@title='${keyword}']   3

选择condition单选开窗数据
    [Arguments]    ${searchkey}
    元素存在则点击   //div[@tabindex='-1']/div/span/ath-patch-cell-renderer/div/label/span[@class='ant-radio']   3
    当前页面可见   //span[@class='ant-radio ant-radio-checked']   3
    提交开窗数据
    Sleep   3
    当前页面可见   //dynamic-operation-editor/div/input[contains(@title,'${searchkey}')]

清除condition开窗数据
    [Arguments]    ${searchkey}
    当前页面可见   //dynamic-operation-editor/div/input[contains(@title,'${searchkey}')]
    鼠标悬停   //dynamic-operation-editor/div/input[contains(@title,'${searchkey}')]
    当前页面可见   //dynamic-operation-editor/div/div/span[contains(@class,'name-btn--del')]/i
    点击   //dynamic-operation-editor/div/div/span[contains(@class,'name-btn--del')]/i
    当前页面不可见元素  //dynamic-operation-editor/div/input[contains(@title,'${searchkey}')]    3

单选开窗功能属性检查
    元素存在则点击   //dynamic-operation-editor/div[@class='editor showInputSearch']/div/div/span/i    3
    Sleep   5
    #根据配置显示字段
    当前页面可见   //header-render/div/div/span[@title='状态']   3
    #根据查询条件展示数据
    当前页面可见   //div[@row-index='0']/div[@col-id='manage_status']/following::span[@title='Y'][1]    3
    #开窗字段显示【主】、【荐】
    当前页面可见   //div[@row-index='0']/div[@col-id='create_by']/following::span[text()='荐']    3
    当前页面可见   //div[@row-index='0']/div[@col-id='create_by']/following::span[text()='主']    3

开窗查询条件筛选
    [Arguments]    ${filedkey}    ${querycondition}    ${searchkey}
    当前页面可见   //ath-open-window-search/ath-multi-condition-search/div/div/div[contains(@class,'search-line')]   3
    #选择栏位
    元素存在则点击   //div[contains(@class,'search-container-field')]/ath-select/ath-select-arrow/i[contains(@class,'athena-select-arrow-down')]   3
    当前页面可见   //div/div[@class='cdk-overlay-pane']/ath-option-container    3
    元素存在则点击   //div/ath-tooltip-wrapper/div/span[text()='${filedkey}']   3
    #选择公式
    点击   //div[contains(@class,'search-container-formula')]/ath-select/ath-select-arrow/i[contains(@class,'athena-select-arrow-down')]
    当前页面可见   //*[@class="cdk-overlay-pane"]/ath-option-container/div[@class='athena-option-container']   3
    元素存在则点击   //div/ath-tooltip-wrapper/div/span[text()='${querycondition}']   3
    #搜索关键字
    元素存在则点击   //div[contains(@class,'ath-input-content-box')]/input   3
    输入    //div[contains(@class,'ath-input-content-box')]/input    ${searchkey}
    Run Keyword If  '${filedkey}'=='供应商名称-供应商编号'   当前页面可见      //dy-open-window-table-cell-renderer/div/div/dynamic-contain-explicit/ath-contain-explicit/div/div/span/span[contains(text(),'${searchkey}')]
    ...    ELSE    当前页面可见   //*[@row-index='0']/div/div/span/dy-open-window-table-cell-renderer/following::span[contains(text(),'${searchkey}')]   3

开窗快捷入口
    当前页面可见    //ath-open-window/div/div/div/span[@class='add-button']
    点击   //ath-open-window/div/div/div/span[@class='add-button']
    当前页面可见    //div/app-base-data-tab-title/div/div[contains(text(),'表格_开窗维护表')]
    关闭当前基础资料

打开非输入型开窗
    [Arguments]    ${owkey}
    点击   //dynamic-operation-editor/div/input[@placeholder='请选择采购产品编号']
    当前页面可见   //*[@col-id='${owkey}']/div/span/cell-renderer/div/div/div/dynamic-operation-editor/div/div/div/span[@class='edit hidden-icon']/i   3
    鼠标悬停   //*[@col-id='${owkey}']/div/span/cell-renderer/div/div/div/dynamic-operation-editor/div/div/div/span[@class='edit hidden-icon']/i
    点击   //*[@col-id='${owkey}']/div/span/cell-renderer/div/div/div/dynamic-operation-editor/div/div/div/span[@class='edit hidden-icon']/i
    Sleep   10
    当前页面可见   //ath-open-window-table/div[@class='table-and-pagination multipleOpenWindow']   3

多选开窗功能属性检查
    #根据配置显示字段
    当前页面可见   //header-render/div/div/span[@title='金额单位']   3
    #根据查询条件展示数据
    当前页面可见   //div[@row-index='0']/div[@col-id='product_price']/following::span[@title='人民币'][1]   3
    #开窗字段显示【主】、【荐】
    当前页面可见   //div[@row-index='0']/div[@col-id='product_name']/div/span/app-custom-open-window/following::span[text()='荐'][1]    3
    当前页面可见   //div[@row-index='0']/div[@col-id='product_name']/div/span/app-custom-open-window/following::span[text()='主'][1]    3

开窗必填项校验
    点击单元格字段      case_tid
    输入单元格字段值    202502201   case_tid
    Press Keys    //input[@name='case_tid']     ENTER
    当前页面可见   //dynamic-control-error/div/div/div/span/span[contains(text(),'开窗字段不可为空')]   3

开窗最大/最小长度校验
    #最大长度校验
    打开单选开窗
    搜索开窗数据    202510
    元素存在则点击   //dynamic-ant-label/ant-label/span[contains(@title,'202510')]    3
    当前页面可见   //span[@class='ant-radio ant-radio-checked']   3
    提交开窗数据
    当前页面可见   //dynamic-control-error/div/div/div/span/span[contains(text(),'最大长度为10')]   3
    清除回显数据    202510      dynamic-operation-editor
    #最小长度校验
    打开单选开窗
    搜索开窗数据    表1
    元素存在则点击   //dynamic-ant-label/ant-label/span[contains(@title,'表1')]    3
    当前页面可见   //span[@class='ant-radio ant-radio-checked']   3
    提交开窗数据
    当前页面可见   //dynamic-control-error/div/div/div/span/span[contains(text(),'最小长度为3')]   3

开窗最大/最小值规则校验
    鼠标悬停   //div[@col-id='case_tphone']/div[@class='ag-cell-wrapper']
    当前页面可见   //dynamic-operation-editor/div[@class='editor isTableRender']/div/div/span[@class='edit hidden-icon']/i   3
    点击   //dynamic-operation-editor/div[@class='editor isTableRender']/div/div/span[@class='edit hidden-icon']/i
    搜索开窗数据    1008611
    元素存在则点击   //dynamic-ant-label/ant-label/span[contains(@title,'1008611')]    3
    当前页面可见   //span[@class='ant-radio ant-radio-checked']   3
    提交开窗数据
    #最大值校验
    当前页面可见   //dynamic-control-error/div/div/div/span/span[contains(text(),'最大值为100')]   3
    #清除回显数据
    鼠标悬停   //div[@col-id='case_tphone']/div[@class='ag-cell-wrapper']
    #当前页面可见   \ \ //dynamic-operation-editor/div/div/span[contains(@class,'name-btn--del')]    3
    #点击   \ //dynamic-operation-editor/div/div/span[contains(@class,'name-btn--del')]
    #鼠标悬停   \ //div[@col-id='case_tphone']/div[@class='ag-cell-wrapper']
    #最小值校验
    当前页面可见   //*[@col-id='case_tphone']/div/span/cell-renderer/div/div/div/dynamic-operation-editor/div/div/div/span/i   3
    点击   //*[@col-id='case_tphone']/div/span/cell-renderer/div/div/div/dynamic-operation-editor/div/div/div/span/i
    开窗查询条件筛选    全部    等于    8
    元素存在则点击   //dynamic-ant-label/ant-label/span[contains(@title,'8')]   3
    当前页面可见   //span[@class='ant-radio ant-radio-checked']   3
    提交开窗数据
    当前页面可见   //dynamic-control-error/div/div/div/span/span[contains(text(),'最小值为10')]   3

开窗重复性校验
    点击新增行
    鼠标悬停      //div[@row-index='1']/div[@col-id='case_tname']/div[@class='ag-cell-wrapper']
    点击   //div[@row-index='1']/div[@col-id='case_tname']/div/span/cell-renderer/div/div/div/dynamic-operation-editor/div/div/div/span[@class='edit hidden-icon']/i
    搜索开窗数据    表1
    元素存在则点击   //dynamic-ant-label/ant-label/span[contains(@title,'表1')]    3
    当前页面可见   //span[@class='ant-radio ant-radio-checked']   3
    提交开窗数据
    当前页面可见   //*[@row-index='1']/div/following::dynamic-control-error/div/div/div/span/span[text()='数据已存在，不可重复添加;']  3

开窗样式color校验
    打开单选开窗
    搜索开窗数据    表格名称01
    元素存在则点击   //dynamic-ant-label/ant-label/span[contains(@title,'表格名称01')]   3
    当前页面可见   //span[@class='ant-radio ant-radio-checked']   3
    提交开窗数据
    当前页面可见   //dynamic-operation-editor/div/span[@style='color: rgb(249, 64, 76);']   3
    清除回显数据    表格名称01  dynamic-operation-editor
    打开单选开窗
    搜索开窗数据    表1
    元素存在则点击   //dynamic-ant-label/ant-label/span[contains(@title,'表1')]   3
    当前页面可见   //span[@class='ant-radio ant-radio-checked']   3
    提交开窗数据
    当前页面可见   //dynamic-operation-editor/div/span[@style='color: rgb(49, 179, 68);']   3

开窗样式disabled校验
    打开单选开窗
    搜索开窗数据    表格开窗case名称80
    元素存在则点击   //dynamic-ant-label/ant-label/span[contains(@title,'表格开窗case名称80')]   3
    当前页面可见   //span[@class='ant-radio ant-radio-checked']   3
    提交开窗数据
    鼠标悬停   //div[@row-index='0']/div[@col-id='case_tname']/div[@class='ag-cell-wrapper']
    当前页面可见   //dynamic-operation-editor/div[contains(@class,'readOnly')]   3

开窗默认值规则校验
    打开发起项目录入界面    card_projectCenterConsole_mainProject
    Sleep   3
    当前页面可见   //dynamic-operation-editor/div/span[@title='测试环境自动化测试小AI平台']   3

开窗赋值、联动规则校验
    点击新增行
    当前页面可见   //*[@class='cdk-overlay-pane']/nz-modal-container/div/div   3
    关闭开窗窗口
    当前页面可见   //dynamic-operation-editor/div/span[@title='中国移动']   3
