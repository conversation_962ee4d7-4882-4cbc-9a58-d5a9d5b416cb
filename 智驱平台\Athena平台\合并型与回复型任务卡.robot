*** Settings ***
Suite Setup       Run Keywords    环境设定
Test Teardown     Run Keywords    错误处理    关闭浏览器
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/系统关键字/公共方法.robot
Resource          ../关键字/业务关键字/合并任务卡.robot

*** Test Cases ***
合并型任务卡
    #当责者账号
    #合并型任务卡场景：连续发起两次项目。任务会合并到一张卡里面
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    <EMAIL>    '${ENV}'=='microsoft.prod'    <EMAIL>
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    Huanglei@0920    '${ENV}'=='microsoft.prod'    Huanglei@0920
    ${tenant}    Set Variable If    '${ENV}'=='pressure'    自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    Set Global Variable    ${key}    ${唯一标识}
    登录Athena平台    ${ownerUsername}    ${ownerPassword}    tenant=${tenant}
    手动发起项目（采购申请）    ${key}

回复型任务卡
    #生产环境没有适配，缺失邮件发送的模版
    #当责者账号
#    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    HL18271405997
#    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    HuangL0920
#    #邮件回复人
#    ${EmailUsername}    Set Variable If    '${ENV}'=='huawei.test'    <EMAIL>
#    ${EmailPassword}    Set Variable If    '${ENV}'=='huawei.test'    ${pwd}
    
    ${username}    Set Variable    HL18271405997
    ${password}    Set Variable    HuangL0920
    #邮件回复人
    ${EmailUsername}    Set Variable    <EMAIL>
    ${EmailPassword}    Set Variable    ${pwd}
    ${tenant}    Set Variable If    '${ENV}'=='pressure'    自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    ${key}    生成秒时间戳
    Set Global Variable    ${key}
    登录Athena平台    ${username}    ${password}    tenant=${tenant}
    手动发起项目（采购供应商寻源）    ${key}
    #回复
    登录Athena平台    ${EmailUsername}    ${EmailPassword}    tenant=${tenant}
    采购邀请回复
    邀请回复审核签核任务
    #功能闭环-工作提醒提示项目结束
    登录Athena平台    ${username}    ${password}    tenant=${tenant}
    工作提醒项目结束