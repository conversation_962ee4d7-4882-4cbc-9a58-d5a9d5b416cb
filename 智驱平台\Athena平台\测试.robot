*** Settings ***
Library    SeleniumLibrary
Library     ../../RFLib/Base.py
Library    String
Library    DateTime
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/系统关键字/公共方法.robot
Resource          ../关键字/业务关键字/开发平台.robot
Resource          ../关键字/业务关键字/智能入口.robot
Resource          ../关键字/业务关键字/核决.robot

*** Test Cases ***
获取浏览器会话信息
#    ${a}    Evaluate    str(uuid.uuid4())    modules=uuid
#    Open Browser    https://www.163.com    Chrome    alias=${a}
#    ${b}    Evaluate    str(uuid.uuid4())    modules=uuid
#    Open Browser    http://www.baidu.com    Chrome    alias=b
#    Switch Browser    ${a}
#    Maximize Browser Window
#    ${a}    Get Title
#    Sleep    1
#    Switch Browser    b
##    ${b}    Get Title
#    ${token}    Get Token    https://athena.digiwincloud.com.cn    https://isv-gateway.apps.digiwincloud.com.cn    <EMAIL>    @cjm820412000
#    Log    ${token}
#    ${time1}    Set Variable    2025-04-16 10:56
#    ${time2}    Set Variable    2025-04-16 09:42
#    ${timestamp1}    Convert Date    ${time1}    result_format=epoch
#    ${timestamp2}    Convert Date    ${time2}    result_format=epoch
#    ${is_greater}    Evaluate    ${timestamp1} > ${timestamp2}
#    Log    ${is_greater}
#    Run Keyword If    ${is_greater}    Log    ${time1} 大于 ${time2}
#    ...    ELSE    Log    ${time1} 小于等于 ${time2}

#
#    ${original_date}=    Set Variable    2025年04月22日 15:01
##    ${formatted_date}=    Convert Date    ${original_date}    input_format=%Y年%m月%d日 %H:%M    output_format=%Y-%m-%d %H:%M
##    Log    ${formatted_date}
#    ${datestr}    Datetime Format    ${original_date}
#    log    ${datestr}

    Open Browser    https://mail.163.com    gc
    Iframe选择
    元素存在则点击    //a[contains(text(),'密码登录')]
    输入    //input[@data-placeholder="邮箱账号或手机号码"]    18994129019
    输入    //input[@data-placeholder="输入密码"]    f.sL)4qD:5mMF4~
    点击    //a[@id="dologin"]
    Unselect Frame
    点击    //span[contains(text(),'收件箱')]
    点击    (//span[contains(text(),'自动化测试')])[1]
    ${str}    获取元素字符    //div[contains(text(),'时')]/following-sibling::div[1]

    ${result}    Evaluate    '${str}'.split(' (')    modules=sys
    Log    ${result}[0]
    ${datestr}    Datetime Format    ${result}[0]
    log    ${datestr}