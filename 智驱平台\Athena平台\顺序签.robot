*** Settings ***
Documentation    黄蕾
Suite Setup       Run Keywords    环境设定
Test Teardown     Run Keywords    错误处理    AND    关闭浏览器
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/系统关键字/公共方法.robot
Resource          ../关键字/业务关键字/顺序签核.robot
Resource          ../关键字/业务关键字/PCC.robot

*** Test Cases ***
单人职能签核
    #当责者账号
    ${username}    Set Variable If    '${ENV}'=='pressure'    <EMAIL>    '${ENV}'=='paas'    qcsupplier001   '${ENV}'=='huawei.test'    qcsupplier001    '${ENV}'=='huawei.prod'    qcsupplier001    '${ENV}'=='microsoft.prod'    qcsupplier001
    ${password}    Set Variable If    '${ENV}'=='pressure'    <PERSON><PERSON><PERSON>@0920    '${ENV}'=='paas'    supplier001    '${ENV}'=='huawei.test'    supplier001    '${ENV}'=='huawei.prod'    supplier001    '${ENV}'=='microsoft.prod'     supplier001
    ${tenant}    Set Variable If    '${ENV}'=='pressure'     自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'     自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境   
    Set Global Variable    ${key}    ${唯一标识}
    Set Global Variable    ${plan}    ${唯一标识}
    登录Athena平台    ${username}    ${password}    tenant=${tenant}
    手动发起项目（职能）    全部    发起项目    ${key}    ${key}    ${key}    ${key}    ${key}
    商品退货    ${key}
    #职能签核
    签核同意    ${key}
    关闭浏览器

单人部门主管签核
    #当责者账号
    ${username}    Set Variable If    '${ENV}'=='pressure'    <EMAIL>    '${ENV}'=='paas'    <EMAIL>    '${ENV}'=='huawei.test'    qcsupplier001    '${ENV}'=='huawei.prod'    qcsupplier001    '${ENV}'=='microsoft.prod'    qcsupplier001
    ${password}    Set Variable If    '${ENV}'=='pressure'    Huanglei@0920    '${ENV}'=='paas'    Huanglei@0920    '${ENV}'=='huawei.test'    supplier001    '${ENV}'=='huawei.prod'    supplier001    '${ENV}'=='microsoft.prod'    supplier001
    #部门主管
    ${managerusername}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${managerusernamepassword}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${tenant}    Set Variable If    '${ENV}'=='pressure'     自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'     自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境

    Set Global Variable    ${key}    ${唯一标识}
    Set Global Variable    ${plan}    ${唯一标识}
    登录Athena平台    ${username}    ${password}    tenant=${tenant}
    手动发起项目（主管）    全部    发起项目    ${key}
    商品发货    ${key}
    关闭浏览器
    #部门主管签核
    登录Athena平台    ${managerusername}    ${managerusernamepassword}    tenant=${tenant}
    #商品退回签核
    签核同意    ${key}
    关闭浏览器

多人或签
    #职能 id 002 name qcusr
    #当责者账号
    ${username}    Set Variable If    '${ENV}'=='pressure'    HL18271405997    '${ENV}'=='paas'    HL18271405997    '${ENV}'=='huawei.test'    HL18271405997    '${ENV}'=='huawei.prod'    HL18271405997    '${ENV}'=='microsoft.prod'    HL18271405997    '${ENV}'=='muihuawei.test'    HL18271405997
    ${password}    Set Variable If    '${ENV}'=='pressure'    HuangL0920    '${ENV}'=='paas'    HuangL0920    '${ENV}'=='huawei.test'    HuangL0920    '${ENV}'=='huawei.prod'    HuangL0920    '${ENV}'=='microsoft.prod'    HuangL0920    '${ENV}'=='muihuawei.test'    HuangL0920
    #部门主管1
    ${ownerUsername}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${ownerPassword}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    #部门主管2
    ${ownerUsername2}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${ownerPassword2}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001

    #模糊查询字段
    ${date}    Set Variable If    '${ENV}'=='pressure'    多人或签    '${ENV}'=='paas'    多人或签    '${ENV}'=='huawei.test'    多人或签    '${ENV}'=='huawei.prod'    多人或签    '${ENV}'=='microsoft.prod'    多人或签
    ${name}    Set Variable If    '${ENV}'=='pressure'    多人或签（职能）    '${ENV}'=='paas'    多人或签（职能）    '${ENV}'=='huawei.test'    多人或签（职能）    '${ENV}'=='huawei.prod'    多人或签（职能）    '${ENV}'=='microsoft.prod'    多人或签（职能）
    ${pdate}    Set Variable If    '${ENV}'=='pressure'    退货    '${ENV}'=='paas'    退货    '${ENV}'=='huawei.test'    退货    '${ENV}'=='huawei.prod'    退货    '${ENV}'=='microsoft.prod'    退货
    ${pname}    Set Variable If    '${ENV}'=='pressure'    退货成功    '${ENV}'=='paas'    退货成功    '${ENV}'=='huawei.test'    退货成功    '${ENV}'=='huawei.prod'    退货成功    '${ENV}'=='microsoft.prod'    退货成功
    ${tenant}    Set Variable If    '${ENV}'=='pressure'     自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'     自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    ${key}    生成秒时间戳
    Set Global Variable    ${key}
    登录Athena平台    ${username}    ${password}    当责者登录    tenant=${tenant}
#    切换浏览器    当责者登录
    手动发起项目（或签）    全部    发起项目    ${key}
    #我的任务/项目根据模糊搜索
    模糊搜索任务卡/项目卡    ${key}    ${date}    ${name}    ${pdate}    ${pname}
    Comment    添加收藏    ${key}
    关闭浏览器
    登录Athena平台    ${ownerUsername}    ${ownerPassword}    部门主管登录    tenant=${tenant}
#    切换浏览器    部门主管登录
    #视图切换
    卡片视图切换
    #团队任务/团队项目卡片展示
    团队项目任务卡片展示    ${key}
    关闭浏览器
    登录Athena平台    ${username}    ${password}    当责者登录    tenant=${tenant}
#    切换浏览器    当责者登录
    #职能签核
    签核同意    ${key}
    #人员签核
    签核同意    ${key}
    关闭浏览器
    登录Athena平台    ${ownerUsername2}    ${ownerPassword2}    部门主管登录    tenant=${tenant}
#    切换浏览器    部门主管登录
    #部门签核
    签核同意    ${key}
    关闭浏览器


