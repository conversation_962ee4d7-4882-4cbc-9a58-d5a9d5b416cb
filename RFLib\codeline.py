import subprocess
import os
import ast
from datetime import datetime


def count_python_functions_by_user(author, start_date, end_date, repo_path='.'):
    """
    使用 AST 分析 Python 文件中的函数（更准确）
    """

    # 获取指定时间范围内的提交
    log_cmd = [
        'git', '-C', repo_path, 'log',
        f'--author={author}',
        f'--since={start_date}',
        f'--until={end_date}',
        '--pretty=format:%H',
        '--no-merges'
    ]

    try:
        log_result = subprocess.run(log_cmd, capture_output=True, text=True, check=True)
        commits = log_result.stdout.strip().split('\n')
        commits = [commit for commit in commits if commit]

        if not commits:
            print(f"在指定时间范围内没有找到 {author} 的提交")
            return None

        function_stats = {
            'author': author,
            'period': f'{start_date} 到 {end_date}',
            'total_functions': 0,
            'total_classes': 0,
            'total_methods': 0,
            'by_file': {}
        }

        # 分析每个提交
        for commit in commits:
            # 获取 Python 文件
            diff_cmd = [
                'git', '-C', repo_path, 'diff-tree',
                '--no-commit-id', '--name-only', '-r', commit
            ]

            diff_result = subprocess.run(diff_cmd, capture_output=True, text=True, check=True)
            files = diff_result.stdout.strip().split('\n')
            python_files = [f for f in files if f.endswith('.py') and f]

            for file_path in python_files:
                try:
                    # 获取文件内容
                    show_cmd = [
                        'git', '-C', repo_path, 'show',
                        f'{commit}:{file_path}'
                    ]

                    show_result = subprocess.run(show_cmd, capture_output=True, text=True, check=True)
                    content = show_result.stdout

                    # 使用 AST 分析
                    tree = ast.parse(content)
                    visitor = FunctionVisitor()
                    visitor.visit(tree)

                    file_functions = len(visitor.functions)
                    file_classes = len(visitor.classes)
                    file_methods = len(visitor.methods)

                    if file_functions > 0 or file_classes > 0:
                        function_stats['total_functions'] += file_functions
                        function_stats['total_classes'] += file_classes
                        function_stats['total_methods'] += file_methods
                        function_stats['by_file'][file_path] = {
                            'functions': file_functions,
                            'classes': file_classes,
                            'methods': file_methods
                        }

                except subprocess.CalledProcessError:
                    continue
                except SyntaxError as e:
                    print(f"语法错误在文件 {file_path}: {e}")
                    continue
                except Exception as e:
                    print(f"分析文件 {file_path} 时出错: {e}")
                    continue

        return function_stats

    except subprocess.CalledProcessError as e:
        print(f"执行 Git 命令失败: {e}")
        return None
    except Exception as e:
        print(f"发生错误: {e}")
        return None


class FunctionVisitor(ast.NodeVisitor):
    """AST 访问器，用于统计函数、类和方法"""

    def __init__(self):
        self.functions = []
        self.classes = []
        self.methods = []
        self.current_class = None

    def visit_FunctionDef(self, node):
        if self.current_class:
            self.methods.append(node.name)
        else:
            self.functions.append(node.name)
        self.generic_visit(node)

    def visit_AsyncFunctionDef(self, node):
        if self.current_class:
            self.methods.append(node.name)
        else:
            self.functions.append(node.name)
        self.generic_visit(node)

    def visit_ClassDef(self, node):
        self.classes.append(node.name)
        old_class = self.current_class
        self.current_class = node.name
        self.generic_visit(node)
        self.current_class = old_class


def print_python_function_stats(stats):
    """打印 Python 函数统计结果"""
    if not stats:
        print("没有统计到函数信息")
        return

    print("=" * 60)
    print("Python 函数统计报告")
    print("=" * 60)
    print(f"用户: {stats['author']}")
    print(f"统计期间: {stats['period']}")
    print("-" * 60)
    print(f"函数总数: {stats['total_functions']}")
    print(f"类总数: {stats['total_classes']}")
    print(f"方法总数: {stats['total_methods']}")

    if stats['by_file']:
        print("\n按文件统计:")
        for file_path, counts in stats['by_file'].items():
            print(f"  {file_path}:")
            print(f"    函数: {counts['functions']}, 类: {counts['classes']}, 方法: {counts['methods']}")


# 使用示例
if __name__ == "__main__":
    # 指定参数
    author = "<EMAIL>"  # 替换为实际的用户名或邮箱
    start_date = "2025-01-01"
    end_date = "2025-06-30"

    # 统计 Python 函数
    stats = count_python_functions_by_user(author, start_date, end_date)

    if stats:
        print_python_function_stats(stats)
    else:
        print("函数统计失败")