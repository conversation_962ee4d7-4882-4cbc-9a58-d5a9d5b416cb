*** Settings ***
Documentation     郑苏振
Library           SeleniumLibrary
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/业务关键字/交付设计器.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/私有化部署关键字/交付设计器.robot
Resource          ../配置/全局参数.robot
Resource          ../配置/Athena平台.robot
Suite Setup       Run Keywords    环境设定
Suite Teardown    关闭浏览器

*** Test Cases ***
首页加载检查
    ${Username}    Set Variable If    '${ENV}'=='private.test'    default
    ${Password}    Set Variable If    '${ENV}'=='private.test'    1qaz@WSX 
    登录Athena平台    ${Username}    ${Password}
    语言设定    简体
    点击顶部菜单    全部
    点击右侧菜单    交付设计器
    选择窗体    交付设计器
    交付设计器首页加载
    打开导入数据开窗
    关闭导入数据开窗
    点击通用设定  通用设定
    [Teardown]    Run Keywords    关闭浏览器

首页/通用参数设定检查
    ${Username}    Set Variable If    '${ENV}'=='private.test'    default
    ${Password}    Set Variable If    '${ENV}'=='private.test'    1qaz@WSX 
    登录Athena平台    ${Username}    ${Password}
    语言设定    简体
    点击顶部菜单    全部
    点击右侧菜单    交付设计器
    选择窗体    交付设计器
    点击通用设定    通用设定
    设定通用参数设定/待办工作台设定/敏捷数据设定    通用参数设定
    生效通用参数设定
    [Teardown]    Run Keywords    关闭浏览器

首页/待办工作台设定
    ${Username}    Set Variable If    '${ENV}'=='private.test'    default
    ${Password}    Set Variable If    '${ENV}'=='private.test'    1qaz@WSX 
    登录Athena平台    ${Username}    ${Password}
    语言设定    简体
    点击顶部菜单    全部
    点击右侧菜单    交付设计器
    选择窗体    交付设计器
    点击通用设定    通用设定
    设定通用参数设定/待办工作台设定/敏捷数据设定    待办工作台设定
    #分组设定异常待排查
    # 选择任务/项目    任务
    # 选择分组设定/排序设定/筛选设定/卡面呈现    分组设定
    # 点击设定    设定
    选择任务/项目    任务
    选择分组设定/排序设定/筛选设定/卡面呈现    排序设定
    点击设定
    选择任务/项目    任务
    选择分组设定/排序设定/筛选设定/卡面呈现    筛选设定
    点击设定
    选择任务/项目    任务
    选择分组设定/排序设定/筛选设定/卡面呈现    卡面呈现
    卡面呈现设定  展示模式二
    卡面呈现设定  展示模式一
    #分组设定异常待排查
    # 选择任务/项目    项目
    # 选择分组设定/排序设定/筛选设定/卡面呈现    分组设定
    # 点击设定    设定
    选择任务/项目    项目
    选择分组设定/排序设定/筛选设定/卡面呈现    排序设定
    点击设定
    选择任务/项目    项目
    选择分组设定/排序设定/筛选设定/卡面呈现    筛选设定
    点击设定
    选择任务/项目    项目
    选择分组设定/排序设定/筛选设定/卡面呈现    卡面呈现
    卡面呈现设定  展示模式二
    卡面呈现设定  展示模式一
    [Teardown]    Run Keywords    关闭浏览器

首页/敏捷数据设定
    ${Username}    Set Variable If    '${ENV}'=='private.test'    default
    ${Password}    Set Variable If    '${ENV}'=='private.test'    1qaz@WSX 
    登录Athena平台    ${Username}    ${Password}
    语言设定    简体
    点击顶部菜单    全部
    点击右侧菜单    交付设计器
    选择窗体    交付设计器
    点击通用设定    通用设定
    设定通用参数设定/待办工作台设定/敏捷数据设定    敏捷数据设定
    敏数设定同义词配置/新建同义词  dgtest  域名称
    敏数设定同义词配置/删除同义词  dgtest
    [Teardown]    Run Keywords    关闭浏览器

应用参数设定
    ${Username}    Set Variable If    '${ENV}'=='private.test'    default
    ${Password}    Set Variable If    '${ENV}'=='private.test'    1qaz@WSX
    ${group}    Set Variable If    '${ENV}'=='private.test'    惠柏新材料科技(上海)股份有限公司测试 
    ${app}    Set Variable If    '${ENV}'=='private.test'    调用服务编排发起项目_DTDAPP
    登录Athena平台    ${Username}    ${Password}
    语言设定    简体
    点击顶部菜单    全部
    点击右侧菜单    交付设计器
    选择窗体    交付设计器
    应用设定    ${group}    ${app}
    生效应用设定
    [Teardown]    Run Keywords    关闭浏览器

数据驱动模型设定
    ${Username}    Set Variable If    '${ENV}'=='private.test'    default
    ${Password}    Set Variable If    '${ENV}'=='private.test'    1qaz@WSX
    ${group}    Set Variable If    '${ENV}'=='private.test'    惠柏新材料科技(上海)股份有限公司测试 
    ${app}    Set Variable If    '${ENV}'=='private.test'    试验优测
    登录Athena平台    ${Username}    ${Password}
    语言设定    简体
    点击顶部菜单    全部
    点击右侧菜单    交付设计器
    选择窗体    交付设计器
    应用设定    ${group}    ${app}
    数据驱动模型设定
    生效应用设定
    [Teardown]    Run Keywords    关闭浏览器

项目任务要素设定
    ${Username}    Set Variable If    '${ENV}'=='private.test'    default
    ${Password}    Set Variable If    '${ENV}'=='private.test'    1qaz@WSX
    ${group}    Set Variable If    '${ENV}'=='private.test'    惠柏新材料科技(上海)股份有限公司测试 
    ${app}    Set Variable If    '${ENV}'=='private.test'    试验优测
    登录Athena平台    ${Username}    ${Password}
    语言设定    简体
    点击顶部菜单    全部
    点击右侧菜单    交付设计器
    选择窗体    交付设计器
    应用设定    ${group}    ${app}
    项目/任务要素设定
    切换项目要素设定页签  质量检验
    Sleep   3
    打开进阶设定  project   质量检测
    关闭进阶设定
    生效应用设定
    Sleep   3
    打开进阶设定  task  质检异常处理
    关闭进阶设定
    生效应用设定
    [Teardown]    Run Keywords    关闭浏览器
