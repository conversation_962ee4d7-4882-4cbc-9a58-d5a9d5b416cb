*** Settings ***
Library           RequestsLibrary
Library           JSONLibrary

*** Keywords ***
POST
    [Arguments]    ${host}    ${path}    ${data}    #${digimiddlewareauthapp}=${token}    ${digimiddlewareauthappsecret}=${token}    ${digimiddlewareauthuser}=${token}
    ${headers}    Create Dictionary    Content-Type=application/json    locale=zh_CN    routerkey=${tenantId}    token=${token}
    Create Session    mysession    http://${host}    headers=${headers}
    ${data}   Evaluate    json.loads('${data}')    json
    ${response}    POST On Session    mysession    ${path}    json=${data}
    Log    ${response.text}
    Set Global Variable    ${response_dict}    ${response.json()}
#    ${duration}=    Get From Dictionary    ${response.json()}    duration

POST-UI
    [Arguments]    ${host}    ${path}    ${data}
    ${headers}    Create Dictionary    Content-Type=application/json
    Create Session    mysession    https://${host}    headers=${headers}    disable_warnings=1
    ${data}   Evaluate    json.loads('${data}')    json
    ${response}    POST On Session    mysession    ${path}    json=${data}
    Log    ${response.text}
    Set Global Variable    ${response_dict}    ${response.json()}
#    ${duration}=    Get From Dictionary    ${response.json()}    duration


DIGIWIN-POST
    [Arguments]    ${host}    ${path}    ${data}
    ${headers}    Create Dictionary    Content-Type=application/json    locale=zh_CN    digi-middleware-auth-app=${auth-app}    digi-middleware-auth-user=${digiwin_token}    digi-middleware-auth-app-secret=${auth-app-secret}
    Create Session    mysession    https://${host}    headers=${headers}    disable_warnings=1
    ${data}   Evaluate    json.loads('${data}')    json
    ${response}    POST On Session    mysession    ${path}    json=${data}
    Log    ${response.text}
    Set Global Variable    ${response_dict}    ${response.json()}
    

DIGIWIN-GET-TOKEN
    [Arguments]    ${host}    ${path}    ${data}
    ${headers}    Create Dictionary    Content-Type=application/json    locale=zh_CN    digi-middleware-auth-app=${auth-app}    digi-middleware-device-id=${device-id}    digi-middleware-auth-app-secret=${auth-app-secret}
    Create Session    mysession    https://${host}    headers=${headers}    disable_warnings=1
    ${data}   Evaluate    json.loads('${data}')    json
    ${response}    POST On Session    mysession    ${path}    json=${data}
    ${digiwin_token_list}    Get Value From Json    ${response.json()}    $..token
    ${digiwin_token}    Set Variable    ${digiwin_token_list}[0]
    Set Global Variable    ${digiwin_token}
    Log    ${digiwin_token}