*** Settings ***
Suite Setup       Run Keywords    环境设定
Test Teardown     Run Keywords    错误处理    AND    关闭浏览器
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/系统关键字/公共方法.robot
Resource          ../关键字/业务关键字/顺序签核.robot
Resource          ../关键字/业务关键字/PCC.robot

*** Test Cases ***
2.0应用核心功能用例
    [Documentation]    陈金明，场景：新建项目/任务卡-职能签核同意-人员签核退回重办
    #当责者账号/部门签核者账号
    ${username}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    HL18271405997    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    HL18271405997
    ${password}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    HuangL0920    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    HuangL0920
    #职能签核账号/人员签核账号/签核者账号
    ${approver}    Set Variable If    '${ENV}'=='paas'    HL18271405997    '${ENV}'=='huawei.test'    HL18271405997    '${ENV}'=='huawei.prod'    HL18271405997    '${ENV}'=='microsoft.prod'    HL18271405997    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${approverPaasword}    Set Variable If    '${ENV}'=='paas'    HuangL0920    '${ENV}'=='huawei.test'    HuangL0920    '${ENV}'=='huawei.prod'    HuangL0920    '${ENV}'=='microsoft.prod'    HuangL0920    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${key}    生成秒时间戳
    Set Global Variable    ${key}
    登录Athena平台    ${username}    ${password}    当责者/部门签核者
    手动发起项目（或签）    全部    发起项目    ${key}
    登录Athena平台    ${approver}    ${approverPaasword}    职能/人员签核者
    #职能签核
    签核同意    ${key}
    #人员签核-退回重办
    签核同意    ${key}
    切换浏览器    当责者/部门签核者
    #部门签核
    签核同意    ${key}

2.0应用核心功能用例签核不同意
    #当责者账号/部门签核者账号
    ${username}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    HL18271405997    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    HL18271405997
    ${password}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    HuangL0920    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    HuangL0920
    #职能签核账号/人员签核账号/签核者账号
    ${approver}    Set Variable If    '${ENV}'=='paas'    HL18271405997    '${ENV}'=='huawei.test'    HL18271405997    '${ENV}'=='huawei.prod'    HL18271405997    '${ENV}'=='microsoft.prod'    HL18271405997    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${approverPaasword}    Set Variable If    '${ENV}'=='paas'    HuangL0920    '${ENV}'=='huawei.test'    HuangL0920    '${ENV}'=='huawei.prod'    HuangL0920    '${ENV}'=='microsoft.prod'    HuangL0920    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${key}    生成秒时间戳
    Set Global Variable    ${key}
    登录Athena平台    ${username}    ${password}    当责者/部门签核者
    手动发起项目（或签）    全部    发起项目    ${key}
    登录Athena平台    ${approver}    ${approverPaasword}    职能/人员签核者
    #职能签核
    签核不同意    ${key}