*** Settings ***
Resource          ../系统关键字/web.robot
Resource          ../业务关键字/Athena平台.robot
#Resource          ../关键字/业务关键字/PCC.robot


*** Keywords ***
发起项目录入合同条款
    点击    //span[contains(text(),'发起项目')]
    点击    //span[contains(text(),'录入合同条款-pilotrun')]
    输入    //input[@placeholder='一级标题']    ${id}
    点击    //div[@class='ag-center-cols-container']/div/div[1]
    输入    //input[@placeholder='二级标题']    ${id}
    点击    //div[@class='ag-center-cols-container']/div/div[3]
    输入    //input[@placeholder='条款名称']    ${id}
    点击    //span[contains(text(),'提交')]
    点击    //span[contains(text(),'确定')]
    当前页面不可见元素    //span[contains(text(),'提交')]
    Sleep    5

催办消息检验    [Arguments]    ${text}
    打开消息页
    Sleep    10
    当前页面可见    //*[contains(text(),'${text}')]/preceding::span[contains(text(),'任务催办')]    180
    点击    //*[contains(text(),'${text}')]/preceding::span[contains(text(),'任务催办')]/following::span[text()='立即处理'][1]
    当前页面可见    //div[@title='${text}']

完成条款送审
    点击    //span[contains(text(),'提交')]
    点击    //span[contains(text(),'确定')]
    当前页面可见字符    上传成功
    当前页面可见    //p[contains(text(),'-提交-')]

