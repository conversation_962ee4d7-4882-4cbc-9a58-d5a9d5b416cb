*** Settings ***
Library           SeleniumLibrary
Resource          ../系统关键字/web.robot
Resource          ../../元素/Athena平台元素.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../系统关键字/接口.robot
Resource          ../业务关键字/交接.robot
Resource          ../业务关键字/待办.robot
Resource          ../业务关键字/基础数据录入.robot

*** Keywords ***
按钮在当前区域的位置检查
    [Arguments]    ${buttoName}    ${align}
    当前页面可见    //div[@align='${align}']/button/span[text()='${buttoName}']

按钮类型检查
    [Arguments]    ${buttoName}     ${styleMode} 
    当前页面可见    //div/button[contains(@class,'ant-btn-${styleMode}')]/span[text()='${buttoName}']

按钮大小检查
    [Arguments]    ${buttoName}     ${size}
    Run Keyword If  '${size}'=='defalut'   当前页面可见     //div/button[contains(@class,'ant-btn')]/span[text()='${buttoName}']
    ...    ELSE    当前页面可见    //div/button[contains(@class,'ant-btn-${size}')]/span[text()='${buttoName}']

按钮状态禁用
    [Arguments]    ${buttoName}     ${disabled}
    当前页面可见   //div/button[@disabled='${disabled}']/span[text()='${buttoName}']

开启幽灵按钮
    [Arguments]    ${buttoName}
    当前页面可见  //div/button[contains(@class,'ghost')]/span[text()='${buttoName}']
    Execute Javascript    document.querySelector('div.dynamic-form-item-content.dynamic-form-item-content-field-button_doubletest_detail_button1.ng-star-inserted > div > dynamic-ant-button > div > button').style.background = '#0000'
    
开启块状按钮
    [Arguments]    ${buttoName}
    当前页面可见  //div/button[contains(@class,'block')]/span[text()='${buttoName}']
    Execute Javascript    document.querySelector('div.dynamic-form-item-content.dynamic-form-item-content-field-button_doubletest_detail_buttont3.ng-star-inserted > div > dynamic-ant-button > div > button').style.width = '100%'

开启危险按钮
    [Arguments]    ${buttoName}
    当前页面可见  //div/button[contains(@class,'dangerous')]/span[text()='${buttoName}']
    Execute Javascript    document.querySelector('div.dynamic-form-item-content.dynamic-form-item-content-field-button_doubletest_detail_button4.ng-star-inserted > div > dynamic-ant-button > div > button').style.color = '#ff3528'

按钮图标展示
    当前页面可见  //div/button/i[@class='anticon ng-star-inserted']

基础资料跳报表
    [Arguments]    ${buttoName}
    元素存在则点击  //div/button/span[text()='${buttoName}']
    Sleep   3
    当前页面可见  //div[@aria-selected='true']/div/div/app-report-tab-title/div/div[@class='title']

基础资料跳基础资料
    [Arguments]    ${buttoName}
    元素存在则点击  //div/button/span[text()='${buttoName}']
    Sleep   3
    当前页面可见  //div[@aria-selected='true']/div/div/app-base-data-tab-title/div/div[@class='title']

基础资料跳发起项目
    [Arguments]    ${buttoName}
    元素存在则点击  //div/button/span[text()='${buttoName}']
    Sleep   3
    当前页面可见  //ul/li[contains(text(),'发起项目')]

任务详情跳报表
    [Arguments]    ${tagName}   ${buttonName}
    Sleep    5
    跳转到待办页    /todo/task
    交接任务/项目名称搜索    ${tagName}
    点击将要交接的任务卡    ${tagName}
    鼠标悬停    //div/button/span[text()='${buttonName}']
    点击    //div/button/span[text()='${buttonName}']
    当前页面可见   //div[@aria-selected='true']/div/div/app-report-tab-title/div/div[@class='title']


任务详情跳基础资料
    [Arguments]    ${buttonName}
    元素存在则点击  //div/button/span[text()='${buttonName}']
    当前页面可见   //div[@aria-selected='true']/div/div/app-base-data-tab-title/div/div[@class='title']

项目详情跳报表
    [Arguments]    ${tagName}   ${buttonName}
    Sleep    5
    跳转到待办页    /todo/project
    交接任务/项目名称搜索    ${tagName}
    点击将要交接的任务卡    ${tagName}
    元素存在则点击  //div/button/span[text()='${buttonName}']
    当前页面可见   //div[@aria-selected='true']/div/div/app-report-tab-title/div/div[@class='title']

项目详情跳任务详情
    [Arguments]    ${buttonName}
    元素存在则点击  //div/button/span[text()='${buttonName}']
    当前页面可见   //ul/li[contains(text(),'项目/任务详情')]

项目详情跳基础资料
    [Arguments]    ${buttonName}
    元素存在则点击  //div/button/span[text()='${buttonName}']
    当前页面可见   //div[@aria-selected='true']/div/div/app-base-data-tab-title/div/div[@class='title']

按钮二次确认弹窗
    [Arguments]    ${buttonName}
    元素存在则点击   //div/button/span[text()='${buttonName}']
    当前页面可见     //div[contains(text(),'确认删除？')]
    元素存在则点击   //button/span[contains(text(),'确定')]
    当前页面不可见元素   //div[contains(text(),'确认删除？')]

按钮可用条件condition
    [Arguments]    ${buttonName}   ${content}
    点击   //div[@class='ath-input-group-cocoon']/section/input[@name='buttonid']
    输入   //div[@class='ath-input-group-cocoon']/section/input[@name='buttonid']  ${content}
    Press Keys   //div[@class='ath-input-group-cocoon']/section/input[@name='buttonid']     ENTER
    Run Keyword If  '${content}'=='222'   当前页面可见  //div/button[@disabled='true']/span[text()='通用按钮 4']
    ...  ELSE   当前页面可见   //div/button/span[text()='通用按钮 4']

按钮动态显隐hidden
    [Arguments]    ${buttonName}    ${content}
    点击   //div[@class='ath-input-group-cocoon']/section/input[@name='buttonid']
    输入   //div[@class='ath-input-group-cocoon']/section/input[@name='buttonid']  ${content}
    Press Keys   //div[@class='ath-input-group-cocoon']/section/input[@name='buttonid']     ENTER
    Run Keyword If  '${content}'=='111'   当前页面不可见元素  //div/button/span[text()='通用按钮 4']
    ...  ELSE   当前页面可见   //div/button/span[text()='通用按钮 4']

打开子页面弹窗
    [Arguments]    ${buttonName}
    元素存在则点击    //dynamic-sub-page-button/div/button/span[text()='${buttonName}']
    当前页面可见    //*[@class='cdk-overlay-pane']/nz-modal-container/div/div/div/dynamic-sub-page-content

关闭子页面弹窗
    元素存在则点击  //*[@class='cdk-overlay-pane']/nz-modal-container/div/div/button/span/i[contains(@class,'anticon-close')]
    当前页面不可见元素   //*[@class='cdk-overlay-pane']/nz-modal-container/div/div/div/dynamic-sub-page-content

子页面抽屉位置检查
    [Arguments]    ${buttonName}    ${placement}
    元素存在则点击    //dynamic-sub-page-button/div/button/span[text()='${buttonName}']
    当前页面可见   //div[@class='ant-drawer ant-drawer-${placement} ant-drawer-open']/div/div
    当前页面可见    //*[@id="myGrid"]/ag-grid-angular/following::div[@class='ag-header-container']
    关闭子页面抽屉   ${placement}

关闭子页面抽屉
    [Arguments]    ${placement}
    #关闭遮罩
    Execute Javascript    document.querySelector('.ant-drawer-${placement}.ant-drawer-open').remove()
    元素存在则点击   //*[@class='cdk-overlay-pane']/div/following::button[contains(@class,'ant-drawer-close')]/i

按钮间距检查
    [Arguments]    ${gap}
    当前页面可见    //dynamic-button-group[contains(@style,'gap: ${gap}')]

展开更多按钮
    当前页面可见    //dynamic-button-group/button[contains(@class,'more-button-dropdown-btn')]
    点击    //dynamic-button-group/button[contains(@class,'more-button-dropdown-btn')]
    当前页面可见    //*[@class='cdk-overlay-pane']/div[contains(@class,'more-button-dropdown')]


关闭更多按钮
    当前页面可见    //*[@class='cdk-overlay-pane']/div[contains(@class,'more-button-dropdown')]
    点击    //dynamic-button-group/button[contains(@class,'more-button-dropdown-btn')]
    当前页面不可见元素    //*[@class='cdk-overlay-pane']/div[contains(@class,'more-button-dropdown')]

基础资料带参跳转到ABI/TBB报表
    [Arguments]    ${reportType}
    当前页面可见    //span[contains(text(),'基础资料跳${reportType}')]
    点击    //span[contains(text(),'基础资料跳${reportType}')]
    Sleep   5
    iframe选择    //iframe[contains(@src,'${reportType}')]
    Sleep   3
    Run Keyword If    '${reportType}'=='tbb'    当前页面可见    //input[@placeholder='qcuser001']
    Run Keyword If    '${reportType}'=='abi'    当前页面可见    //*[@id="HHH23"]/div/span
    Unselect Frame
    关闭页签    报表

任务卡详情跳转ABI/TBB报表
    [Arguments]    ${task_name}    ${reportType}
    #进入待办页-我的任务-筛选任务名称    请假单任务卡
    重置任务筛选
    按任务/项目类型筛选    ${task_name}     我的任务
    Sleep    3
    点击    //div[contains(text(),'New')]
    当前页面可见    //span[contains(text(),'${task_name}')]
    元素存在则点击    //span[contains(text(),'任务卡详情跳 ${reportType}')]
    Sleep   5
    iframe选择    //iframe[contains(@src,'${reportType}')]
    Sleep   3
    Run Keyword If    '${reportType}'=='tbb'    当前页面可见    //input[@placeholder='qcuser001']
    Run Keyword If    '${reportType}'=='abi'    当前页面可见    //*[@id="HHH23"]/div/span
    Unselect Frame
    关闭页签    报表

项目卡详情跳转ABI/TBB报表
    [Arguments]    ${project_name}    ${reportType}
    判断后跳转到目标页面    /todo/project
    Sleep   10
    点击    //div[@class='toolbar']/app-todo-filter
    当前页面可见  //button[@class='ath-btn ant-btn ant-btn-default']/span[text()='重置']
    点击  //button[@class='ath-btn ant-btn ant-btn-default']/span[text()='重置']
    元素存在则点击  //button[@class='ath-btn ant-btn ant-btn-primary']
    当前页面不可见元素  //app-todo-filter/app-todo-common-tool/span/div
    待办取消分组    我的项目
    按任务/项目类型筛选    ${project_name}     我的项目
    点击    //div[contains(text(),'New')]
    当前页面可见    //div[contains(text(),'${project_name}')]
    元素存在则点击    //span[contains(text(),'项目卡详情跳 ${reportType}')]
    Sleep   5
    iframe选择    //iframe[contains(@src,'${reportType}')]
    Sleep   3
    Run Keyword If    '${reportType}'=='tbb'    当前页面可见    //input[@placeholder='qcuser001']
    Run Keyword If    '${reportType}'=='abi'    当前页面可见    //*[@id="HHH23"]/div/span
    Unselect Frame
    关闭页签    报表


关闭带参跳转任务卡
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    请假单按钮测试
    元素存在则点击    //button/span[contains(text(),'维护')]
    当前页面可见    //button/span[contains(text(),'撤审')]
    点击    //button/span[contains(text(),'撤审')]
    当前页面可见    //div[text()='撤回意见']
    点击    //div/textarea[@id='retrive_opinion']
    输入    //div/textarea[@id='retrive_opinion']   关闭任务卡
    点击    //button/span[contains(text(),'确定')]
    当前页面可见字符    操作成功
    关闭维护页签
    点击    (//span[@class='ant-checkbox-inner'])[1]
    当前页面可见    //button/span[contains(text(),'删除')]
    点击    //button/span[contains(text(),'删除')]
    当前页面可见    //div[contains(@class,'ant-modal-confirm-content')]
    元素存在则点击    //button/span[contains(text(),'确定')]
    当前页面可见字符    操作成功
    #关闭业务数据录入页签  数据驱动2.0bug修复后开启

生成带参跳转任务卡
    点击新增打开子页签
    元素存在则点击    //button/span[contains(text(),'新建')]
    当前页面可见字符    保存成功
    Sleep   3
    元素存在则点击    //button/span[contains(text(),'维护')]
    当前页面可见    //button/span[contains(text(),'送审')]
    点击    //button/span[contains(text(),'送审')]
    当前页面可见字符    操作成功
    


