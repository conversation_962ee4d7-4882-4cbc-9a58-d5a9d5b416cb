*** Settings ***
Suite Setup       Run Keywords    环境设定
Suite Teardown    Run Keywords    关闭浏览器
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/业务关键字/开发平台.robot
Resource          ../关键字/业务关键字/公共方法.robot

*** Test Cases ***
数据清理
    [Documentation]    陈金明
    ${username}    Set Variable If    '${ENV}'=='paas'    ${username}    '${ENV}'=='huawei.test'    ${username}    '${ENV}'=='huawei.prod'    ${username}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${password}    Set Variable If    '${ENV}'=='paas'    ${password}    '${ENV}'=='huawei.test'    ${password}    '${ENV}'=='huawei.prod'    ${password}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${application}    Set Variable If    '${ENV}'=='paas'    质量测试专用应用    '${ENV}'=='huawei.test'    采购管理88CN    '${ENV}'=='huawei.prod'    采购管理88CN
    ${appCode}    Set Variable If    '${ENV}'=='paas'    qctest001    '${ENV}'=='huawei.test'    purchase88CN    '${ENV}'=='huawei.prod'    purchase88CN
    ${tenant}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    智驱中台工作台    '${ENV}'=='huawei.prod'    智驱中台工作台
    ${tenantId}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    IntelligentDriveCenterWorkbench    '${ENV}'=='huawei.prod'    IntelligentDriveCenterWorkbench
    #删除机制-删除范式-删除共享范式-逐级删除
    登录开发平台    ${username}    ${password}    ${tenant}
    开发平台.点击顶部菜单    解决方案中心
    搜索应用    ${application}
    进入应用配置页    ${appCode}
    选择浏览器窗体    -1
    点击左侧菜单    机制设计
    清理机制
    点击左侧菜单    范式设定
    清理范式
    跳转到首页
    开发平台.点击顶部菜单    资产中心
    点击左侧菜单    范式管理
    清理共享范式
