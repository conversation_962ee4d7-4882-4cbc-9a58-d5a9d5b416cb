*** Settings ***
Library           SeleniumLibrary
Resource          Athena平台.robot
Resource          鼎捷云.robot


*** Keywords ***
进入发起项目菜单
    点击顶部菜单    全部
    Sleep    3
    点击    //span[contains(text(),'发起项目')]
    Sleep    3

进入业务数据录入菜单
    点击顶部菜单    全部
    Sleep    3
    点击    //span[contains(text(),'业务数据录入')]
    Sleep    3

判断应用应该存在(基础数据录入入口)
    [Arguments]    ${application_name}
    ${application_count}    Get Element Count    //div[contains(text(),"${application_name}")]
    Should Be True    '${application_count}'=='1'    发现授权应用未正常展示

判断应用应该不存在(基础数据录入入口)
    [Arguments]    ${application_name}
    ${application_count}    Get Element Count    //div[contains(text(),"${application_name}")]
    Should Be True    '${application_count}'=='0'    发现未授权应用正常展示

判断单个作业模组应该存在(基础数据录入入口)
    [Arguments]    ${job_name}
    ${job_count}    Get Element Count    //span[contains(text(),"${job_name}")]
    Should Be True    '${job_count}'=='1'    发现授权应用未正常展示
    Sleep    3

判断单个作业模组不应该存在(基础数据录入入口)
    [Arguments]    ${job_name}
    ${job_count}    Get Element Count    //span[contains(text(),"${job_name}")]
    Should Be True    '${job_count}'=='0'    发现未授权作业模组正常展示
    Sleep    3

判断作业模组列表应该存在(基础数据录入入口)
    [Arguments]    ${job_name01}    ${job_name02}    ${job_name03}    ${application_name}
#    判断应用列表是否展开
    ${visible}    判断元素是否可见    //*[@hostkey="base-data-common" and @id="EMCS"]    6
    Run Keyword If    ${visible}    Log    noting    ELSE    点击    //div[contains(text(),'${application_name}')]    8
    Sleep    3
    ${joblist}    Create List    ${job_name01}    ${job_name02}    ${job_name03}
    FOR    ${jobname}    IN    @{joblist}
        Log    ${jobname}
        ${job_count}    获取元素数量    //span[contains(text(),"${job_name}")]
        Should Be True    '${job_count}'=='1'    发现授权应用未正常展示
        Sleep    3
    END

判断作业模组列表不应该存在(基础数据录入入口)
    [Arguments]    ${job_name01}    ${job_name02}    ${job_name03}
    ${joblist}    Create List    ${job_name01}    ${job_name02}    ${job_name03}
    FOR    ${jobname}    IN    @{joblist}
        Log    ${jobname}
        ${job_count}    Get Element Count    //span[contains(text(),"${job_name}")]
        Should Be True    '${job_count}'=='0'    发现未授权作业模组正常展示
        Sleep    3
    END

进入导入导出中心
    点击顶部菜单    全部
    Sleep    3
    点击    //span[contains(text(),'导入导出中心')]
    Sleep    3

