*** Settings ***
Library           SeleniumLibrary
Library           Collections
Library           BuiltIn
Library           String
#Library           ../../../RFLib/Base.py
Resource          ../系统关键字/web.robot
Resource          ../业务关键字/Athena平台.robot
Resource          Athena平台.robot
#Resource          首页.robot
#Resource          待办.robot
#Resource          数据查询.robot
#Resource          收藏.robot

*** Keywords ***
打开业务数据录入
    点击顶部菜单    全部
    点击右侧菜单    业务数据录入
    Sleep  3
    点击   //span[contains(text(),'ope_')]
    Sleep  3
*** Keywords ***
发起项目
    [Arguments]    ${story_code}    ${story_name}
    点击顶部菜单    全部
    点击  //span[contains(text(),'发起项目')]
    Sleep  3
    点击    //span[contains(text(),'satest')]
    当前页面可见    //span[contains (text(),'提交')]
    Sleep  10
    点击    //app-dynamic-input-display[@placeholder='需求编号']
    Sleep    3
    输入    //input[@placeholder="需求编号"]    ${story_code}
    Sleep  3
    点击    //app-dynamic-input-display[@placeholder='需求名称']
    Sleep    3
    输入    //input[@placeholder="需求名称"]    ${story_name}
    Sleep    3
    点击    //span[contains(text(),'提交')]
    点击    //span[contains(text(),'确定')]
    Sleep  10

*** Keywords ***
切换业务数据录入页签
    点击   //body//div[@id='main-header']//div//div//li[1]
    Sleep  3    
*** Variables ***
${共XX项定位}     //span[contains(text(), '共') and contains(text(), '项')]  # 分页器内的"共xx项"元素
${等待超时}       3s
*** Keywords ***
获取当前表格总数量
    ${raw_text}   获取元素字符    ${共xx项定位}    # 获取原始文本（如"共 10 项"）
    log    分页器原始文本: ${raw_text}
    # 使用正则表达式提取数字
    ${total_count}   Should Match Regexp   ${raw_text}  \\d+
    Log    共${total_count}项
    RETURN    ${total_count}
*** Keywords ***
刷新作业页签并断言表格数据项增加1
    ${before_count}  获取当前表格总数量
    刷新页面
    Sleep    10
    ${after_count}  获取当前表格总数量
    ${diff}  Evaluate    abs(${after_count}-${before_count})
    IF      ${diff} == 1
    Log    http自动任务发送成功
    ELSE
        Log    http自动任务发送失败
    END
    





