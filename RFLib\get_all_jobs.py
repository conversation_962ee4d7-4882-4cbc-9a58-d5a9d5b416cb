import requests

# Jenkins 服务器地址
jenkins_url = 'http://172.16.2.154:9989/api/json?tree=jobs[name]'
# 用户名和 API Token
username = 'chenjme'
api_token = '11bb21179c6bc84a24673a8e82aa96db25'

# 发送请求
response = requests.get(jenkins_url, auth=(username, api_token))

# 检查响应状态码
if response.status_code == 200:
    data = response.json()
    jobs = data.get('jobs', [])
    for job in jobs:
        print(job['name'])
else:
    print(f"请求失败，状态码: {response.status_code}")