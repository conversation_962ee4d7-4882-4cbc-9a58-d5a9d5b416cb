*** Settings ***
Library           SeleniumLibrary
Resource          ../../配置/Athena平台.robot
Resource          ../../元素/Athena平台元素.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/web.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../../元素/开发平台元素.robot
Resource          ../系统关键字/接口.robot
Resource          公共方法.robot
Resource          Athena平台控件.robot
Resource          Athena平台.robot
Resource          智能入口.robot
Resource          数据查询.robot

*** Keywords ***
手动发起项目（核决签）
    [Arguments]    ${topmeu}    ${meu}    ${key}    ${plan}    ${name}
    Athena平台.点击顶部菜单    ${topmeu}
    点击右侧菜单    ${meu}
    发起一般项目    质量考核审批    ${key}    ${plan}    HL的核决签
    Sleep    10

发起一般项目
    [Arguments]    ${task}    ${key}    ${plan}    ${name}
    点击    //*[contains(text(),'${task}')]
    输入    //input[@placeholder="质量考核主键"]    ${plan}
    输入    //input[@placeholder="考核标准编号"]    ${plan}
    输入    //input[@placeholder="考核标准名称"]    ${name}
    Sleep    3
    点击    //span[contains(text(),'提交')]
    点击    //span[contains(text(),'确定')]
    Sleep    10

我的任务搜索关键字
    [Arguments]    ${keyword}
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'我的任务')]
    任务/项目名称搜索    ${keyword}
    Sleep    5
    关注取消关注断言
    关注取消关注断言
    Sleep    3
    点击    //span[contains(text(),'${keyword}')]
    Sleep    10

核决签
    当前页面可见    //button[@class='ath-btn ant-btn ant-btn-primary ant-btn-lg ng-star-inserted']//span[@class='ng-star-inserted'][contains(text(),'同意')]
    点击    //button[@class='ath-btn ant-btn ant-btn-primary ant-btn-lg ng-star-inserted']//span[@class='ng-star-inserted'][contains(text(),'同意')]
    输入    //textarea[@id='opinion']    同意
    点击    //span[contains(text(),'确定')]
    Sleep    10

历史项目/任务热库中展示已完成任务卡
    [Arguments]    ${key}
    Athena平台.点击顶部菜单    全部
    点击右侧菜单    数据查询
    点击    //span[contains(text(),'历史项目/任务')]
    Sleep    2
    #    Run Keyword If    '${key}'!=' '    输入    //input[@class='search-input ant-input ath-input ng-untouched ng-pristine ng-valid ng-star-inserted']    ${key}
    #    数据查询.搜索
    js点击    //div[@class='ath-search-icon-with-text ng-star-inserted']//span[contains(text(),'搜索')]
    输入    //input[@placeholder='请输入关键字搜索']    ${key}
    点击    //button[@class='ath-search-single-input-icon-btn ant-btn ant-btn-primary ant-input-search-button ant-btn-icon-only ng-star-inserted']
    Sleep    10
    点击    //div[@title="任务"]
    ${a}    获取元素字符    //p[contains(text(),'同意')]
    Log    $(a)
    Should Be Equal    ${a}    -同意-
    Sleep    2

团队任务搜索关键字
    [Arguments]    ${keyword}
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'团队任务')]
    任务/项目名称搜索    ${keyword}
    Sleep    5
    点击    //span[contains(text(),'${keyword}')]
    Sleep    10

团队项目搜索关键字
    [Arguments]    ${keyword}
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'团队项目')]
    任务/项目名称搜索    ${keyword}
    # 此处执行两次的原因是关注和取消关注都要执行到
    关注取消关注断言
    关注取消关注断言
    Sleep    3
    点击    //span[contains(text(),'${keyword}')]
    Sleep    10

手动发起项目
    [Arguments]    ${topmeu}    ${meu}    ${key}    ${key2}
    Athena平台.点击顶部菜单    ${topmeu}
    点击右侧菜单    ${meu}
    发起项目    采购员信息审查-pilotrun    ${key}    ${key2}
    Sleep    10

发起项目
    [Arguments]    ${task}    ${key}    ${key2}
    点击    //*[contains(text(),'${task}')]
    点击    //span[contains(text(),'新增行')]
    #第一条任务
    输入    (//div[@class='ag-center-cols-viewport']/div/div/div[@col-id="purchaser_no"]//input)[1]    ${key}
    点击    (//div[@class='ag-center-cols-viewport']/div/div/div[@col-id="purchaser_name"])[1]
    输入    (//input[@placeholder="采购员名称"])[1]    ${key}
    点击    (//div[@class='ag-center-cols-viewport']/div/div/div[@col-id="purchaser_phone"])[1]
    输入    (//input[@placeholder="采购员电话"])[1]    ${key}
    点击    (//div[@class='ag-center-cols-viewport']/div/div/div[@col-id="purchaser_email"])[1]
    输入    (//input[@placeholder="采购员邮箱"])[1]    <EMAIL>
    #第二条任务
    输入    (//div[@class='ag-center-cols-viewport']/div/div/div[@col-id="purchaser_no"]//input)[2]    ${key2}
    点击    (//div[@class='ag-center-cols-viewport']/div/div/div[@col-id="purchaser_name"])[2]
    输入    //input[@placeholder="采购员名称"]    ${key2}
    点击    (//div[@class='ag-center-cols-viewport']/div/div/div[@col-id="purchaser_phone"])[2]
    输入    //input[@placeholder="采购员电话"]    ${key2}
    点击    (//div[@class='ag-center-cols-viewport']/div/div/div[@col-id="purchaser_email"])[2]
    输入    //input[@placeholder="采购员邮箱"]    <EMAIL>

    点击    //*[contains(text(),'提交')]
    Sleep    5
    点击    //span[contains(text(),'确定')]
    当前页面可见字符    发起成功

核决条件任务卡提交
    [Arguments]    ${key}    ${money}
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'我的任务')]
    任务/项目名称搜索    ${key}
    Sleep    5
    点击    //span[contains(text(),'${key}')]
    Sleep    10
    点击    (//div[@class='ag-center-cols-clipper']/div/div/div/div[@col-id="sign_number"])[1]
    点击    //span[contains(text(),'${money}')]
    点击    (//div[@class='ag-center-cols-clipper']/div/div/div/div[@col-id="sign_number"])[2]
    点击    //span[@class='option-item-label'][contains(text(),'${money}')]
    点击    //span[contains(text(),'提交')]
    点击    //span[contains(text(),'确定')]
    Sleep    5

签核人员信息
    [Arguments]    ${key}
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'我的任务')]
    任务/项目名称搜索    ${key}
    ${count}    获取元素数量    //span[@class='card-item-name-title'][contains(text(),'签核人员信息')]
    Should Be Equal As Strings    ${count}    2
    #后续配置有点问题，但是不影响分组场景的测试，暂时煮食
#    Sleep    5
#    点击    (//span[@class='card-item-name-title'][contains(text(),'签核人员信息')])[1]
#    Sleep    10
#    点击    //span[contains(text(),'同意')]
#    Sleep    5
#    点击    //span[contains(text(),'确定')]
#    Sleep    5