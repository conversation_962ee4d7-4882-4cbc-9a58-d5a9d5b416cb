*** Settings ***
Resource          ../系统关键字/web.robot

*** Keywords ***
管理模组左侧菜单
    [Arguments]    ${menu}
    点击    //span[contains(text(),'${menu}')]
    Sleep    2

事件记录簿查询
    [Arguments]    ${query}
    输入    //input[@type='text' and @placeholder="请输入发生时间或事件讯息"]    登入系统，成功
    点击    //button[@class='ant-btn ng-star-inserted ant-btn-primary ant-input-search-button']
    Sleep    5

事件记录簿查询结果校验
    [Arguments]    ${occurrenceTime}    ${info}    ${notification}    ${displayLoc}    ${origin}
    当前页面可见    //div[contains(text(),'事件记录簿')]    10
    当前页面可见字符    ${occurrenceTime}
    当前页面可见字符    ${info}
    当前页面可见字符    ${notification}
    当前页面可见字符    ${displayLoc}
    当前页面可见字符    ${origin}
        

运营单元代码一览表数据校验
    [Arguments]
    当前页面可见    //div[contains(text(),'运营单元代码一览表')]


启用/停止启用
    ${textBefor}    Get Text    //tbody[@class='ant-table-tbody']/tr[1]/td[6]/a[1]
    点击    //tbody[@class='ant-table-tbody']/tr[1]/td[6]/a[1]
    Sleep    3
    ${textAfter}    Get Text    //tbody[@class='ant-table-tbody']/tr[1]/td[6]/a[1]
    #这个写法diao bu diao
    Run Keyword If    '${textBefor}'=='启用'    Should Be Equal    ${textAfter}    停止启用
    Run Keyword If    '${textBefor}'=='停止启用'    Should Be Equal    ${textAfter}    启用

伙伴授权管理数据校验
    [Arguments]
    Element Should Be Visible    //div[contains(text(),'伙伴授权管理')]

搜索
    [Arguments]    ${arg1}    ${arg2}    
    点击    //div[@class='need-assit-search-icon pointer']
    查询条件选择    ${arg1}
    输入    //input[@type='text' and @placeholder='请输入关键词']    ${arg2}
    #搜索按钮
    点击    //button[@class='ant-btn ng-star-inserted ant-btn-primary ant-input-search-button']
    Sleep    5

新增
    [Arguments]    ${arg1}    ${arg2}    ${arg3}
    点击    //div[@class='need-assit-add']
    #此页面暂无好的定位方式,暂时这样,后续看能不能优化
    输入    //tbody[@class='ant-table-tbody']/tr[2]/td[2]/input[1]    ${arg1}
    输入    //tbody[@class='ant-table-tbody']/tr[2]/td[3]/input[1]    ${arg2}
    输入    //tbody[@class='ant-table-tbody']/tr[2]/td[4]/input[1]    ${arg3}
    #点击新增
    点击    //button[@class='ant-btn ant-btn-primary']
    Sleep    4
    #断言保存是否成功
    ${value}    Get Value    //tbody[@class='ant-table-tbody']/tr[1]/td[2]/input[1]
    Should Be Equal    ${arg1}    ${value}
    #删除新增的数据
    点击    //tbody[@class='ant-table-tbody']/tr[1]/td[1]/div[1]/*[1]
    Sleep    2
    #点击删除确定按钮
    点击    css=.ant-btn:nth-child(2)
    #断言删除是否成功
    ${value}    Get Value    //tbody[@class='ant-table-tbody']/tr[1]/td[3]/input[1]
    Should Not Be Equal    ${arg2}    ${value}

查询条件选择
    [Arguments]    ${arg}
    点击    //div[text()='请选择']
    点击    //li[contains(text(),'${arg}')]

需辅助名单管理数据校验
    [Arguments]    ${property}    ${enterprise}    ${enterpriseId}    ${uniformly}    ${update}
    ${gys}    Get Value    //table[@class='ant-table-fixed']/tbody[@class='ant-table-tbody']/tr[1]/td[2]/input[1]
    ${gysid}    Get Value    //table[@class='ant-table-fixed']/tbody[@class='ant-table-tbody']/tr[1]/td[3]/input[1]
    Element Should Be Visible    //div[contains(text(),'需辅助名单管理')]
    Page Should Contain    ${property}
    Should Be Equal    ${enterprise}    ${gys}
    Should Be Equal    ${enterpriseId}    ${gysid}
    Page Should Contain    ${uniformly}
    Page Should Contain    ${update}
    
传送方式管理数据校验
    [Arguments]
    Element Should Be Visible    //div[contains(text(),'传送方式管理')]

数据来源设定数据校验
    [Arguments]
    Element Should Be Visible    //div[contains(text(),'数据来源设定')]

授权码管理数据校验
    [Arguments]
    Element Should Be Visible    //div[contains(text(),'授权码管理')]

用户登录卡管理数据校验
    [Arguments]
    Element Should Be Visible    //div[contains(text(),'用户登录卡管理')]