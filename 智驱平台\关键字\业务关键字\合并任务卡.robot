*** Settings ***
Library           SeleniumLibrary
Resource          ../../配置/Athena平台.robot
Resource          ../../元素/Athena平台元素.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/web.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../../元素/开发平台元素.robot
Resource          ../系统关键字/接口.robot
Resource          公共方法.robot
Resource          Athena平台控件.robot
Resource          Athena平台.robot
Resource          智能入口.robot
Resource          数据查询.robot
Resource          ../控件关键字/表格分组.robot

*** Keywords ***
手动发起项目（采购申请）
    [Arguments]    ${key}
    Athena平台.点击顶部菜单    全部
    点击    //span[contains(text(),'发起项目')]
    点击    //*[contains(text(),"采购申请-pilotRun")]
    Sleep    10
    新增合并数据    product_name    total_amount    quantity    owner_dept_name    owner_emp_name    YSL246    徐筱静    TestAthenaAutoTestAi001    owner_emp_phone    ${key}
    #提交
    点击    //span[contains(text(),'提交')]
    Sleep    3
    点击    //span[contains(text(),'确定')]
    当前页面可见字符    发起成功
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'我的任务')]
    任务/项目名称搜索    ${key}
    Sleep    5
    当前页面可见字符    ${key}
    点击    //span[contains(text(),'申请采购商品：YSL')]
    点击    //ath-patch-header-renderer[@class='ng-star-inserted']/label/span[@class="ant-checkbox"]
    点击    //*[contains(text(),'同意')]
    点击    //span[contains(text(),'确定')]
    Sleep    5

发起采购申请任务卡
    [Arguments]    ${project}
    鼠标悬停    //div[@class="editor isTableRender showInputSearch"]/div/div
    点击    //div[@class="editor isTableRender showInputSearch"]/div/div
    点击    //div[@class="ag-pinned-left-cols-container"]/div[@class="ag-row-even ag-row-no-focus ag-row ag-row-level-0 ag-row-position-absolute ag-row-first ag-after-created"]
    点击    //div[@class="ant-modal-footer"]/button[@class="ath-btn ant-btn ant-btn-primary ng-star-inserted active"]
    输入    //input[@class='ant-input ath-input']    100
    输入    //div[@class="ag-header-viewport"]/div/div/div[@col-id="quantity"]//input    10
    点击    //div[@class="ag-center-cols-container"]/div/div[@col-id="owner_dept_name"]/div/span/cell-renderer/div/div/div/dynamic-operation-editor/div/div
    Sleep    5
    鼠标悬停    //div[@class="editor isTableRender show showInputSearch"]/div/div
    点击    //div[@class="editor isTableRender show showInputSearch"]/div/div
    点击    //div[@class="ag-pinned-left-cols-container"]/div[@class="ag-row-even ag-row-no-focus ag-row ag-row-level-0 ag-row-position-absolute ag-row-first ag-row-selected ag-after-created"]
    点击    //div[@class="ant-modal-footer"]/button[@class="ath-btn ant-btn ant-btn-primary ng-star-inserted active"]
    鼠标悬停    //div[@class="editor isTableRender"]/div/div
    点击    //div[@class="editor isTableRender"]/div/div
    点击    //div[@class="ag-pinned-left-cols-container"]/div[@class="ag-row-even ag-row-no-focus ag-row ag-row-level-0 ag-row-position-absolute ag-row-first ag-after-created"]
    点击    //div[@class="ant-modal-footer"]/button[@class="ath-btn ant-btn ant-btn-primary ath-btn-no-submit ng-star-inserted"]/span
    输入    //textarea[@placeholder="申请人电话"]    ${project}

新增合并数据
    [Arguments]    ${product_name}    ${total_amount}    ${quantity}    ${owner_dept_name}    ${owner_emp_name}    ${owkey1}    ${owkey2}    ${owkey3}    ${owner_emp_phone}    ${phone}
    #发起项目-采购申请-必填字段1  开窗
    鼠标悬停       //div[@col-id='${product_name}'][@role='gridcell']
    点击    //div[@col-id='${product_name}'][@role='gridcell']/descendant::span[@class='edit hidden-icon']
    单选开窗1    ${owkey1}
    #发起项目-采购申请-必填字段2
    鼠标悬停    //div[@col-id='${total_amount}'][@role='gridcell']
    点击    //div[@col-id='${total_amount}'][@role='gridcell']
    web.输入    //div/input[contains(@class,'ant-input-number-input')]    299
    #发起项目-采购申请-必填字段3
    鼠标悬停        //div[@col-id='${quantity}'][@role='gridcell']
    点击    //div[@col-id='${quantity}'][@role='gridcell']
    web.输入    //ath-input-text/ath-input-group/div[2]/section/input    20
    Press Keys    //ath-input-text/ath-input-group/div[2]/section/input    ENTER
    #发起项目-采购申请-必填字段4  开窗
    鼠标悬停        //div[@col-id='${owner_dept_name}'][@role='gridcell']
    点击    //div[@col-id='${owner_dept_name}'][@role='gridcell']/descendant::span[@class='edit hidden-icon']
    单选开窗1    ${owkey2}
    鼠标悬停    //div[@col-id='${owner_emp_name}'][@role='gridcell']
    点击    //div[@col-id='${owner_emp_name}'][@role='gridcell']/descendant::span[@class='edit hidden-icon']
    单选开窗1    ${owkey3}
    当前页面不可见元素    操作成功
    #点击新增========================================新增第二条
    Log    新增第二条
    鼠标悬停  //button/span[contains(text(),'新增')]
    点击    //button/span[contains(text(),'新增')]
    #新增表格数据
    #发起项目-采购申请-必填字段1  开窗
    鼠标悬停       //div[@aria-rowindex=3]/child::div[@col-id='${product_name}'][@role='gridcell']
    点击    //div[@aria-rowindex=3]/child::div[@col-id='${product_name}'][@role='gridcell']/descendant::span[@class='edit hidden-icon']
    单选开窗1    ${owkey1}
    #发起项目-采购申请-必填字段2
    鼠标悬停  //div[@row-index='1']/div[2]/div/span/cell-renderer
    点击     //div[@row-index='1']/div[2]/div/span/cell-renderer
    web.输入    //div/input[contains(@class,'ant-input-number-input')]     399
    #发起项目-采购申请-必填字段3
    鼠标悬停  //div[@row-index='1']/div[3]/div/span/cell-renderer
    点击     //div[@row-index='1']/div[3]/div/span/cell-renderer
    web.输入    //div[@row-index='1']/div[3]/div/span/cell-renderer/*//input    200
    Press Keys    //div[@row-index='1']/div[3]/div/span/cell-renderer/*//input    ENTER
    #发起项目-采购申请-必填字段4   开窗
    鼠标悬停       //div[@aria-rowindex=3]/child::div[@col-id='${owner_dept_name}'][@role='gridcell']
    点击    //div[@aria-rowindex=3]/child::div[@col-id='${owner_dept_name}'][@role='gridcell']/descendant::span[@class='edit hidden-icon']
    单选开窗1    ${owkey2}
    #发起项目-采购申请-必填字段5-------开窗
    鼠标悬停       //div[@aria-rowindex=3]/child::div[@col-id='${owner_emp_name}'][@role='gridcell']
    点击    //div[@aria-rowindex=3]/child::div[@col-id='${owner_emp_name}'][@role='gridcell']/descendant::span[@class='edit hidden-icon']
    单选开窗1    ${owkey3}
    #发起项目-采购申请-摘要字段
    鼠标悬停  //div[@col-id='${owner_emp_phone}'][@role='gridcell']
    点击     //div[@col-id='${owner_emp_phone}'][@role='gridcell']
    web.输入  //textarea[@placeholder='申请人电话']    ${phone}
    Sleep    3

手动发起项目（采购供应商寻源）
    [Arguments]    ${id}
    Athena平台.点击顶部菜单    全部
    点击    //span[contains(text(),'发起项目')]
    点击    //span[contains(text(),'采购供应商寻源-pilotRun')]
    Sleep    10
    新增回复数据    plan_name    ${id}    sourcing_type    mode_name
    #提交
    点击    	//span[contains(text(),'提交')]
    Sleep    3
    点击    //span[contains(text(),'确定')]
    当前页面可见字符    发起成功
    #进入项目卡详情
    Comment    判断后跳转到目标页面    /todo/project
    Comment    Sleep    5
    Comment    点击    //div[@class='toolbar']/app-todo-filter
    Comment    Comment    点击    //button[@class='ath-btn ant-btn ant-btn-default']/span[text()='重置']
    Comment    任务/项目名称搜索    ${key}
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'我的任务')]
    任务/项目名称搜索    ${id}
    Sleep    5
    当前页面可见字符    ${id}
    点击    //span[contains(text(),'${id}')]
    Sleep    5
    #新增供应商
    新增供应商数据    陈金明
    Sleep    5
#    点击    //div[@title='${id}']/preceding::span[1]
    点击    //span[contains(text(),'提交')]
    点击    //span[contains(text(),'确定')]
    Sleep    3
    Comment    当前页面可见字符    上传成功

新增回复数据
    [Arguments]    ${plan_name}    ${id}    ${sourcing_type}    ${mode_name}
    #发起项目-采购供应商寻源-采购计划名称-摘要字段
    输入    //div[@col-id="${plan_name}"]//input[@placeholder="采购计划"]    ${id}
    #发起项目-采购供应商寻源-采购计划名称-必填字段1
    Mouse Over    //div[@ref="centerContainer"]/div/div/div/div[@col-id="${sourcing_type}"]
    点击    //span[contains(text(),'请选择')]
    点击    //span[contains(text(),'公开招标')]
    #发起项目-采购供应商寻源-采购计划名称-必填字段2
    Mouse Over    //div[@ref="centerContainer"]/div/div/div/div[@col-id="${mode_name}"]
    点击    //div[@ref="centerContainer"]/div/div/div/div[@col-id="${mode_name}"]
    点击    //span[contains(text(),'集中采购')]

新增供应商数据
    [Arguments]    ${name}
    #整合供应商信息任务卡-供应商
#    Mouse Over    //span[contains(text(),'供应商维护')]
    点击    //span[text()=' 新增 ']
    #多选开窗搜索
    鼠标悬停    //div[@class='editor isTableRender showInputSearch']
    点击    //span[@class='edit hidden-icon']/i
    ${row}    获取元素属性值    //div[@id='first-line']//span[@title='${name}']/ancestor::div[@role="row"]    row-index
    点击   //ath-table[@class='table-container ag-theme-athena ng-star-inserted']//div[@name='left']//div[@row-index='${row}']//label
#    点击     //div[@id='first-line']//span[@title='${name}'][contains(text(),'${name}')]/preceding::input[1]/parent::span[1]
    Sleep    3
    #勾选搜索数据
    点击    //button[@class='ath-btn ant-btn ant-btn-primary ng-star-inserted active']//span[@class='ng-star-inserted'][contains(text(),'提交')]
    当前页面可见字符    操作成功

采购邀请回复
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'我的任务')]
    #搜索未读回复型任务卡
    点击    //*[contains(text(),'筛选')]
    点击    //*[contains(text(),'重置')]
    按筛选未读数据筛选待办数据
    当前页面可见字符    陈金明
    点击    //div[@class='cards-renderer-wrapper']/app-todo-card-render/div
    采购回复内容
    #提交回复任务卡
    当前页面可见    //button[@class='ath-btn ant-btn ant-btn-primary ant-btn-lg ng-star-inserted']
    点击    //button[@class='ath-btn ant-btn ant-btn-primary ant-btn-lg ng-star-inserted']
    Sleep    3
    当前页面可见    //div[@class='confirm-content']
    点击    //ath-modal-confirm-container/div/div/div/div/div[2]/button[2]
    Sleep    3
    Comment    当前页面可见字符    上传成功
    #提交戳
    当前页面可见    //*[contains(text(),'提交')]

采购回复内容
    [Arguments]
    #第一行
    #采购邀请回复-必填字段1
    鼠标悬停    //div[@class='ag-center-cols-container']/div[1]/div[@col-id='price']
    点击    //div[@class='ag-center-cols-container']/div[1]/div[@col-id='price']
    sleep    3
    输入    //div[@class='ag-center-cols-container']/div[1]/div[@col-id='price']//input    100
    sleep    3
    #采购邀请回复-必填字段2
    鼠标悬停    //div[@class="ag-center-cols-container"]/div[1]/div[@col-id='unit_name']
    点击    //div[@class="ag-center-cols-container"]/div[1]/div[@col-id='unit_name']
    sleep    3
    输入    //ath-input-group[@class='ath-input-group-wrapper ant-input-group-wrapper']//input[@name='unit_name']    10
    sleep    3
    #采购邀请回复-必填字段3
    鼠标悬停    //div[@class="ag-center-cols-container"]/div[1]/div[@col-id='delivery_date']
    点击    //div[@class="ag-center-cols-container"]/div[1]/div[@col-id='delivery_date']
    输入    //div[@class="ag-center-cols-container"]/div[1]/div[@col-id='delivery_date']//input    2025/02/28
    sleep    3
    点击    //div[@class="ag-center-cols-container"]/div[1]/div[@col-id="graph_info01"]
#    #第二行
#    #采购邀请回复-必填字段1
#    Mouse Over    //div[@class='ag-center-cols-container']/div[2]/div[@col-id="${price}"]
#    Comment    点击    //div[@class='ag-center-cols-container']/div[2]/div[@col-id="${price}"]
#    sleep    3
#    输入    //div[@class='ag-center-cols-container']/div[2]/div[@col-id="${price}"]//input    100
#    sleep    3
#    #采购邀请回复-必填字段2
#    鼠标悬停    //div[@class="ag-center-cols-container"]/div[2]/div[@col-id="${unit_name}"]
#    点击    //div[@class="ag-center-cols-container"]/div[2]/div[@col-id="${unit_name}"]
#    输入    //ath-input-group[@class='ath-input-group-wrapper ant-input-group-wrapper']//input[@name='${unit_name}']    10
#    #采购邀请回复-必填字段3
#    鼠标悬停    //div[@class="ag-center-cols-container"]/div[2]/div[@col-id="${delivery_date}"]
#    点击    //div[@class="ag-center-cols-container"]/div[2]/div[@col-id="${delivery_date}"]
#    输入    //div[@class="ag-center-cols-container"]/div[2]/div[@col-id="delivery_date"]//input    2025/02/28
#    sleep    3
#    点击    //div[@class="ag-center-cols-container"]/div[2]/div[@col-id="graph_info01"]
#    #输入第一行报价
#    鼠标悬停    //div[@class='ag-center-cols-container']/div[1]/div[@col-id="${price}"]
#    点击    //div[@class='ag-center-cols-container']/div[1]/div[@col-id="${price}"]
#    sleep    3
#    输入    //div[@class='ag-center-cols-container']/div[1]/div[@col-id="${price}"]//input    100
#    sleep    3
#    点击    //div[@class="ag-center-cols-container"]/div[2]/div[@col-id="graph_info01"]
#    #输入第二行报价
#    鼠标悬停    //div[@class='ag-center-cols-container']/div[2]/div[@col-id="${price}"]
#    点击    //div[@class='ag-center-cols-container']/div[2]/div[@col-id="${price}"]
#    sleep    3
#    输入    //div[@class='ag-center-cols-container']/div[2]/div[@col-id="${price}"]//input    100
#    sleep    3
#    点击    //div[@class="ag-center-cols-container"]/div[2]/div[@col-id="graph_info01"]

邀请回复审核签核任务
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'我的任务')]
    #搜索未读回复型任务卡
    点击    //*[contains(text(),'筛选')]
    点击    //*[contains(text(),'重置')]
    输入    //div/app-todo-comm-filter/div[1]/app-todo-filter-item/ath-tree-select/div/nz-select-search/input    邀请回复签核\n
    元素存在则点击    //div/span/span[text()='邀请回复签核']
    点击    //div/app-todo-comm-filter/div[1]/app-todo-filter-item/ath-tree-select/div/nz-select-search/input
    输入    //div[@class="filter-list-container ng-star-inserted"]/app-todo-comm-filter/div[2]/app-todo-filter-item/ath-tree-select/div/nz-select-search/input    未读
    鼠标悬停    //span[@class='font-highlight'][text()='未读']
    点击    //span[@class='font-highlight'][text()='未读']
    Comment    当前页面可见    //div[contains(@class,'athena-selected-panel')]    3
    点击    //button[@class='ath-btn ant-btn ant-btn-primary']
    点击    //div[@class='cards-renderer-wrapper']/app-todo-card-render[1]/div
    Sleep    5
    #同意
    点击    //span[contains(text(),'同意')]
    点击    //span[contains(text(),'确定')]
    Comment    当前页面可见字符    上传成功
    当前页面可见    //*[contains(text(),'同意')]

工作提醒项目结束
    点击    //div[@class="ant-dropdown-trigger"]/ath-badge/nz-badge/div/i
    当前页面可见字符    项目结束
    当前页面可见字符    供应商邀请回复

按筛选未读数据筛选待办数据
    #筛选采购邀请回复任务卡
    输入    //div/app-todo-comm-filter/div[1]/app-todo-filter-item/ath-tree-select/div/nz-select-search/input    采购邀请
    元素存在则点击    //div/span/span[text()='采购邀请']
    点击    //div/app-todo-comm-filter/div[1]/app-todo-filter-item/ath-tree-select/div/nz-select-search/input
    输入    //div[@class="filter-list-container ng-star-inserted"]/app-todo-comm-filter/div[2]/app-todo-filter-item/ath-tree-select/div/nz-select-search/input    未读
    鼠标悬停    //span[@class='font-highlight'][text()='未读']
    点击    //span[@class='font-highlight'][text()='未读']
    Comment    当前页面可见    //div[contains(@class,'athena-selected-panel')]    3
    点击    //button[@class='ath-btn ant-btn ant-btn-primary']
    #根据创建时间降序排序
    js点击    //span[contains(text(),'排序')]
    点击    //span[contains(text(),'重置')]
    Sleep    5
    #删除排序条件
    #js点击    //span[contains(text(),'排序')]
    点击    //div[@class='cdk-drop-list sort-item-list']/div[1]/*[name()='svg']
    点击    //div[@class='cdk-drop-list sort-item-list']/div[1]/*[name()='svg']
    点击    //div[@class='cdk-drop-list sort-item-list']/div[1]/*[name()='svg']
    Comment    点击    //div[@class='cdk-drop-list sort-item-list']/div[1]/*[name()='svg']
    #降序
    #js点击    //span[contains(text(),'排序')]
    元素存在则点击    //div[@class='ant-tabs-tab-btn'][contains(text(),'降序')]    3
    点击    //span[contains(text(),'确定')]
    Sleep    5
