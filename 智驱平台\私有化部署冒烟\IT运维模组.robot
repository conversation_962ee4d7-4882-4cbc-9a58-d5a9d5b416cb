*** Settings ***
Documentation     沈飞飞
Suite Setup       Run Keywords    环境设定
Suite Teardown    Run Keywords    关闭浏览器
Library           SeleniumLibrary
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/业务关键字/Athena平台控件.robot
Resource          ../关键字/业务关键字/IT运维模组.robot
Resource          ../关键字/业务关键字/Athena平台.robot

*** Test Cases ***
IT运维模组
    [Documentation]    沈飞飞
    #...    目前只在生产执行
    ${ownerUsername}    Set Variable If    '${ENV}'=='private.test'    default     '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001
    ${ownerPassword}    Set Variable If    '${ENV}'=='private.test'    1qaz@WSX    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001

    登录Athena平台    ${ownerUsername}    ${ownerPassword}
    语言设定    简体
    点击顶部菜单    全部
    点击右侧菜单    IT运维模组
    sleep   10
    当前页面包含元素    //span[contains(text(),'总计')]

#    关闭异常信件发送

    [Teardown]    Run Keywords    关闭浏览器
