import requests

# 配置信息
token = "ac8be63e-e2fe-4698-a02c-bfc2e9658846"
tenant_id = "AthenaAutoProdWr"
statement_code = "abnormalReviewReviewQuery"
headers = {
    "token": "966c6883-1bc6-4109-bee3-e157193c76c8",  # 根据实际鉴权方式调整
    "Content-Type": "application/json",
    "locale": "zh_CN"
}

# 1. 调用第一个接口获取 resId 列表
list_url = "https://isv-gateway.apps.digiwincloud.com/knowledgegraph/restful/service/knowledgegraph/statement/styleList"
params = {
    "tenantId": tenant_id,
    "statementCode": statement_code
}

response = requests.get(list_url, headers=headers, params=params)
if response.status_code != 200:
    print(f"获取列表失败！状态码：{response.status_code}, 响应：{response.text}")
    exit()

data = response.json()
res_ids = [item["resId"] for item in data["response"]["list"]]

# 2. 循环调用删除接口
delete_url = "https://isv-gateway.apps.digiwincloud.com/knowledgegraph/restful/service/knowledgegraph/statement/deleteStyle"

for res_id in res_ids:
    payload = {
        "resId": res_id,
        "tenantId": tenant_id,
        "statementCode": statement_code
    }
    delete_response = requests.post(delete_url, headers=headers, json=payload)

    if delete_response.status_code == 200:
        print(f"成功删除 resId: {res_id}")
    else:
        print(f"删除失败！resId: {res_id}, 状态码：{delete_response.status_code}, 错误：{delete_response.text}")
