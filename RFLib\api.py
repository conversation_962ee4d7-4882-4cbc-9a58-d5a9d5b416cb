import json

# 示例 JSON 数据（你可以替换为你的文件路径或 API 请求结果）
data = {
    "results": {
        "A": {
            "frames": [
                {
                    "schema": {
                        "name": "{appName=\"HW_Prod_asa\", path=\"/asa/actuator/healthcheck\"}",
                        "refId": "A",
                        "meta": {
                            "custom": {
                                "resultType": "vector"
                            }
                        },
                        "fields": [
                            {
                                "name": "Time",
                                "type": "time",
                                "typeInfo": {
                                    "frame": "time.Time"
                                }
                            },
                            {
                                "name": "Value",
                                "type": "number",
                                "typeInfo": {
                                    "frame": "float64"
                                },
                                "labels": {
                                    "appName": "HW_Prod_asa",
                                    "path": "/asa/actuator/healthcheck"
                                },
                                "config": {
                                    "displayNameFromDS": "{appName=\"HW_Prod_asa\", path=\"/asa/actuator/healthcheck\"}"
                                }
                            }
                        ]
                    },
                    "data": {
                        "values": [
                            [1752825559000],
                            [12]
                        ]
                    }
                },
                {
                    "schema": {
                        "name": "{appName=\"HW_Prod_asa\", path=\"/asa/actuator/prometheus\"}",
                    },
                    "data": {
                        "values": [
                            [1752825559000],
                            [12]
                        ]
                    }
                },
                {
                    "schema": {
                        "name": "{appName=\"HW_Prod_asa\", path=\"/asa/atmc/project/distribute\"}",
                    },
                    "data": {
                        "values": [
                            [1752825559000],
                            [3]
                        ]
                    }
                },
                {
                    "schema": {
                        "name": "{appName=\"HW_Prod_asa\", path=\"/asa/healthcheck\"}",
                    },
                    "data": {
                        "values": [
                            [1752825559000],
                            [156]
                        ]
                    }
                }
            ]
        }
    }
}

# 提取 appName 和 path
results = []

frames = data['results']['A']['frames']
for frame in frames:
    schema_name = frame['schema']['name']
    # 使用字符串解析提取 appName 和 path
    pairs = schema_name.strip('{}').split(', ')
    info = {}
    for pair in pairs:
        key, value = pair.split('=')
        info[key] = value.strip('"')
    appName = info.get('appName')
    path = info.get('path')
    results.append({
        'appName': appName,
        'path': path
    })

# 输出结果
for item in results:
    print(f"appName: {item['appName']}, path: {item['path']}")

# 如果你需要保存为 CSV、JSON 文件，也可以扩展这个脚本