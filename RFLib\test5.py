# data = [{
#     'pageref': 'abc',
#     'startedDateTime': '2024-12-26T14:55:08.078+08:00',
#     'request': {
#         'method': 'GET',
#         'url': 'https://www.baidu.com/',
#         'httpVersion': 'HTTP/1.1',
#         'cookies': [],
#         'headers': [],
#         'queryString': [],
#         'headersSize': 669,
#         'bodySize': 0,
#         'comment': ''
#     },
#     'response': {
#         'status': 200,
#         'statusText': 'OK',
#         'httpVersion': 'HTTP/1.1',
#         'cookies': [],
#         'headers': [],
#         'content': {
#             'size': 0,
#             'mimeType': 'text/html; charset=utf-8',
#         },
#         'redirectURL': '',
#         'headersSize': 1118,
#         'bodySize': 94956,
#         'comment': ''
#     },
#     'cache': {},
#     'timings': {
#         'comment': '',
#         'dns': 1,
#         'send': 0,
#         'ssl': 440,
#         'wait': 37,
#         'connect': 466,
#         'receive': 52,
#         'blocked': 0
#     },
#     'serverIPAddress': '*************',
#     'comment': '',
#     'time': 559
# }, {
#     'pageref': 'abc',
#     'startedDateTime': '2024-12-26T14:55:08.411+08:00',
#     'request': {
#         'method': 'GET',
#         'url': 'https://pss.bdstatic.com/r/www/static/font/cosmic/pc/cos-icon_8bae49a.css',
#         'httpVersion': 'HTTP/1.1',
#         'cookies': [],
#         'headers': [],
#         'queryString': [],
#         'headersSize': 588,
#         'bodySize': 0,
#         'comment': ''
#     },
#     'response': {
#         'status': 200,
#         'statusText': 'OK',
#         'httpVersion': 'HTTP/1.1',
#         'cookies': [],
#         'headers': [],
#         'content': {
#             'size': 0,
#             'mimeType': 'text/css; charset=utf-8',
#         },
#         'redirectURL': '',
#         'headersSize': 889,
#         'bodySize': 2539,
#         'comment': ''
#     },
#     'cache': {},
#     'timings': {
#         'comment': '',
#         'dns': 27,
#         'send': 0,
#         'ssl': 202,
#         'wait': 37,
#         'connect': 252,
#         'receive': 0,
#         'blocked': 0
#     },
#     'serverIPAddress': '************',
#     'comment': '',
#     'time': 319
# }, {
#     'pageref': 'abc',
#     'startedDateTime': '2024-12-26T14:55:08.411+08:00',
#     'request': {
#         'method': 'GET',
#         'url': 'https://pss.bdstatic.com/static/superman/img/topnav/newfanyi-da0cea8f7e.png',
#         'httpVersion': 'HTTP/1.1',
#         'cookies': [],
#         'headers': [],
#         'queryString': [],
#         'headersSize': 636,
#         'bodySize': 0,
#         'comment': ''
#     },
#     'response': {
#         'status': 200,
#         'statusText': 'OK',
#         'httpVersion': 'HTTP/1.1',
#         'cookies': [],
#         'headers': [],
#         'content': {
#             'size': 0,
#         },
#         'redirectURL': '',
#         'headersSize': 846,
#         'bodySize': 4560,
#         'comment': ''
#     },
#     'cache': {},
#     'timings': {
#         'comment': '',
#         'dns': 0,
#         'send': 0,
#         'ssl': 199,
#         'wait': 74,
#         'connect': 238,
#         'receive': 0,
#         'blocked': 0
#     },
#     'serverIPAddress': '************',
#     'comment': '',
#     'time': 314
# }, {
#     'pageref': 'abc',
#     'startedDateTime': '2024-12-26T14:55:08.453+08:00',
#     'request': {
#         'method': 'GET',
#         'url': 'https://pss.bdstatic.com/static/superman/js/lib/jquery-1-edb203c114.10.2.js',
#         'httpVersion': 'HTTP/1.1',
#         'cookies': [],
#         'headers': [],
#         'queryString': [],
#         'headersSize': 576,
#         'bodySize': 0,
#         'comment': ''
#     },
#     'response': {
#         'status': 200,
#         'statusText': 'OK',
#         'httpVersion': 'HTTP/1.1',
#         'cookies': [],
#         'headers': [],
#         'content': {
#             'size': 0,
#         },
#         'redirectURL': '',
#         'headersSize': 897,
#         'bodySize': 43711,
#         'comment': ''
#     },
#     'cache': {},
#     'timings': {
#         'comment': '',
#         'dns': -1,
#         'send': 0,
#         'ssl': -1,
#         'wait': 45,
#         'connect': -1,
#         'receive': 18,
#         'blocked': -1
#     },
#     'serverIPAddress': '************',
#     'comment': '',
#     'time': 64
# }, {
#     'pageref': 'abc',
#     'startedDateTime': '2024-12:55:08.459+08:00',
#     'request': {
#         'method': 'GET',
#         'url': 'https://www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png',
#         'httpVersion': 'HTTP/1.1',
#         'cookies': [],
#         'headers': [],
#         'queryString': [],
#         'headersSize': 766,
#         'bodySize': 0,
#         'comment': ''
#     },
#     'response': {
#         'status': 200,
#         'statusText': 'OK',
#         'httpVersion': 'HTTP/1.1',
#         'cookies': [],
#         'headers': [],
#         'content': {
#             'size': 0,
#         },
#         'redirectURL': '',
#         'headersSize': 463,
#         'bodySize': 15444,
#         'comment': ''
#     },
#     'cache': {},
#     'timings': {
#         'comment': '',
#         'dns': -1,
#         'send': 0,
#         'ssl': -1,
#         'wait': 26,
#         'connect': -1,
#         'receive': 0,
#         'blocked': -1
#     },
#     'serverIPAddress': '*************',
#     'comment': '',
#     'time': 27
# }, {
#     'pageref': 'abc',
#     'startedDateTime': '2024-12:55:08.461+08:00',
#     'request': {
#         'method': 'GET',
#         'url': 'https://www.baidu.com/img/PCfb_5bf082d29588c07f842ccde3f97243ea.png',
#         'httpVersion': 'HTTP/1.1',
#         'cookies': [],
#         'headers': [],
#         'queryString': [],
#         'headersSize': 766,
#         'bodySize': 0,
#         'comment': ''
#     },
#     'response': {
#         'status': 200,
#         'statusText': 'OK',
#         'httpVersion': 'HTTP/1.1',
#         'cookies': [],
#         'headers': [],
#         'content': {
#             'size': 0,
#         },
#         'redirectURL': '',
#         'headersSize': 463,
#         'bodySize': 24774,
#         'comment': ''
#     },
#     'cache': {},
#     'timings': {
#         'comment': '',
#         'dns': 0,
#         'send': 0,
#         'ssl': 437,
#         'wait': 26,
#         'connect': 466,
#         'receive': 6,
#         'blocked': 0
#     },
#     'serverIPAddress': '*************',
#     'comment': '',
#     'time': 499
# }]
#
#
# def traverse_and_extract(data):
#     result = []
#     for item in data:
#         url = item['request']['url']  # 从 request 中提取 url
#         status = item['response']['status']  # 从 response 中提取 status
#         result.append((url, status))
#     return result
#
#
# # 调用函数进行遍历和提取
# print(traverse_and_extract(data))
#
# from datetime import datetime
#
# date_str = '2025年4月22日 15:01'
# # 定义原始日期时间字符串的格式
# original_format = '%Y年%m月%d日 %H:%M'
# # 定义目标日期时间字符串的格式
# target_format = '%Y-%m-%d %H:%M'
# # 将字符串解析为datetime对象
# dt = datetime.strptime(date_str, original_format)
# # 将datetime对象格式化为目标字符串
# formatted_date = dt.strftime(target_format)
#
# print(formatted_date)

import imaplib
import email

# 邮箱账号和授权码
email_user = '<EMAIL>'
email_pass = 'LMhU6pw4cG6GGssk'

# 连接到 163 邮箱的 IMAP 服务器
mail = imaplib.IMAP4_SSL('imap.163.com')

try:
    # 登录邮箱
    mail.login(email_user, email_pass)
    # 选择收件箱
    status, response = mail.select('INBOX')
    if status == 'OK':
        print("成功选择收件箱")
        # 定义搜索条件，这里以发件人为例
        search_criteria = 'FROM "<EMAIL>"'
        # 搜索符合条件的邮件
        status, data = mail.search(None, search_criteria)

        if status == 'OK':
            mail_ids = data[0].split()
            if mail_ids:
                print("存在符合条件的邮件。")
                # 可以进一步处理邮件，例如获取邮件内容
                for mail_id in mail_ids:
                    status, msg_data = mail.fetch(mail_id, '(RFC822)')
                    if status == 'OK':
                        raw_email = msg_data[0][1]
                        msg = email.message_from_bytes(raw_email)
                        print(f"邮件主题: {msg['Subject']}")
            else:
                print("未找到符合条件的邮件。")
        else:
            print("搜索邮件时出现错误。")
        # 关闭当前选择的邮箱文件夹
        mail.close()
    else:
        print(f"选择收件箱失败: {response}")
except imaplib.IMAP4.error as e:
    print(f"IMAP 错误: {e}")
finally:
    # 注销登录
    mail.logout()

