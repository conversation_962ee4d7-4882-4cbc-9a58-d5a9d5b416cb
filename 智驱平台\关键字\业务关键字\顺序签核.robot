*** Settings ***
Library           SeleniumLibrary
Resource          ../../元素/Athena平台元素.robot
Resource          ../../配置/Athena平台.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/web.robot
Resource          ../系统关键字/公共方法.robot
Resource          Athena平台.robot
Resource          Athena平台控件.robot

*** Keywords ***
手动发起项目（职能）
    [Arguments]    ${topmeu}    ${meu}    ${reason}    ${id}    ${key}    ${status}    ${order}
    点击顶部菜单    ${topmeu}
    点击右侧菜单    ${meu}
    发起一般项目（职能）    商品退货（职能）    ${reason}    ${id}    ${key}    ${status}    ${order}

发起一般项目（职能）
    [Arguments]    ${task}    ${reason}    ${id}    ${key}    ${status}    ${order}
    点击    //*[contains(text(),'${task}')]
    输入    //input[@placeholder="退货原因"]    ${reason}
    输入    //input[@placeholder="退货编号"]    ${id}
    输入    //input[@placeholder="商品退货主键"]    ${key}
    输入    //input[@placeholder="状态"]    ${status}
    输入    //input[@placeholder="订单编号"]    ${order}
    Sleep    3
    点击    //span[contains(text(),'提交')]
    点击    //span[contains(text(),'确定')]
    Sleep    30

商品退货
    [Arguments]    ${key}
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'我的任务')]
    任务/项目名称搜索    ${key}
    Sleep    5
    点击    //span[contains(text(),'${key}')]
    Sleep    10
    当前页面可见    //span[contains(text(),'提交')]
    点击    //span[contains(text(),'提交')]
    点击    //span[contains(text(),'确定')]
    Comment    Sleep    10
#    当前页面可见字符    上传成功
    Sleep    30
    当前页面可见    //p[contains(text(),'-提交-')]
    点击    //div[contains(text(),'查看流程进度')]
    当前页面可见    //div[contains(text(),'待审核')]
    Sleep    5

职能签核
    [Arguments]    ${key}
    跳转网页    /todo/task
    Sleep    5
    刷新页面
    Sleep    5
    点击    //div[contains(text(),'我的任务')]
    任务/项目名称搜索    ${key}
    Sleep    5
    点击    //span[contains(text(),'${key}')]
    Sleep    10
    当前页面可见    //div[@class="task-content-action"]/div/div/div/button[2]/span
    点击    //div[@class="task-content-action"]/div/div/div/button[2]/span
    点击    //span[contains(text(),'确定')]
    Sleep    5
    当前页面包含元素    //p[contains(text(),'同意')]
    Sleep    5

手动发起项目（主管）
    [Arguments]    ${topmeu}    ${meu}    ${key}
    点击顶部菜单    ${topmeu}
    点击右侧菜单    ${meu}
    发起一般项目（主管）    退货签收    ${key}    ${key}    ${key}    ${key}

发起一般项目（主管）
    [Arguments]    ${task}    ${order}    ${id}    ${key}    ${reson}
    点击    //*[contains(text(),'${task}')]
    输入    //input[@placeholder="订单编号"]    ${order}
    输入    //input[@placeholder="退货申请编号"]    ${id}
    输入    //input[@placeholder="退货签收主键"]    ${key}
    输入    //input[@placeholder="退货原因"]    ${reson}
    Sleep    3
    点击    //span[contains(text(),'提交')]
    点击    //span[contains(text(),'确定')]
    Sleep    30

商品发货
    [Arguments]    ${key}
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'我的任务')]
    任务/项目名称搜索    ${key}
    Sleep    5
    点击    //span[contains(text(),'${key}')]
    Sleep    10
    当前页面可见    //span[contains(text(),'提交')]
    点击    //span[contains(text(),'提交')]
    点击    //span[contains(text(),'确定')]
#    当前页面可见字符    上传成功
    Sleep    30
    当前页面可见    //p[contains(text(),'-提交-')]
    点击    //div[contains(text(),'查看流程进度')]
    当前页面可见    //div[contains(text(),'待审核')]
    Sleep    5
    关闭浏览器

商品退回签核
    [Arguments]    ${key}
    跳转网页    /todo/task
    Sleep    5
    刷新页面
    Sleep    5
    点击    //div[contains(text(),'我的任务')]
    任务/项目名称搜索    ${key}
    Sleep    5
    点击    //span[contains(text(),'${key}')]
    Sleep    10
    当前页面可见    //div[@class="task-content-action"]/div/div/div/button[2]/span
    点击    //div[@class="task-content-action"]/div/div/div/button[2]/span
    点击    //span[contains(text(),'确定')]
    Sleep    5
    当前页面包含元素    //p[contains(text(),'同意')]
    Sleep    5
    关闭浏览器

手动发起项目（或签）
    [Arguments]    ${topmeu}    ${meu}    ${key}
    点击顶部菜单    ${topmeu}
    点击右侧菜单    ${meu}
    发起一般项目（主管）    退货成功    ${key}    ${key}    ${key}    ${key}

发起一般项目（或签）
    [Arguments]    ${task}    ${key}    ${id}    ${order}    ${reson}    ${sgno}
    点击    //*[contains(text(),'${task}')]
    输入    //input[@placeholder="退货签收主键"]    ${key}
    输入    //input[@placeholder="退货申请编号"]    ${id}
    输入    //input[@placeholder="订单编号"]    ${order}
    输入    //input[@placeholder="退货原因"]    ${reson}
    输入    //input[@placeholder="签收编号"]    ${sgno}
    Sleep    3
    点击    //span[contains(text(),'提交')]
    点击    //span[contains(text(),'确定')]
    Sleep    10

多人或签（职能）
    [Arguments]    ${key}
    跳转网页    /todo/task
    点击    //div[contains(text(),'我的任务')]
    任务/项目名称搜索    ${key}
    点击    //span[contains(text(),'${key}')]
    当前页面可见    //div[@class="task-content-action"]/div/div/div/button[2]/span
    点击    //div[@class="task-content-action"]/div/div/div/button[2]/span
    点击    //span[contains(text(),'确定')]
    当前页面包含元素    //p[contains(text(),'同意')]
    点击    //div[contains(text(),'查看流程进度')]
    当前页面包含元素    //div[contains(text(),'无需审核')]

多人或签（人员）
    [Arguments]    ${key}
    跳转网页    /todo/task
    点击    //div[contains(text(),'我的任务')]
    任务/项目名称搜索    ${key}
    点击    //span[contains(text(),'${key}')]
    当前页面可见    //div[@class="task-content-action"]/div/div/div/button[2]/span
    点击    //div[@class="task-content-action"]/div/div/div/button[2]/span
    点击    //span[contains(text(),'确定')]
    当前页面包含元素    //p[contains(text(),'同意')]
    点击    //div[contains(text(),'查看流程进度')]
    当前页面包含元素    //div[contains(text(),'无需审核')]
    关闭浏览器

多人或签（部门)
    [Arguments]    ${key}
    Comment    #团队任务
    Comment    跳转网页    /todo/team-tasks
    Comment    Sleep    5
    Comment    任务/项目名称搜索    ${key}
    Comment    Sleep    5
    Comment    当前页面包含元素    //span[contains(text(),' 【hl_test】多人或签（职能')]
    Comment    当前页面包含元素    //span[contains(text(),' 【黄蕾】多人或签（职能')]
    Comment    #团队项目
    Comment    跳转网页    /team-projects
    Comment    Sleep    5
    Comment    任务/项目名称搜索    ${key}
    Comment    Sleep    5
    Comment    当前页面包含元素    //div[@class="card-item-name"]/span/span[contains(text(),'退货成功')]
    #我的任务
    跳转网页    /todo/task
    点击    //div[contains(text(),'我的任务')]
    任务/项目名称搜索    ${key}
    点击    //span[contains(text(),'${key}')]
    当前页面可见    //div[@class="task-content-action"]/div/div/div/button[2]/span
    点击    //div[@class="task-content-action"]/div/div/div/button[2]/span
    点击    //span[contains(text(),'确定')]
    当前页面包含元素    //p[contains(text(),'同意')]
    关闭浏览器
