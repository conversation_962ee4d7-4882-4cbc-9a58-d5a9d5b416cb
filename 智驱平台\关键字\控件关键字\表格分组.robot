*** Settings ***
Library           SeleniumLibrary
Resource          ../系统关键字/web.robot
Resource          ../../元素/Athena平台元素.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../系统关键字/接口.robot
Resource            开窗控件.robot
Resource          ../业务关键字/首页.robot
Resource          ../业务关键字/待办.robot
Resource          ../业务关键字/Athena平台.robot

*** Keywords ***
发起表格分组项目卡
    点击顶部菜单    全部
    点击    //span[contains(text(),'发起项目')]
    当前页面可见   //div[@id='card_caigoushenqin_mainline_project_0001']
    点击    //div[@id='card_caigoushenqin_mainline_project_0001']
    Sleep   10
    新增项目数据  product_name  total_amount  quantity  owner_dept_name  owner_emp_name  YSL  PaaS平台事業處  郑苏振  
   #提交
    当前页面可见  //button[@class='ath-btn ant-btn ant-btn-primary ant-btn-lg ng-star-inserted']
    点击  //button[@class='ath-btn ant-btn ant-btn-primary ant-btn-lg ng-star-inserted']
    Sleep   3
    当前页面可见  //div[@class='confirm-content']
    点击  //ath-modal-confirm-container/div/div/div/div/div[2]/button[2]
    当前页面可见字符  发起成功
    #进入项目卡详情
    判断后跳转到目标页面    /todo/project
    Sleep   10
    视图模式设定    卡片
    点击    //div[@class='toolbar']/app-todo-filter
    点击  //button[@class='ath-btn ant-btn ant-btn-default']/span[text()='重置']
    #筛选采购申请项目卡
    输入  //div/app-todo-comm-filter/div[1]/app-todo-filter-item/ath-tree-select/div/nz-select-search/input  采购申请
    元素存在则点击  //div/span/span[text()='采购申请']
    点击  //div/app-todo-comm-filter/div[1]/app-todo-filter-item/ath-tree-select/div/nz-select-search/input 
    输入    //div[@class="filter-list-container ng-star-inserted"]/app-todo-comm-filter/div[2]/app-todo-filter-item/ath-tree-select/div/nz-select-search/input    未读
    鼠标悬停    //span[@class='font-highlight'][text()='未读']
    点击  //span[@class='font-highlight'][text()='未读']
    当前页面可见    //div[contains(@class,'athena-selected-panel')]
    点击    //button[@class='ath-btn ant-btn ant-btn-primary']
    #根据创建时间降序排序
    js点击    //span[contains(text(),'排序')]
    点击    //span[contains(text(),'重置')]
    Sleep    5
    #删除排序条件
    #js点击    //span[contains(text(),'排序')]
    点击    //div[@class='cdk-drop-list sort-item-list']/div[1]/*[name()='svg']
    点击    //div[@class='cdk-drop-list sort-item-list']/div[1]/*[name()='svg']
    点击    //div[@class='cdk-drop-list sort-item-list']/div[1]/*[name()='svg']
    点击    //div[@class='cdk-drop-list sort-item-list']/div[1]/*[name()='svg']
    #降序
    #js点击    //span[contains(text(),'排序')]
    元素存在则点击     //div/ath-tab-normal-title/span[text()='降序']
    点击    //span[contains(text(),'确定')]
    Sleep    5
    当前页面可见  //div[@class='task-outer-content ng-star-inserted']/div[1]
    点击   //div[@class='task-outer-content ng-star-inserted']/div[1]
    Sleep   10 
    #选择最新的项目卡点击进入详情
    当前页面可见   //div[@class='dynamic-full-cell-render']
    点击   //span[@class='group-fold ag-icon ag-icon-fold ng-star-inserted']
    当前页面可见    //span[@class='group-fold ag-icon ag-icon-fold ng-star-inserted group-unfold']
    点击   //span[@class='group-fold ag-icon ag-icon-fold ng-star-inserted group-unfold']
    当前页面可见   //div[@class='dynamic-full-cell-render']
    鼠标悬停   //li[@class='ath-tab-menus-li ng-star-inserted ath-tab-menus-active']/i[@class='anticon ath-tab-menus-item-close-btn anticon-close']
    点击   //li[@class='ath-tab-menus-li ng-star-inserted ath-tab-menus-active']/i[@class='anticon ath-tab-menus-item-close-btn anticon-close']


新增项目数据
    [Arguments]   ${product_name}    ${total_amount}  ${quantity}  ${owner_dept_name}   ${owner_emp_name}  ${owkey1}  ${owkey2}   ${owkey3} 
    #发起项目-采购申请-必填字段1
    鼠标悬停  //div[@col-id='${product_name}'][@role='gridcell']
    当前页面可见   //div[@col-id='${product_name}']//cell-renderer/div[1]/div/div/dynamic-operation-editor/div/div[1]/div/span[2]/i
    点击  //div[@col-id='${product_name}']//cell-renderer/div[1]/div/div/dynamic-operation-editor/div/div[1]/div/span[2]/i
    Sleep   5
    单选开窗  ${owkey1}
    #发起项目-采购申请-必填字段2
    鼠标悬停  //div[@col-id='${total_amount}'][@role='gridcell']
    点击    //div[@col-id='${total_amount}'][@role='gridcell']
    web.输入    //div/input[contains(@class,'ant-input-number-input')]    299
    #发起项目-采购申请-必填字段3
    鼠标悬停  //div[@col-id='${quantity}'][@role='gridcell']
    点击  //div[@col-id='${quantity}'][@role='gridcell']
    web.输入   //ath-input-text/ath-input-group/div[2]/section/input  20
    Press Keys   //ath-input-text/ath-input-group/div[2]/section/input   ENTER
    #发起项目-采购申请-必填字段4
    鼠标悬停  //div[@col-id='${owner_dept_name}'][@role='gridcell']
    鼠标悬停  //div[@col-id='${owner_dept_name}']//dynamic-operation-editor/div/div[1]/div/span[2]
    点击  //div[@col-id='${owner_dept_name}']//dynamic-operation-editor/div/div[1]/div/span[2]
    Sleep   5
    单选开窗  ${owkey2}
    #发起项目-采购申请-必填字段5
    鼠标悬停  //div[@col-id='${owner_emp_name}'][@role='gridcell']
    当前页面可见   //*[@col-id='${owner_emp_name}']//cell-renderer/div[1]/div/div/dynamic-operation-editor/div/div[1]/div/span[2]/i
    点击  //*[@col-id='${owner_emp_name}']//cell-renderer/div[1]/div/div/dynamic-operation-editor/div/div[1]/div/span[2]/i
    Sleep   5
    单选开窗  ${owkey3}
    #点击新增
    鼠标悬停  //button/span[contains(text(),'新增')]
    点击  //button/span[contains(text(),'新增')]
    #新增表格数据
    鼠标悬停  //cell-renderer[@class='dynamic-cell-render ath-table-cell-render cell-single-control canEditor ng-star-inserted editor-focus']
    当前页面可见   //cell-renderer[@class='dynamic-cell-render ath-table-cell-render cell-single-control canEditor ng-star-inserted editor-focus']/div[1]/div/div/dynamic-operation-editor/div/div[1]/div/span[2]/i
    点击  //cell-renderer[@class='dynamic-cell-render ath-table-cell-render cell-single-control canEditor ng-star-inserted editor-focus']/div[1]/div/div/dynamic-operation-editor/div/div[1]/div/span[2]/i
    单选开窗  ${owkey1}
    #发起项目-采购申请-必填字段2
    鼠标悬停  //div[@row-index='1']/div[2]/div/span/cell-renderer
    点击  //div[@row-index='1']/div[2]/div/span/cell-renderer
    web.输入    //div/input[contains(@class,'ant-input-number-input')]   399
    #发起项目-采购申请-必填字段3
    鼠标悬停  //div[@row-index='1']/div[3]/div/span/cell-renderer
    点击  //div[@row-index='1']/div[3]/div/span/cell-renderer
    web.输入   //div[@row-index='1']/div[3]/div/span/cell-renderer/*//input  200
    Press Keys   //div[@row-index='1']/div[3]/div/span/cell-renderer/*//input   ENTER
    #发起项目-采购申请-必填字段4
    鼠标悬停  //div[@row-index='1']/div[4]/div/span/cell-renderer
    当前页面可见   //div[@row-index='1']/div[4]/div/span/cell-renderer/div[1]/div/div/dynamic-operation-editor/div/div[1]/div/span[2]/i
    点击  //div[@row-index='1']/div[4]/div/span/cell-renderer/div[1]/div/div/dynamic-operation-editor/div/div[1]/div/span[2]/i
    Sleep   5
    单选开窗  ${owkey2}
    #发起项目-采购申请-必填字段5
    鼠标悬停  //div[@row-index='1']/div[5]/div/span/cell-renderer
    当前页面可见   //div[@row-index='1']/div[5]/div/span/cell-renderer/div[1]/div/div/dynamic-operation-editor/div/div[1]/div/span[2]/i
    点击  //div[@row-index='1']/div[5]/div/span/cell-renderer/div[1]/div/div/dynamic-operation-editor/div/div[1]/div/span[2]/i
    Sleep   5
    单选开窗  ${owkey3}




















   



