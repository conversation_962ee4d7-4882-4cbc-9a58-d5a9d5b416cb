*** Settings ***
Resource          ../../配置/域名.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../业务关键字/接口域名.robot
Resource          ../系统关键字/web.robot
Library           SeleniumLibrary
Library           Collections
Library           OperatingSystem
Library           BrowserMobProxyLibrary

*** Keywords ***
环境设定
    Set Selenium Timeout    180 seconds    #全局超时时间设定
    Set Selenium Page Load Timeout    180
    #如果apiScan没传此处默认为false
    ${apiScan}    Get Variable Value    ${apiScan}    False  # 尝试获取变量${a}的值，若不存在则返回default_value
    Set Global Variable    ${apiScan}
    ${browser}    Get Variable Value    ${browser}    gc  # 尝试获取变量${a}的值，若不存在则返回default_value
    Set Global Variable    ${browser}
    ${ENV}    Get Variable Value    ${ENV}    huawei.test  # 尝试获取变量${a}的值，若不存在则返回default_value
    Set Global Variable    ${ENV}
#    ${status}    Run Keyword And Return Status    log    ${apiScan}
#    Run Keyword If    '${status}'=='False'    Set Global Variable    ${apiScan}    False
#    ${status}    Run Keyword And Return Status    log    ${browser}
#    Run Keyword If    '${status}'=='False'    Set Global Variable    ${browser}    gc
    #    ${system}    GetSystemType
    ${system}=    Evaluate    platform.system()    modules=platform
    #启动前kill掉这个2个端口占用的进程，处理因异常退出导致的代理程序进程没被关闭
    #条件中的and必须小写
    Run Keyword If    '${system}'=='Darwin' and '${apiScan}'=='True'    Run    lsof -t -i :9090 | xargs kill -9    ELSE    log    Windows部分待完成
    Run Keyword If    '${system}'=='Darwin' and '${apiScan}'=='True'    Run    lsof -t -i :9100 | xargs kill -9    ELSE    Log    Windows部分待完成
    #设置字符类型
    #设置开发平台环境变量
    Log    ${ENV}
    ${PLATFORM_ENV}    Set Variable If    '${ENV}'=='paas'    ${开发平台域名}[paas]    '${ENV}'=='huawei.test'    ${开发平台域名}[huaweitest]    '${ENV}'=='microsoft.test'    ${开发平台域名}[microsofttest]    '${ENV}'=='huawei.prod'    ${开发平台域名}[huaweiprod]    '${ENV}'=='microsoft.prod'    ${开发平台域名}[microsoftprod]
    ${ATHENA_ENV}    Set Variable If    '${ENV}'=='paas'    ${Athena平台域名}[paas]    '${ENV}'=='huawei.test'    ${Athena平台域名}[huaweitest]    '${ENV}'=='microsoft.test'    ${Athena平台域名}[microsofttest]    '${ENV}'=='huawei.prod'    ${Athena平台域名}[huaweiprod]    '${ENV}'=='microsoft.prod'    ${Athena平台域名}[microsoftprod]     '${ENV}'=='muihuawei.test'    ${Athena平台域名}[muihuaweitest]    '${ENV}'=='private.test'     ${Athena平台域名}[privatetest]    '${ENV}'=='pressure'     ${Athena平台域名}[pressure]
    ${DIGIWIN_CLOULD}    Set Variable If    '${ENV}'=='paas'    ${鼎捷云域名}[paas]    '${ENV}'=='huawei.test'    ${鼎捷云域名}[huaweitest]    '${ENV}'=='microsoft.test'    ${鼎捷云域名}[microsofttest]    '${ENV}'=='huawei.prod'    ${鼎捷云域名}[huaweiprod]    '${ENV}'=='microsoft.prod'    ${鼎捷云域名}[microsoftprod]
    #如果不传这个参数，则默认用ENV这个参数下设定的环境，解决开发平台和athena平台不是同一个环境的场景
    ${status}    Run Keyword And Return Status    log    ${ENV_PLA}
    IF    '${status}'!='False'
        ${PLATFORM_ENV}    Set Variable If    '${ENV_PLA}'=='paas'    ${开发平台域名}[paas]    '${ENV_PLA}'=='huawei.test'    ${开发平台域名}[huaweitest]    '${ENV_PLA}'=='microsoft.test'    ${开发平台域名}[microsofttest]    '${ENV_PLA}'=='huawei.prod'    ${开发平台域名}[huaweiprod]
    END
    #必须设置为全局变量
    Set Global Variable    ${PLATFORM_ENV}
    Set Global Variable    ${ATHENA_ENV}
    Set Global Variable    ${DIGIWIN_CLOULD} 
    #通过毫秒时间戳生成全局唯一标识
    ${唯一标识}    生成秒时间戳
    Set Global Variable    ${唯一标识}
    ${长唯一标识}    生成毫秒时间戳
    Set Global Variable    ${长唯一标识}
    Run Keyword If    '${apiScan}'=='true'    启动代理服务
    #此处接口域名初始化
    接口域名

启动代理服务
    [Arguments]
    ${browsermob-proxy}    Set Variable    ${CURDIR}\/..\/..\/..${/}browsermob-proxy/bin/browsermob-proxy
    Start Local Server    ${browsermob-proxy}    {'port':9090}
    ## 创建代理
    ${BrowserMob_Proxy}    Create Proxy    {'port':9100}
    Set Global Variable    ${BrowserMob_Proxy}
    log    ${BrowserMob_Proxy.proxy}
    New Har    har

错误处理
    ${exist}    判断元素是否可见    //span[contains(text(),'查看明细')]    5
    Run Keyword If    ${exist}    错误截图

错误截图
    Log    环境:${ENV}
    Capture Page Screenshot
    点击    //span[contains(text(),'查看明细')]
    Capture Page Screenshot
    点击    //span[contains(text(),'一站式问题排查')]
    Get Location