*** Settings ***
Library           SeleniumLibrary
Resource          ../../元素/Athena平台元素.robot
Resource          ../../配置/Athena平台.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/web.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../业务关键字/PCC.robot
Resource          Athena平台.robot
Resource          Athena平台控件.robot

*** Keywords ***
手动发起项目2.0
    [Arguments]    ${purchase}    ${amount}
    点击顶部菜单    全部
    点击    //span[contains(text(),'发起项目')]
    点击    //span[contains(text(),'2.0应用功能测试')]
    输入    //input[@placeholder="请输入采购单号"]    ${purchase}
    输入    //input[@placeholder="请输入数量"]    ${amount}
    点击    //span[contains(text(),'提交')]
    点击    //span[contains(text(),'确定')]
    当前页面可见    //*[contains(text(),'发起成功')]

任务卡提交
    [Arguments]    ${taskname}    ${state}=null
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'我的任务')]
    Comment    点击    //span[contains(text(),'搜索')]
    Comment    Ente Text    ${taskname}
    Comment    点击    //span[@type='addon']
    任务/项目名称搜索    ${taskname}    ${state}
    Sleep    5
    #点击    //div[@class='activity-item']
    点击卡片    ${taskname}
    Sleep    3
    当前页面可见    //span[contains(text(),'提交')]
    点击    //span[contains(text(),'提交')]
    点击    //span[contains(text(),'确定')]
    当前页面不可见字符    请稍等