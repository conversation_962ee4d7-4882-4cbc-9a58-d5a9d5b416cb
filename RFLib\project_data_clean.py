#!/usr/local/bin/python3
# -*- coding: utf8 -*-
# Author: cjm
import requests
import json
import time

#通过查询项目列表查出所有项目，然后调项目关闭接口关闭
# 要请求的URL
url = "https://atmc-test.apps.digiwincloud.com.cn/api/atmc/v1/project/list?clientId=DD2CAD59C5E8D9CFA4DED75A1400A689"
# 数据清理接口
retryUrl = "https://troubleshoot-test.apps.digiwincloud.com.cn/restful/standard/troubleshoot/api/scene/execute/fix"

#此处需要权限token
token = "50b70f5e-d66d-4ad6-88aa-d3f15650a82f"

# 设置查任务卡数据查询接口请求头
headersGet = {
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:132.0) Gecko/20100101 Firefox/132.0",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "token": "30466d45-02ed-4b03-b28a-30d30f1c87e1",
    "routerKey": "AthenaAutoTest"
}
# 清理数据接口请求头
headersPost = {
    "Content-Type": "application/json",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "token": token
}

try:
    # 发送带请求头的GET请求
    response = requests.get(url, headers=headersGet)
    # 检查请求是否成功，状态码200表示成功
    if response.status_code == 200:
        print("请求成功！")
        print("响应内容：")
        #print(response.text)
    else:
        print(f"请求失败，状态码：{response.status_code}")

    data = response.json()
    dataCount = len(data['response'])
    print(dataCount)
    # 如果返回的数据是列表，遍历列表中的每个元素
    for item  in range(dataCount):
        id = data['response'][item]['id']
        postdata = {"id": 382, "tenantId": "AthenaAutoTest", "repairRemark": "测试",
                    "reqParams": [{"tenantId": "AthenaAutoTest", "projectCardId": "id"}]}
        postdata['reqParams'][0]["projectCardId"] = id
        postdata['reqParams'] = json.dumps(postdata['reqParams'])
        postdata = json.dumps(postdata)
        time.sleep(0.1)
        response = requests.post(retryUrl, data=postdata,headers=headersPost)
        if response.status_code == 200:
            print("请求成功！")
            print("响应内容：")
            print(response.text)
        else:
            print(f"请求失败，状态码：{response.status_code}")
            print(response.text)
except requests.exceptions.RequestException as e:
    print(f"请求发生异常：{e}")