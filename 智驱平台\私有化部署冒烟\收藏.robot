*** Settings ***
Documentation     郑苏振
Library           SeleniumLibrary
Resource          ../关键字/业务关键字/公共方法.robot
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/系统关键字/公共方法.robot
Resource          ../关键字/业务关键字/PCC.robot
Resource          ../关键字/业务关键字/智能入口.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/收藏.robot
Test Setup        Run Keywords    环境设定
Test Teardown     Close Browser


*** Test Cases ***
任务卡收藏
    #当责者账号
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #当责者/执行者名称,新建一级计划时需要按名称选择人员
    ${ownerUsernameCN}    Set Variable If    '${ENV}'=='huawei.test'    测试环境自动化测试SD    '${ENV}'=='huawei.prod'    生产华为环境自动化测试SD    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试智驱入口
    ${exUsernameCN}    Set Variable If    '${ENV}'=='huawei.test'    测试环境自动化测试智驱平台    '${ENV}'=='huawei.prod'    生产华为环境自动化测试智驱平台    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试智驱平台
    #执行者账号
    ${exUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02
    ${exPassword}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02
    #签核者账号
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #加签人
    ${additionalSignatory}    Set Variable If    '${ENV}'=='huawei.test'    15722677434测试环境自动化测试SD    '${ENV}'=='huawei.prod'    15722677434生产华为环境自动化测试智驱入口    '${ENV}'=='microsoft.prod'    1591310641生产微软环境自动化测试智驱入口
    Set Global Variable    ${projectname}    ${唯一标识}
    Set Global Variable    ${taskname}    ${唯一标识}
    登录Athena平台    ${ownerUsername}    ${ownerPassword}
    语言设定    简体
    Sleep   10
    手动发起项目    ${projectname}    项目无需签核    BB    3    2024/09/15
    Sleep   30
    新建一级计划    ${projectname}    ${taskname}    2024/09/15    2024/09/15    ${ownerUsernameCN}    ${exUsernameCN}     无需签核
    启动项目
    关闭项目/任务卡详情页签
    任务详情添加收藏   ${taskname}
    查询任务卡快照  ${taskname}
    根据时间筛选快照    1      30
    根据关键字搜索快照     ${taskname}
    进入快照详情    ${taskname}
    删除快照

项目卡收藏
    #当责者账号
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestSd01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestSd01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #当责者/执行者名称,新建一级计划时需要按名称选择人员
    ${ownerUsernameCN}    Set Variable If    '${ENV}'=='huawei.test'    测试环境自动化测试SD    '${ENV}'=='huawei.prod'    生产华为环境自动化测试KM    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试智驱平台
    ${exUsernameCN}    Set Variable If    '${ENV}'=='huawei.test'    测试环境自动化测试智驱平台    '${ENV}'=='huawei.prod'    生产华为环境自动化测试智驱入口    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试智驱入口
    #执行者账号
    ${exUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02
    ${exPassword}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02
    #签核者账号
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='huawei.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='huawei.prod'    ProdWrAthenaAutoTestAi01
    #加签人
    ${additionalSignatory}    Set Variable If    '${ENV}'=='huawei.test'    15722677434测试环境自动化测试SD    '${ENV}'=='huawei.prod'    15722677434生产华为环境自动化测试智驱入口    '${ENV}'=='microsoft.prod'    1591310641生产微软环境自动化测试智驱入口
    Set Global Variable    ${projectname}    ${唯一标识}
    Set Global Variable    ${taskname}    ${唯一标识}
    登录Athena平台    ${ownerUsername}    ${ownerPassword}
    语言设定    简体
    Sleep   10
    手动发起项目    ${projectname}    项目无需签核    BB    3    2024/09/15
    Sleep   10
    项目卡详情添加收藏  ${projectname}
    查询项目卡快照  ${projectname}
    根据时间筛选快照    1      30
    根据关键字搜索快照     ${projectname}
    进入快照详情    ${projectname}
    删除快照
# 测试收藏
#     登录Athena平台    TestAthenaAutoTestAi001    TestAthenaAutoTestAi001
#     语言设定    简体
#     Sleep   30
#     根据关键字搜索快照    1729677251
#     #切换列表模式
#     进入快照详情    1729677251
    
