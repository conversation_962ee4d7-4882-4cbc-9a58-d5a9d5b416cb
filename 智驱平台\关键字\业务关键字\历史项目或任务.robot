*** Settings ***
Library           SeleniumLibrary
Resource          ../系统关键字/web.robot
Resource          ../系统关键字/公共方法.robot
Resource          Athena平台控件.robot
Resource          Athena平台.robot


*** Keywords ***
进入历史项目/任务菜单
    点击顶部菜单    全部
    Sleep    3
    点击    //div[contains(text(),'历史项目/任务')]

数据导出
    点击    //span[contains(text(),'数据导出')]
#    Clear Element Text    //div[@class='ath-input-content-box ng-star-inserted']//input
#    输入    //div[@class='ath-input-content-box ng-star-inserted']//input    ${唯一标识}
    点击    //span[contains(text(),'确定')]

导出记录检查
    点击    //div[@class='tools ng-star-inserted']  # 导出记录
    点击    //div[contains(text(),'查看所有导出记录 ')]
    点击    //nz-select-placeholder[@class='ant-select-selection-placeholder ng-star-inserted']
    点击    //span[contains(text(),'失败')]
    点击    //span[contains(text(),'搜索')]
    Page Should Contain Element    //span[contains(text(),'暂无数据')]


