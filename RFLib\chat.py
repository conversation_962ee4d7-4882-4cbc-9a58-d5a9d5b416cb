import requests
import json

# 企业微信群机器人 Webhook 地址
webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=28698546-b4f6-430b-ba90-987f9ddec431"

# 要 @ 的成员手机号列表
mentioned_phone_list = ["18994129019"]

# 消息内容
message_content = "你好，你的UI自动化用例今日执行失败未处理，扣1分，你还剩99分！"

# 构建消息
message = {
    "msgtype": "text",
    "text": {
        "content": "你好，你的UI自动化用例今日执行失败未处理，扣1分，你还剩99分！",
        "mentioned_mobile_list": "18994129019"
    }
}

# 设置请求头
headers = {'Content-Type': 'application/json'}

# 发送 POST 请求
try:
    response = requests.post(webhook_url, headers=headers, data=json.dumps(message))
    response.raise_for_status()  # 检查响应状态
    result = response.json()
    if result.get("errcode") == 0:
        print("消息发送成功")
    else:
        print(f"消息发送失败，错误码: {result.get('errcode')}，错误信息: {result.get('errmsg')}")
except requests.RequestException as e:
    print(f"请求出错: {e}")