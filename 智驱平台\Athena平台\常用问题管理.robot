*** Settings ***
Documentation     沈阳
Suite Setup       Run Keywords    环境设定
Test Teardown     Run Keywords    错误处理    关闭浏览器
Resource          ../关键字/业务关键字/智能入口.robot
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/业务关键字/待办.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/顺序签核.robot
Resource          ../关键字/业务关键字/首页.robot


*** Keywords ***


*** Test Cases ***
登录娜娜
    ${username}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${password}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${tenant}    Set Variable If    '${ENV}'=='pressure'     自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'     自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    ${url}      Set Variable If     '${ENV}'=='paas'    https://ania-web-paas.digiwincloud.com.cn/login    '${ENV}'=='huawei.test'     https://ania-web-test.digiwincloud.com.cn/login      '${ENV}'=='microsoft.prod'    https://ania-web.digiwincloud.com.cn/login

    Set Global Variable    ${username}
    Set Global Variable    ${password}
    打开网页    ${url}    ${browser}
    IF    '${ENV}'=='microsoft.prod'
        当元素不可见则刷新页面    1    //input[@name="userId"]    10
    END
    #如果登录页白屏就尝试刷新2次
    当元素不可见则刷新页面    2    //input[@name="userId"]
    输入    //input[@name="userId"]    ${username}
    输入    //input[@name="password"]    ${password}
    点击    //div[@class="action"]/button/span
    Sleep    2

    #全局设置统一入口
    鼠标悬停    //img[@src='assets/img/side/setting.svg']
    Sleep    1
    点击    //span[contains(text(),'设置')]
    Sleep    1
    #会话-常用问题
    鼠标悬停    //body/app-root/app-main-layout[@class='ng-star-inserted']/div[@class='main-layout']/div[@class='main-content']/div[@class='page-container']/div[@class='page-content']/app-setting[@class='ng-star-inserted']/div[@class='setting-wrap']/div[@class='content']/div[@class='ng-star-inserted']/div[2]/div[1]/div[1]/div[1]
    Sleep    1
    点击    //span[contains(text(),'常用问题')]
    Sleep    1

    关闭浏览器
