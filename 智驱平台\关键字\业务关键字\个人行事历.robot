*** Settings ***
Library           SeleniumLibrary
Resource          ../../元素/开发平台元素.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../系统关键字/web.robot
Resource          ../../配置/域名.robot
Resource          Athena平台控件.robot
Library           Collections
Library           OperatingSystem

*** Keywords ***
新增个人行事历（未勾选已执行）
#    [Arguments]    ${name}
    ${name}    生成毫秒时间戳
    鼠标悬停    //td[@class='fc-daygrid-day fc-day fc-day-mon fc-day-past fc-day-other']//div[@class='fc-daygrid-day-frame fc-scrollgrid-sync-inner']/div[1]
    点击    //td[@class='fc-daygrid-day fc-day fc-day-mon fc-day-past fc-day-other']//div[@class='fc-daygrid-day-frame fc-scrollgrid-sync-inner']//div[@class='fc-daygrid-day-top']//a[@class='cell-add-task'][contains(text(),'新增任务')]
    输入    //input[@class='ant-input ath-input ng-untouched ng-pristine ng-invalid ng-star-inserted']    ${name}
    点击    //span[contains(text(),'提交')]
    当前页面可见字符    ${name}
#    -----标识-----


点击右侧菜单行事历
    [Arguments]    ${meu}
    点击    //span[@class='item-menu-title ng-star-inserted']//span[contains(text(),'${meu}')]
    当前页面可见字符    周一
    Sleep    10

删除已生成的行事历
    [Arguments]    ${name}
    点击    //span[contains(text(),'${name}')]
    点击    //span[contains(@class,'button font-class icondelete3 ng-star-inserted')]
    点击    //span[contains(text(),'确定')]

新增个人行事历（勾选已执行）
    [Arguments]    ${name}    ${dscri}
    鼠标悬停    //td[@class='fc-daygrid-day fc-day fc-day-mon fc-day-past fc-day-other']//div[@class='fc-daygrid-day-frame fc-scrollgrid-sync-inner']/div[1]
    点击    //td[@class='fc-daygrid-day fc-day fc-day-mon fc-day-past fc-day-other']//div[@class='fc-daygrid-day-frame fc-scrollgrid-sync-inner']//div[@class='fc-daygrid-day-top']//a[@class='cell-add-task'][contains(text(),'新增任务')]
    输入    //input[@class='ant-input ath-input ng-untouched ng-pristine ng-invalid ng-star-inserted']    ${name}
    点击    //span[contains(text(),'已执行')]
    输入    //textarea[@placeholder="计划工作说明"]    ${dscri}
    点击    //span[contains(text(),'提交')]
    当前页面可见字符    ${name}

个人行事历视图展示
    [Arguments]    ${name}
    点击    //td[@class='fc-daygrid-day fc-day fc-day-mon fc-day-past fc-day-other']//div[@class='fc-daygrid-day-frame fc-scrollgrid-sync-inner']//div[@class='fc-daygrid-day-events']//div[@class='fc-daygrid-day-bottom']//a[@class="fc-daygrid-more-link fc-more-link"][contains(text(),'全部')]\n
    鼠标滑动到底部    ${name}
    点击    //span[contains(text(),'${name}')]
    Sleep    10

打开已创建的行事历
    [Arguments]    ${name}
    判断个人行事历是否超过5个    HL的自动化行事历
    点击    //span[contains(text(),'${name}')]

已完成个人行事历创建展示
    ${a}    生成毫秒时间戳
    新增个人行事历（勾选已执行）    ${a}    ${a}
    Comment    个人行事历视图展示    ${a}
    Comment    Sleep    10
    Comment    ${ab}    Get Text    //p[contains(text(),'已执行')]
    Comment    Log    $(ab)
    Comment    Should Be Equal    ${ab}    -已执行-

未完成个人行事历创建展示
    ${code}    生成毫秒时间戳
    ${name}    Set Variable    HL的行事历${code}
    Set Global Variable    ${name}
    Log    ${name}
    鼠标悬停    //*[contains(text(),'周一')]/following::div[@class='fc-daygrid-day-top'][1]
    点击    //*[contains(text(),'周一')]/following::div[@class='fc-daygrid-day-top'][1]//a[contains(text(),'新增任务')]
    输入    //input[@class='ant-input ath-input ng-untouched ng-pristine ng-invalid ng-star-inserted']    ${name}
    点击    //span[contains(text(),'提交')]
    Sleep    10
    ${Keywords}    Get Element Count    //td[@class='fc-daygrid-day fc-day fc-day-mon fc-day-past fc-day-other']//div[@class='fc-daygrid-day-frame fc-scrollgrid-sync-inner']//div[@class='fc-daygrid-day-events']//div[@class='fc-daygrid-day-bottom']//a[@class="fc-daygrid-more-link fc-more-link"][contains(text(),'全部')]
    Run Keyword If    '${Keywords}'=='0'
    ...    个人行事历编辑    ${name}
    ...    ELSE IF    '${Keywords}'!='0'    个人行事历视图编辑    ${name}
    Sleep    10
    ${Keywords}    Get Element Count    //td[@class='fc-daygrid-day fc-day fc-day-mon fc-day-past fc-day-other']//div[@class='fc-daygrid-day-frame fc-scrollgrid-sync-inner']//div[@class='fc-daygrid-day-events']//div[@class='fc-daygrid-day-bottom']//a[@class="fc-daygrid-more-link fc-more-link"][contains(text(),'全部')]
    Run Keyword If    '${Keywords}'=='0'
    ...    删除已生成的行事历    ${name}
    ...    ELSE IF    '${Keywords}'!='0'    个人行事历视图删除    ${name}
    Sleep    10

个人行事历编辑
    [Arguments]    ${Own}
    #编辑行事历
    js点击    //span[contains(@class,'button font-class iconbianjiicon ng-star-inserted')]
    输入    //textarea[@placeholder="计划工作说明"]    ${Own}
    点击    //span[contains(text(),'提交')]

上传附件
    [Arguments]    ${aaa}
    ${file}    Set Variable    ${CURDIR}${/}..${/}..${/}..${/}123.txt
    Comment    click element    //span[contains(text(),'请上传')]
    Sleep    10
    Choose File    //div[@class='ant-upload']//ath-upload-entrance-form[@class='ath-upload-entrance-form ng-star-inserted']//span[@class='ath-upload-text'][contains(text(),'请上传')]    ${file}
    Sleep    10

创建上传附件个人行事历
    [Arguments]    ${Name}
    Mouse Over    //td[@class='fc-daygrid-day fc-day fc-day-mon fc-day-past fc-day-other']//div[@class='fc-daygrid-day-frame fc-scrollgrid-sync-inner']
    点击    //td[@class='fc-daygrid-day fc-day fc-day-mon fc-day-past fc-day-other']//div[@class='fc-daygrid-day-frame fc-scrollgrid-sync-inner']//div[@class='fc-daygrid-day-top']//a[@class='cell-add-task'][contains(text(),'新增任务')]
    Sleep    10
    输入    //input[@class='ant-input ath-input ng-untouched ng-pristine ng-invalid ng-star-inserted']    ${Name}
    ${file}    Set Variable    ${CURDIR}${/}..${/}..${/}..${/}123.txt
    Comment    click element    //span[contains(text(),'请上传')]
    Sleep    10
    click element    //span[contains(text(),'请上传')]
    Sleep    20
    Sc    ${file}
    Sleep    20
    点击    //span[contains(text(),'提交')]
    Sleep    20

判断个人行事历是否超过5个
    [Arguments]    ${name}
    Sleep    10
    ${Keywords}    Get Element Count    //a[@class='fc-daygrid-more-link fc-more-link']
    Run Keyword If    '${Keywords}'=='0'
    ...    新增个人行事历（未勾选已执行）    ${name}
    Run Keyword If    '${Keywords}'!='0'
    ...    个人行事历视图展示    ${name}

编辑删除选中的行事历
    [Arguments]    ${name}
    ${Keywords}    Get Element Count    //td[@class='fc-daygrid-day fc-day fc-day-mon fc-day-past fc-day-other']//div[@class='fc-daygrid-day-frame fc-scrollgrid-sync-inner']//div[@class='fc-daygrid-day-events']//div[@class='fc-daygrid-day-bottom']//a[@class="fc-daygrid-more-link fc-more-link"][contains(text(),'全部')]
    Run Keyword If    '${Keywords}'=='0'
    ...    删除已生成的行事历    ${name}
    ...    Sleep    10
    ...    ELSE IF    '${Keywords}'!='0'    个人行事历视图删除    ${name}

个人行事历视图编辑
    [Arguments]    ${name}
    点击    //td[@class='fc-daygrid-day fc-day fc-day-mon fc-day-past fc-day-other']//div[@class='fc-daygrid-day-frame fc-scrollgrid-sync-inner']//div[@class='fc-daygrid-day-events']//div[@class='fc-daygrid-day-bottom']//a[@class="fc-daygrid-more-link fc-more-link"][contains(text(),'全部')]\n
    鼠标滑动到底部
    点击    //span[contains(text(),'${name}')]
    Sleep    10
    个人行事历编辑    ${name}

个人行事历视图删除
    [Arguments]    ${name}
    点击    //td[@class='fc-daygrid-day fc-day fc-day-mon fc-day-past fc-day-other']//div[@class='fc-daygrid-day-frame fc-scrollgrid-sync-inner']//div[@class='fc-daygrid-day-events']//div[@class='fc-daygrid-day-bottom']//a[@class="fc-daygrid-more-link fc-more-link"][contains(text(),'全部')]\n
    鼠标滑动到底部
    Sleep    10
    删除已生成的行事历    ${name}
