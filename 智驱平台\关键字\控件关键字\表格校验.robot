*** Settings ***
Library           SeleniumLibrary
Resource          ../系统关键字/web.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../系统关键字/接口.robot
Resource          ../../元素/Athena平台元素.robot
Resource          ../业务关键字/公共方法.robot
Resource          ../../配置/全局参数.robot
Resource          ../业务关键字/作业授权.robot
Resource          控件公共关键字.robot

*** Keywords ***
长度校验
    [Arguments]    ${story_desc}    ${story_name}    ${story_no}
    #长度校验+最值校验+空白校验+赋值校验+联动控制校验
    点击单元格    ${story_desc}    1
    点击单元格    ${story_name}    1
    当前页面可见字符    需求最小长度为1
    点击单元格    ${story_desc}    1
    当前页面可见字符    需求名称不可空白
    输入    //div[@class="ag-center-cols-viewport"]/div/div[1]/div[@col-id="${story_desc}"]//input    1212122111111111111111111111
    点击单元格    ${story_no}    1
    当前页面可见字符    最大长度为20
    当前页面可见字符    需求编号最小值为1
    当前页面可见字符    未保存
    输入    //div[@class="ag-center-cols-viewport"]/div/div[1]/div[@col-id="${story_no}"]//input    1212122111111111111111111111
    点击单元格    ${story_name}    1
    当前页面可见字符    需求编号最大值为20

重复校验
    [Arguments]    ${story_desc}    ${story_name}    ${story_no}
    Sleep    5
    点击单元格    ${story_desc}    1
    输入    //div[@class="ag-center-cols-viewport"]/div/div[1]/div[@col-id="${story_desc}"]//input    1
    点击单元格    ${story_name}    1
    输入    //div[@class="ag-center-cols-viewport"]/div/div[1]/div[@col-id="${story_name}"]//input    1
    点击单元格    ${story_no}    1
    点击单元格    ${story_no}    1
    输入    //div[@class="ag-center-cols-viewport"]/div/div[1]/div[@col-id="${story_no}"]//input    1
    点击单元格    ${story_no}    2
    输入    //div[@class="ag-center-cols-viewport"]/div/div[2]/div[@col-id="${story_no}"]//input    1
    点击单元格    ${story_name}    2
    输入    //div[@class="ag-center-cols-viewport"]/div/div[2]/div[@col-id="${story_name}"]//input    1
    点击单元格    ${story_desc}    2
    输入    //div[@class="ag-center-cols-viewport"]/div/div[2]/div[@col-id="${story_desc}"]//input    1
    点击单元格    ${story_name}    2
    当前页面可见字符    需求编号,需求名称唯一

颜色校验
    [Arguments]    ${story_no}    ${story_name}
    #小于5，颜色为绿色
    Sleep    5
    点击单元格    ${story_no}    1
    输入    //div[@class="ag-center-cols-viewport"]/div/div[1]/div[@col-id="${story_no}"]//input    1
    点击单元格    ${story_name}    1
    Comment    ${color}=    Set Variable    rgb(49, 179, 68)
    Comment    ${element}=    Get WebElement    //div[@class="ag-center-cols-viewport"]/div/div[1]/div[@col-id="story_no"]/div/span/cell-renderer/div/div/div/app-dynamic-input-display/ath-control-wrapper
    Comment    ${style}=    Get Element Attribute    ${element}    style
    Comment    ${color}=    Extract Color From Style    ${style}
    Comment    ${is_green}=    Is Color Green    ${color}
    Comment    Should Be True    ${is_green}
    Comment    ${element}=    Get WebElement    //div[@class="ag-center-cols-viewport"]/div/div[1]/div[@col-id="${story_no}"]/div/span/cell-renderer/div/div/div/app-dynamic-input-display/ath-control-wrapper
    Comment    Log    Element located: ${element}
    Comment    ${style}=    Get Element Attribute    ${element}    style
    Comment    Log    Element style: ${style}
    Comment    ${extracted_color}=    Extract Color From Style    ${style}
    Comment    Log    Extracted color: ${extracted_color}
    Comment    ${extracted_color}=    Strip String    ${extracted_color}
    Comment    ${green_rgb}=    Set Variable    rgb(49, 179, 68)
    Comment    ${green_hex}=    Set Variable    #31B344
    Comment    ${is_green}=    Evaluate    "${extracted_color}".lower() == '${green_rgb}'.lower() or "${extracted_color}".lower() == '${green_hex}'.lower()
    Comment    Log    Is green: ${is_green}
    Comment    Should Be True    ${is_green}
    #用CSS来比对色值
    ${element}=    Get WebElement    css=[style*="color: rgb(49, 179, 68);"]
    Log    Element located: ${element}
    #大于5红色
    Sleep    5
    点击单元格    ${story_no}    2
    输入    //div[@class="ag-center-cols-viewport"]/div/div[2]/div[@col-id="${story_no}"]//input    6
    点击单元格    ${story_name}    2
    ${elementa}    Get WebElement    css=[style*="color: rgb(249, 64, 76);"]
    Log    Element located: ${elementa}

Extract Color From Style
    [Arguments]    ${style}
    # 假设 style 属性包含 "color: xxx" 格式，这里使用正则表达式提取颜色值
    ${pattern}=    Set Variable    color:\s*([^;]+);
    ${match}=    Run Keyword And Return Status    Regexp Should Match    ${style}    ${pattern}
    ${color}=    Set Variable    None
    IF    ${match}
        ${color}=    Get Regexp Matches    ${style}    ${pattern}    1
    END
    Log    ${style}
    Log    ${color}
    RETURN    ${color}

Is Color Green
    [Arguments]    ${color}
    # 支持 RGB 和十六进制颜色格式判断
    ${green_rgb}=    Set Variable    rgb(49, 179, 68)
    ${green_hex}=    Set Variable    #00ff00
    ${is_green}=    Evaluate    "${color}".lower() == '${green_rgb}'.lower() or "${color}".lower() == '${green_hex}'.lower()
    RETURN    ${is_green}

操作列禁用
    [Arguments]    ${tenant_id}
    点击单元格    ${tenant_id}    1
    点击    //div[@class="ag-center-cols-viewport"]/div/div[2]/div[@col-id="${tenant_id}"]/div/span/cell-renderer/div/div/div/app-dynamic-input-display[@placeholder]

默认赋值
    当前页面可见字符    001
    当前页面可见字符    HL的需求

开始结束时间联动控制
    点击    //*[contains(text(),"新增")]
    Sleep    2
    输入    //div[@class="dynamic-form-list"]//input[@placeholder="请输入计划起始日期"]    2025/02/18
    点击    //div[@class="dynamic-form-list"]//input[@placeholder="请输入需求"]
    当前页面可见字符    2025年
