*** Settings ***
Documentation     郑苏振
Test Setup        Run Keywords    环境设定
Test Teardown     Close Browser
Resource          ../关键字/业务关键字/业务数据录入.robot
Library           SeleniumLibrary
Resource          ../元素/Athena平台元素.robot
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/业务关键字/智能入口.robot
Resource          ../关键字/业务关键字/公共方法.robot
Resource          ../关键字/业务关键字/作业授权.robot
Resource          ../关键字/业务关键字/业务数据录入.robot
Resource          ../配置/全局参数.robot
Resource          ../关键字/控件关键字/表格筛选.robot
Resource          ../关键字/控件关键字/表格分页.robot
Resource          ../关键字/控件关键字/表格分组.robot
Resource          ../关键字/控件关键字/表格工具栏操作.robot
Resource          ../关键字/控件关键字/表格行.robot
Resource          ../关键字/控件关键字/表格列.robot
Resource          ../关键字/控件关键字/表格排序.robot
Resource          ../关键字/控件关键字/单元格.robot
Resource          ../关键字/控件关键字/表格校验.robot

*** Test Cases ***
表格前端筛选
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001     '${ENV}'=='paas'    TestAthenaAutoTestAi001  #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001     '${ENV}'=='paas'    TestAthenaAutoTestAi001  #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    表格_高级查询作业测试
    Sleep   10
    FOR    ${data-colid}    ${searchkey}    IN
    ...    test_num        20240601
    ...    test_input    景枫中心15层
    ...    test_select    功能测试
    #...    test_data        1990/01/01
    ...    test_time        0.5%
        前端筛选    ${data-colid}    ${searchkey}
        重置筛选    ${data-colid}    ${searchkey}
    END
    打开表格设定开窗    表格_高级查询作业测试
    调整表格字段顺序    test日期选择    test数字输入框   表格_高级查询作业测试
    前端筛选  test_data        1990/01/01
    打开表格设定开窗    表格_高级查询作业测试
    重置表格设定

表格服务端筛选
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001     '${ENV}'=='paas'    TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001     '${ENV}'=='paas'    TestAthenaAutoTestAi001   #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    表格_后端筛选
    Sleep   30
    打开表格设定开窗    表格_后端筛选
    调整表格字段顺序    test日期选择    test数字输入框   表格_后端筛选
    Sleep   3
    日期控件后端筛选    test_data   1990/01/01  1990/01/01    
    打开表格设定开窗    表格_后端筛选
    重置表格设定
    刷新页面
    Sleep   10
    #服务端筛选 \ test_num \ 20240601
    FOR    ${sdata-colid}    ${ssearchkey}    IN
    ...    test_num        20240601
    ...    test_input    景枫中心15层
    ...    test_select    功能测试
    ...    test_time        0.5%
        服务端筛选      ${sdata-colid}    ${ssearchkey}
        重置筛选      ${sdata-colid}    ${ssearchkey}
    END


表格设定
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001     '${ENV}'=='paas'    TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001     '${ENV}'=='paas'    TestAthenaAutoTestAi001   #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    表格_高级查询作业测试
    Sleep   20
    打开表格设定开窗    表格_高级查询作业测试
    重置表格设定
    Sleep   10
    打开表格设定开窗    表格_高级查询作业测试
    显示/隐藏表格字段    复合字段
    打开表格设定开窗    表格_高级查询作业测试
    Sleep   10
    表格字段冻结    case单档多栏位模型驱动主键
    关闭作业页签
    # 打开表格设定开窗 \ 表格_高级查询作业测试
    # Sleep \ \ 10
    #调整表格字段顺序
    查询所需作业    表格_内含外显
    Sleep   5
    打开表格设定开窗    表格_内含外显
    Sleep   5
    调整表格字段顺序    内含外显字段    test数字输入框   表格_内含外显
    打开表格设定开窗    表格_内含外显
    重置表格设定

基础资料前端分页
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001  #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001  #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    表格_前端分页
    Sleep   10
    分页页码选择    50  10
    分页页码选择    10  20
    分页页码选择    20  100
    分页页码选择    100     50
    分页切换    5

后端分页
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001   #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001   #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    表格_后端分页
    Sleep   10
    分页页码选择    50  10
    分页页码选择    10  20
    分页页码选择    20  100
    分页页码选择    100     50
    分页切换    5

表格新增行
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001   #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001   #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    表格_标准前端分页
    Sleep   3
    判断表格存在数据则删除  page_id
    表格行.自动新增行   页面编号
    删除表格数据
    关闭业务数据录入页签
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    表格_高级查询作业测试
    Sleep   20
    表格行.手动新增行   数字输入    20240101
    前端筛选    test_num    20240101
    删除表格数据
    重置筛选    test_num    20240101

单元格开窗新增行
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001   #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001     #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    表格行.单元格开窗新增行

表格删除行
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001   #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    表格_双档作业测试
    Sleep   3
    表格列.操作列删除

操作列复制
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001   #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001   #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    表格_高级查询作业测试
    Sleep   20
    前端筛选    test_num    20240601
    表格列.操作列复制
    重置筛选    test_num    20240601
    前端筛选    test_num    20240600
    删除表格数据
    重置筛选    test_num    20240600
    操作列condition

表格checkbox
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001   #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001     #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    表格_高级查询作业测试
    Sleep   20
    checkbox多选
    checkbox取消全选
    checkbox全选    104
    checkbox取消全选

行高设定
    [Tags]   height_seting
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001     '${ENV}'=='paas'    TestAthenaAutoTestAi001   #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001     '${ENV}'=='paas'    TestAthenaAutoTestAi001   #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    表格_高级查询作业测试
    Sleep   20
    FOR     ${setitem}  IN
    ...     紧凑
    ...     适中
    ...     宽松
    ...     超大
    ...     默认
            行高设定    ${setitem}
    END
    列宽设定

表格分组
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001     #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    表格分组.发起表格分组项目卡

表格高级查询/快速查询
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001     '${ENV}'=='paas'    TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001     '${ENV}'=='paas'    TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    表格_后端筛选
    Sleep   10
    #快速查询/数值型
    打开高级查询开窗
    展开/收起查询条件
    快速查询/数值型     1   100
    提交查询
    Sleep   3
    断言查询结果    1
    重置查询结果
    #快速查询/枚举型
    打开高级查询开窗
    快速查询/枚举型     功能测试
    重置查询结果
    #快速查询/字符型
    打开高级查询开窗
    快速查询/字符型     景枫中心15层
    重置查询结果
    #快速查询/日期型
    打开高级查询开窗
    快速查询/日期型     2024/12/01  2024/12/30
    重置查询结果
    #快速查询+排序条件
    打开高级查询开窗
    快速查询/数值型     1   1000
    删除排序条件
    点击添加条件
    组合排序添加排序条件    query-sort-box  数字输入    升序
    提交查询
    断言组合排序结果
    重置查询结果

表格高级查询/高级查询
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001      #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    表格_后端筛选
    Sleep   10
    #高级查询
    打开高级查询开窗
    高级查询切换查询页签    高级查询
    高级查询添加查询条件    test文本输入框  等于    景枫中心15层
    提交查询
    Sleep   3
    断言高级查询结果    景枫中心15层
    重置查询结果
    #高级查询+排序条件
    打开高级查询开窗
    高级查询切换查询页签    高级查询
    高级查询添加查询条件    test文本输入框  等于    景枫中心15层
    组合排序添加排序条件    query-sort-box     数字输入    降序
    提交查询
    断言组合排序结果
    Sleep   3
    重置查询结果

表格高级查询/常用条件
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001   #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    表格_后端筛选
    Sleep   10
    #高级查询
    打开高级查询开窗
    高级查询切换查询页签    高级查询
    高级查询添加查询条件    test文本输入框  等于    景枫中心15层
    保存为常用条件  常用条件1
    Sleep   3
    删除常用条件    常用条件1
    重置查询结果

表格列排序
    #列排序升序
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001   '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001   '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    表格_列排序
    Sleep    10
    #字符+文本
    FOR    ${data-colid}    ${ssortkey}    ${hsortkey}    IN
    ...    case_dandang_id    1    4
    ...    feedback_source    1    4
    列排序    ${data-colid}    ${ssortkey}    ${hsortkey}
    END
    #时间+枚举
    FOR    ${data-colid}    ${ssortkey}    ${hsortkey}    ${key}    IN
    ...    responsible_department    无    厂商    dynamic-select-display
    ...    first_review_time    2024/12/03    2024/12/07    dynamic-datepicker-display
    枚举时间排序    ${data-colid}    ${ssortkey}    ${hsortkey}    ${key}
    END
    复合排序    feedback_id    1    4

表格多条件排序
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001     #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    表格_后端筛选
    Sleep   10
    打开多条件排序开窗
    组合排序添加排序条件    modal-content-box composite-sort-box  数字输入    降序
    多条件排序提交
    断言多条件排序结果  数字输入
    打开多条件排序开窗
    恢复默认排序

表格合计行/通栏合计
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    表格_通栏合计行
    Sleep   3
    判断表格存在数据则删除   page_id
    通栏合计行  页面编号    0.1
    删除表格数据

表格合计行/分栏合计
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001   #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    表格_分栏合计行
    Sleep   3
    判断表格存在数据则删除   page_id
    分栏合计行      page_id     page_name   0.1
    删除表格数据

合并单元格
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    发起合并单元格项目卡    2025    202501  202502
    Sleep   10
    合并单元格任务卡    2025    202501  202502

表头图标
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001     #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001     #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    表格_高级查询作业测试
    Sleep   20
    表头图标

表格拆行
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001   #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001   #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    发起手动拆行项目卡    雅典娜    1000
    Sleep   20
    任务卡表格手动拆行
    重置任务筛选
    任务卡表格自动拆行
    重置任务筛选

单元格批量赋值
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001     '${ENV}'=='paas'    TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001     '${ENV}'=='paas'    TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    表格_高级查询作业测试
    Sleep   20
    前端筛选    test_num    20240601
    单元格拖拽赋值

表格校验规则
    #列排序升序
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    单档作业测试的作业
    Sleep    5
    #默认赋值
    当前页面可见字符    未生效
    #校验
    长度校验    story_desc    story_name    story_no
    刷新页面
    重复校验    story_desc    story_name    story_no
    关闭浏览器
    #打开新作业
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    单档作业行样式
    #样式
    #需求编号大于5展示为红色
    颜色校验    story_no    story_name
    #禁用
    #租户id禁用
    操作列禁用    tenant_id
    关闭浏览器
    #打开新作业
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    单档作业赋值
    #赋值
    #默认赋值
    默认赋值
    关闭浏览器
    #打开新作业
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    单档作业联动控制
    #联动控制
    开始结束时间联动控制
    关闭浏览器

重置表格设定并刷新##场景1：业务数据录入
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001  #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001  #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    ##场景1：业务数据录入
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    Sleep    5s
    查询所需作业    请假单的作业
    打开表格设定开窗    请假单的作业
    重置表格设定
    刷新页面
    Sleep    10s
    打开表格设定开窗    请假单的作业
    关闭浏览器
重置表格设定并刷新####场景2：任务卡
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001  #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001  #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    ####场景2：任务卡
    进入指定任务卡
    Sleep    10s
    打开表格设定开窗（任务卡）
    重置表格设定
    刷新页面
    Sleep    10s
    打开表格设定开窗（任务卡）
    关闭浏览器
重置表格设定并刷新#####场景3：项目卡
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001  #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001  #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    #####场景3：项目卡
    进入指定项目卡
    Sleep    10s
    打开表格设定开窗（项目卡）
    重置表格设定
    刷新页面
    Sleep    10s
    打开表格设定开窗（项目卡）
    关闭浏览器
重置表格设定并刷新###场景4：收藏入口
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001  #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001  #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    ###场景4：收藏入口
    进入指定收藏任务卡
    Sleep    10s
    打开表格设定开窗（收藏任务卡）
    重置表格设定
    刷新页面
    Sleep    10s
    打开表格设定开窗（收藏任务卡）
    关闭浏览器
重置表格设定并刷新#####场景5：消息入口
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001  #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001  #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    #####场景5：消息入口
    进入指定消息任务卡
    Sleep    10s
    打开表格设定开窗（消息任务卡）
    重置表格设定
    刷新页面
    Sleep    10s
    打开表格设定开窗（消息任务卡）
    关闭浏览器