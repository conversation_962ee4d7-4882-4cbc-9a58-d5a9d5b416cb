*** Settings ***
Suite Setup       Run Keywords    环境设定
Test Teardown     Run Keywords    错误处理    AND    关闭浏览器
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/系统关键字/公共方法.robot
Resource          ../关键字/业务关键字/顺序签核.robot
Resource          ../关键字/业务关键字/PCC.robot
Resource          ../关键字/业务关键字/业务数据录入.robot
Resource          ../关键字/业务关键字/繁体检查.robot

*** Test Cases ***
基础资料繁体检查
    [Documentation]    陈金明
    #当责者账号/部门签核者账号
    ${username}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    HL18271405997    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    HL18271405997
    ${password}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    HuangL0920    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    HuangL0920
    登录Athena平台    ${username}    ${password}    language=繁体
    点击顶部菜单    全部
    点击右侧菜单    業務數據輸入
    当前页面可见字符    公共基礎資料
    当前页面可见字符    專案中控台
    查询所需作业    多檔的作業
    多档作业繁体断言

项目/任务繁体检查
    [Documentation]    陈金明
    #当责者账号/部门签核者账号
    ${username}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestSd01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${password}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestSd01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    #当责者,新建一级计划时需要按名称选择人员
    ${ownerUsernameCN}    Set Variable If    '${ENV}'=='pressure'    测试环境自动化测试SD    '${ENV}'=='huawei.test'    测试环境自动化测试SD    '${ENV}'=='huawei.prod'    生产华为环境自动化测试KM    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试KM    '${ENV}'=='muihuawei.test'    测试环境自动化测试智驱平台
    #执行者名称,新建一级计划时需要按名称选择人员
    ${exUsernameCN}    Set Variable If    '${ENV}'=='pressure'    测试环境自动化测试智驱平台    '${ENV}'=='huawei.test'    测试环境自动化测试智驱平台    '${ENV}'=='huawei.prod'    生产华为环境自动化测试智驱入口    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试智驱入口    '${ENV}'=='muihuawei.test'    测试环境自动化测试SD
    #执行者账号
    ${exUsername}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestSd001
    ${exPassword}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestSd001

    Set Global Variable    ${projectname}    ${长唯一标识}
    #前置任务
    Set Global Variable    ${pretaskname}    前置${长唯一标识}
    #后置任务
    Set Global Variable    ${posttaskname}    后置${长唯一标识}
    登录Athena平台    ${username}    ${password}    language=繁体
    手动发起项目(繁体)    ${projectname}    项目无需签核    BB    3    2024/09/15
    新建一级计划(繁体)    ${projectname}    ${pretaskname}    2024/09/15    2024/09/15    ${ownerUsernameCN}    ${exUsernameCN}    无需签核
    启动项目(繁体)
    关闭浏览器
    登录Athena平台    ${exUsername}    ${exPassword}    language=繁体
    报工(繁体)    ${pretaskname}
    关闭浏览器
    登录Athena平台    ${username}    ${password}    language=繁体
    Sleep    150
    指定结案(繁体)    ${projectname}
