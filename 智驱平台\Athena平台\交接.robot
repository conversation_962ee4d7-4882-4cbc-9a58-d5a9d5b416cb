*** Settings ***
Documentation     高伟
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/公共方法.robot
Resource          ../关键字/业务关键字/交接.robot
Resource          ../配置/交接参数.robot
Resource          ../关键字/业务关键字/作业授权.robot
Test Setup    Run Keywords    环境设定
Test Teardown    Close Browser



*** Test Cases ***
任务单点交接
    ${ownerUsername}      Set Variable If    '${ENV}'=='huawei.test'    qcsupplier001    '${ENV}'=='huawei.prod'    qcsupplier001    '${ENV}'=='microsoft.prod'    qcsupplier001
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    supplier001    '${ENV}'=='huawei.prod'    supplier001    '${ENV}'=='microsoft.prod'    supplier001
    ${ownerUsername02}    Set Variable If    '${ENV}'=='huawei.test'    HL18271405997    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02
    ${ownerPassword02}    Set Variable If    '${ENV}'=='huawei.test'    HuangL0920    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02
    ${req_random}    Set Variable    ${唯一标识}
    ${hash_value}    Set Variable If    '${ENV}'=='huawei.test'    ${hash_value_test}    '${ENV}'=='huawei.prod'    ${hash_value_hwprod}    '${ENV}'=='microsoft.prod'    ${hash_value_wrprod}
    ${username02}    Set Variable If    '${ENV}'=='huawei.test'    hl_test    '${ENV}'=='huawei.prod'    生产华为环境自动化测试小AI入口
    Set Global Variable    ${hash_value}
#    获取token
    鼎捷云登陆接口获取token
    ${data}    Set Variable    {"user":{"id":"qcsupplier001","name":"供应商1","email":"<EMAIL>","hash":"${hash_value}","remark":"變更用戶資料"},"metadata":[{"catalogId":"contact","key":"wechat","value":""}],"roleIds":[],"userInOrgs":[],"defaultOrg":[],"userInTag":[],"app":{"deleteAppIds":[],"addAppIds":["aifiveCase"]},"userType":false}
    调用iam接口为用户授权/取消授权应用    ${data}
    登录Athena平台     ${ownerUsername}    ${ownerPassword}
    进入发起项目菜单
    进入-小AI-五要素基线用例专用作业--发起提交    ${req_random}
    ${data}    Set Variable    {"user":{"id":"qcsupplier001","name":"供应商1","email":"<EMAIL>","hash":"${hash_value}","remark":"變更用戶資料"},"metadata":[{"catalogId":"contact","key":"wechat","value":""}],"roleIds":[],"userInOrgs":[],"defaultOrg":[],"userInTag":[],"app":{"deleteAppIds":["aifiveCase"],"addAppIds":[]},"userType":false}
    调用iam接口为用户授权/取消授权应用    ${data}
    #    给被交接用户${ownerUsername02}授权应用
    ${data02}    Set Variable    {"user":{"id":"${ownerUsername02}","name":"${username02}","email":"","remark":"變更用戶資料"},"metadata":[{"catalogId":"contact","key":"wechat","value":""},{"catalogId":"integrationUserId","key":"dingding","value":""}],"roleIds":["superadmin"],"userInOrgs":[],"defaultOrg":[],"userInTag":[],"app":{"deleteAppIds":[],"addAppIds":["aifiveCase"]},"userType":false}
    调用iam接口为用户授权/取消授权应用    ${data02}
    点击任务-交接    ${req_random}    ${ownerUsername02}
    退出当前登录,切换账号登录    ${ownerUsername02}    ${ownerPassword02}
    被交接人-提交任务    ${req_random}


项目单点交接
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    qcsupplier001    '${ENV}'=='huawei.prod'    qcsupplier001    '${ENV}'=='microsoft.prod'    qcsupplier001
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    supplier001    '${ENV}'=='huawei.prod'    supplier001    '${ENV}'=='microsoft.prod'    supplier001
    ${ownerUsername02}    Set Variable If    '${ENV}'=='huawei.test'    HL18271405997    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02
    ${ownerPassword02}    Set Variable If    '${ENV}'=='huawei.test'    HuangL0920    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02
    ${req_random}    Set Variable    ${唯一标识}
    ${hash_value}    Set Variable If    '${ENV}'=='huawei.test'    ${hash_value_test}    '${ENV}'=='huawei.prod'    ${hash_value_hwprod}    '${ENV}'=='microsoft.prod'    ${hash_value_wrprod}
    ${username02}    Set Variable If    '${ENV}'=='huawei.test'    hl_test    '${ENV}'=='huawei.prod'    生产华为环境自动化测试小AI入口
    鼎捷云登陆接口获取token
    ${data}    Set Variable    {"user":{"id":"qcsupplier001","name":"供应商1","email":"<EMAIL>","hash":"${hash_value}","remark":"變更用戶資料"},"metadata":[{"catalogId":"contact","key":"wechat","value":""}],"roleIds":[],"userInOrgs":[],"defaultOrg":[],"userInTag":[],"app":{"deleteAppIds":[],"addAppIds":["aifiveCase"]},"userType":false}
    调用iam接口为用户授权/取消授权应用    ${data}
    登录Athena平台     ${ownerUsername}    ${ownerPassword}
    进入发起项目菜单
    进入-小AI-五要素基线用例专用作业--发起提交    ${req_random}
    ${data}    Set Variable    {"user":{"id":"qcsupplier001","name":"供应商1","email":"<EMAIL>","hash":"${hash_value}","remark":"變更用戶資料"},"metadata":[{"catalogId":"contact","key":"wechat","value":""}],"roleIds":[],"userInOrgs":[],"defaultOrg":[],"userInTag":[],"app":{"deleteAppIds":["aifiveCase"],"addAppIds":[]},"userType":false}
    调用iam接口为用户授权/取消授权应用    ${data}
#    给被交接用户${ownerUsername02}授权应用
    ${data02}    Set Variable    {"user":{"id":"${ownerUsername02}","name":"${username02}","email":"","remark":"變更用戶資料"},"metadata":[{"catalogId":"contact","key":"wechat","value":""},{"catalogId":"integrationUserId","key":"dingding","value":""}],"roleIds":["superadmin"],"userInOrgs":[],"defaultOrg":[],"userInTag":[],"app":{"deleteAppIds":[],"addAppIds":["aifiveCase"]},"userType":false}
    调用iam接口为用户授权/取消授权应用    ${data02}
    点击项目-交接    ${req_random}    ${ownerUsername02}
    退出当前登录,切换账号登录    ${ownerUsername02}    ${ownerPassword02}
    被交接人-查询项目是否创建    ${req_random}


任务-批量交接
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    qcsupplier001    '${ENV}'=='huawei.prod'    qcsupplier001    '${ENV}'=='microsoft.prod'    qcsupplier001
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    supplier001    '${ENV}'=='huawei.prod'    supplier001    '${ENV}'=='microsoft.prod'    supplier001
    ${ownerUsername02}    Set Variable If    '${ENV}'=='huawei.test'    HL18271405997    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02
    ${ownerPassword02}    Set Variable If    '${ENV}'=='huawei.test'    HuangL0920    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02
    ${req_random}    Set Variable    ${唯一标识}
    ${hash_value}    Set Variable If    '${ENV}'=='huawei.test'    ${hash_value_test}    '${ENV}'=='huawei.prod'    ${hash_value_hwprod}    '${ENV}'=='microsoft.prod'    ${hash_value_wrprod}
    ${username02}    Set Variable If    '${ENV}'=='huawei.test'    hl_test    '${ENV}'=='huawei.prod'    生产华为环境自动化测试小AI入口
    鼎捷云登陆接口获取token
    ${data}    Set Variable    {"user":{"id":"qcsupplier001","name":"供应商1","email":"<EMAIL>","hash":"${hash_value}","remark":"變更用戶資料"},"metadata":[{"catalogId":"contact","key":"wechat","value":""}],"roleIds":[],"userInOrgs":[],"defaultOrg":[],"userInTag":[],"app":{"deleteAppIds":[],"addAppIds":["aifiveCase"]},"userType":false}
    调用iam接口为用户授权/取消授权应用    ${data}
    登录Athena平台     ${ownerUsername}    ${ownerPassword}
    进入发起项目菜单
    进入-小AI-五要素基线用例专用作业--发起提交    ${req_random}
    ${data}    Set Variable    {"user":{"id":"qcsupplier001","name":"供应商1","email":"<EMAIL>","hash":"${hash_value}","remark":"變更用戶資料"},"metadata":[{"catalogId":"contact","key":"wechat","value":""}],"roleIds":[],"userInOrgs":[],"defaultOrg":[],"userInTag":[],"app":{"deleteAppIds":["aifiveCase"],"addAppIds":[]},"userType":false}
    调用iam接口为用户授权/取消授权应用    ${data}
   ${data02}    Set Variable    {"user":{"id":"${ownerUsername02}","name":"${username02}","email":"","remark":"變更用戶資料"},"metadata":[{"catalogId":"contact","key":"wechat","value":""},{"catalogId":"integrationUserId","key":"dingding","value":""}],"roleIds":["superadmin"],"userInOrgs":[],"defaultOrg":[],"userInTag":[],"app":{"deleteAppIds":[],"addAppIds":["aifiveCase"]},"userType":false}
    调用iam接口为用户授权/取消授权应用    ${data02}
    点击批量交接-任务    ${req_random}
    任务交接-确定    ${req_random}    ${ownerUsername02}
    退出当前登录,切换账号登录    ${ownerUsername02}    ${ownerPassword02}
    被交接人-提交任务    ${req_random}

项目-批量交接
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    qcsupplier001    '${ENV}'=='huawei.prod'    qcsupplier001    '${ENV}'=='microsoft.prod'    qcsupplier001
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    supplier001    '${ENV}'=='huawei.prod'    supplier001    '${ENV}'=='microsoft.prod'    supplier001
    ${ownerUsername02}    Set Variable If    '${ENV}'=='huawei.test'    HL18271405997    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02
    ${ownerPassword02}    Set Variable If    '${ENV}'=='huawei.test'    HuangL0920    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02
    ${req_random}    Set Variable    ${唯一标识}
    ${hash_value}    Set Variable If    '${ENV}'=='huawei.test'    ${hash_value_test}    '${ENV}'=='huawei.prod'    ${hash_value_hwprod}    '${ENV}'=='microsoft.prod'    ${hash_value_wrprod}
    鼎捷云登陆接口获取token
    ${data}    Set Variable    {"user":{"id":"qcsupplier001","name":"供应商1","email":"<EMAIL>","hash":"${hash_value}","remark":"變更用戶資料"},"metadata":[{"catalogId":"contact","key":"wechat","value":""}],"roleIds":[],"userInOrgs":[],"defaultOrg":[],"userInTag":[],"app":{"deleteAppIds":[],"addAppIds":["aifiveCase"]},"userType":false}
    调用iam接口为用户授权/取消授权应用    ${data}
    登录Athena平台     ${ownerUsername}    ${ownerPassword}
    进入发起项目菜单
    进入-小AI-五要素基线用例专用作业--发起提交    ${req_random}
    ${data}    Set Variable    {"user":{"id":"qcsupplier001","name":"供应商1","email":"<EMAIL>","hash":"${hash_value}","remark":"變更用戶資料"},"metadata":[{"catalogId":"contact","key":"wechat","value":""}],"roleIds":[],"userInOrgs":[],"defaultOrg":[],"userInTag":[],"app":{"deleteAppIds":["aifiveCase"],"addAppIds":[]},"userType":false}
    调用iam接口为用户授权/取消授权应用    ${data}
    点击批量交接-项目    ${req_random}
    项目交接-确定    ${ownerUsername02}
    退出当前登录,切换账号登录    ${ownerUsername02}    ${ownerPassword02}
    被交接人-查询项目是否创建    ${req_random}


任务单点交接-不能选择自己以及未授权用户
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    qcsupplier001    '${ENV}'=='huawei.prod'    qcsupplier001    '${ENV}'=='microsoft.prod'    qcsupplier001
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    supplier001    '${ENV}'=='huawei.prod'    supplier001    '${ENV}'=='microsoft.prod'    supplier001
    ${ownerUsername02}    Set Variable If    '${ENV}'=='huawei.test'    HL18271405997    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02
    ${ownerPassword02}    Set Variable If    '${ENV}'=='huawei.test'    HuangL0920    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02
    ${req_random}    Set Variable    ${唯一标识}
    ${hash_value}    Set Variable If    '${ENV}'=='huawei.test'    ${hash_value_test}    '${ENV}'=='huawei.prod'    ${hash_value_hwprod}    '${ENV}'=='microsoft.prod'    ${hash_value_wrprod}
    ${username02}    Set Variable If    '${ENV}'=='huawei.test'    hl_test    '${ENV}'=='huawei.prod'    生产华为环境自动化测试小AI入口
    鼎捷云登陆接口获取token
    ${data}    Set Variable    {"user":{"id":"qcsupplier001","name":"供应商1","email":"<EMAIL>","hash":"${hash_value}","remark":"變更用戶資料"},"metadata":[{"catalogId":"contact","key":"wechat","value":""}],"roleIds":[],"userInOrgs":[],"defaultOrg":[],"userInTag":[],"app":{"deleteAppIds":[],"addAppIds":["aifiveCase"]},"userType":false}
    调用iam接口为用户授权/取消授权应用    ${data}
    登录Athena平台     ${ownerUsername}    ${ownerPassword}
    进入发起项目菜单
    进入-小AI-五要素基线用例专用作业--发起提交    ${req_random}
    ${data}    Set Variable    {"user":{"id":"qcsupplier001","name":"供应商1","email":"<EMAIL>","hash":"${hash_value}","remark":"變更用戶資料"},"metadata":[{"catalogId":"contact","key":"wechat","value":""}],"roleIds":[],"userInOrgs":[],"defaultOrg":[],"userInTag":[],"app":{"deleteAppIds":["aifiveCase"],"addAppIds":[]},"userType":false}
    调用iam接口为用户授权/取消授权应用    ${data}
    ${data02}    Set Variable    {"user":{"id":"${ownerUsername02}","name":"${username02}","email":"","remark":"變更用戶資料"},"metadata":[{"catalogId":"contact","key":"wechat","value":""},{"catalogId":"integrationUserId","key":"dingding","value":""}],"roleIds":["superadmin"],"userInOrgs":[],"defaultOrg":[],"userInTag":[],"app":{"deleteAppIds":["aifiveCase"],"addAppIds":[]},"userType":false}
    调用iam接口为用户授权/取消授权应用    ${data02}
    判断交接时自己/未授权当前应用的用户在交接时是否可以在下拉选择中展示    ${ownerUsername02}    ${req_random}    ${ownerUsername}
    ${data03}    Set Variable    {"user":{"id":"${ownerUsername02}","name":"${username02}","email":"","remark":"變更用戶資料"},"metadata":[{"catalogId":"contact","key":"wechat","value":""},{"catalogId":"integrationUserId","key":"dingding","value":""}],"roleIds":["superadmin"],"userInOrgs":[],"defaultOrg":[],"userInTag":[],"app":{"deleteAppIds":[],"addAppIds":["aifiveCase"]},"userType":false}
    调用iam接口为用户授权/取消授权应用    ${data03}


