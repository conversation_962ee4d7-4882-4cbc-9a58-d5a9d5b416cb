*** Settings ***
Resource          ../系统关键字/web.robot

*** Keywords ***
启用/停用
    [Arguments]    ${unique}
    #利用现有数据进行测试,可能数据会变化
    Sleep    5
    ${element}    按顺序获取元素    //span[contains(text(),'${unique}')]/following::*//button[3]    0
    ${textBefor}    获取元素字符    ${element}
    点击    ${element}
    点击    //span[contains(text(),'确定')]
    Sleep    5
    ${element}    按顺序获取元素    //span[contains(text(),'${unique}')]/following::*//button[3]    0
    ${textAfter}    获取元素字符    ${element}
    #这个写法diao bu diao
    Run Keyword If    '${textBefor}'=='启用'    Should Be Equal    ${textAfter}    停用
    Run Keyword If    '${textBefor}'=='停用'    Should Be Equal    ${textAfter}    启用
    Sleep    5

查看
    [Arguments]    ${unique}
    ${element}    按顺序获取元素    //span[contains(text(),'${unique}')]/following::*//button[1]    0
    点击    ${element}
    Page Should Contain    查看邮件模版
    点击    //i[@class='anticon ant-modal-close-icon anticon-close ng-star-inserted']
    Sleep    5

编辑
    [Arguments]    ${unique}
    ${element}    按顺序获取元素    //span[contains(text(),'${unique}')]/following::*//button[2]    0
    点击    ${element}
    Sleep    5
    Wait Until Page Contains    编辑邮件模版    60
    #点击    //i[@class='anticon ant-modal-close-icon anticon-close ng-star-inserted']
    Press Keys    //span[@class='input-content']    ${email}@qq.com
    点击    //span[contains(text(),'提交')]
    点击    //span[contains(text(),'确定')]
    Sleep    5

删除
    [Arguments]    ${unique}
    ${element}    按顺序获取元素    //span[contains(text(),'${unique}')]/following::*//button[2]    0
    点击    ${element}
    Page Should Contain    编辑邮件模版
    #点击    //i[@class='anticon ant-modal-close-icon anticon-close ng-star-inserted']
    点击    //span[contains(text(),'${email}')]/following-sibling::span[1]
    点击    //span[contains(text(),'提交')]
