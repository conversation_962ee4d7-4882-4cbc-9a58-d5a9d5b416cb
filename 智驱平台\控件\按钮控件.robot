*** Settings ***
Documentation     郑苏振
Resource          ../关键字/业务关键字/业务数据录入.robot
Library           SeleniumLibrary
Resource          ../元素/Athena平台元素.robot
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/业务关键字/智能入口.robot
Resource          ../关键字/业务关键字/首页.robot
Resource          ../关键字/业务关键字/公共方法.robot
Resource          ../关键字/业务关键字/作业授权.robot
Resource          ../关键字/业务关键字/业务数据录入.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../配置/全局参数.robot
Resource          ../关键字/控件关键字/表格筛选.robot
Resource          ../关键字/控件关键字/表格分页.robot
Resource          ../关键字/控件关键字/表格分组.robot
Resource          ../关键字/控件关键字/表格工具栏操作.robot
Resource          ../关键字/控件关键字/表格行.robot
Resource          ../关键字/控件关键字/表格列.robot
Resource          ../关键字/控件关键字/表格排序.robot
Resource          ../关键字/控件关键字/单元格.robot
Resource          ../关键字/控件关键字/按钮类控件.robot
Resource          ../关键字/控件关键字/控件公共关键字.robot
Test Setup        Run Keywords    环境设定
Test Teardown     Close Browser

*** Test Cases ***
通用按钮
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='muihuawei.test'   TestAthenaAutoTestAi001     #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='muihuawei.test'   TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${req_random}    Set Variable    ${唯一标识}
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    按钮_通用按钮
    Sleep   3
    点击新增打开子页签
    按钮在当前区域的位置检查  通用按钮 1  left
    按钮在当前区域的位置检查  通用按钮 2  center
    按钮在当前区域的位置检查  通用按钮 3  right   
    按钮类型检查  通用按钮 1   primary 
    按钮类型检查  通用按钮 2   default
    按钮类型检查  通用按钮 3   dashed 
    按钮类型检查  通用按钮 4   text 
    按钮类型检查  通用按钮 5   link 
    按钮大小检查  通用按钮 1  lg
    按钮大小检查  通用按钮 2  sm
    按钮大小检查  通用按钮 3  defalut
    按钮状态禁用  通用按钮 6  true
    开启幽灵按钮  通用按钮 1
    开启块状按钮  通用按钮 3
    开启危险按钮  通用按钮 4
    按钮图标展示
    按钮二次确认弹窗  通用按钮 4
    按钮可用条件condition  通用按钮 4  222
    按钮可用条件condition  通用按钮 4  2
    按钮动态显隐hidden  通用按钮4  111
    按钮动态显隐hidden  通用按钮4  11
    基础资料跳报表  通用按钮 1
    关闭页签   报表
    基础资料跳发起项目   通用按钮 5
    关闭页签   发起项目
    基础资料跳基础资料  通用按钮 3
    关闭当前基础资料
    关闭业务数据录入页签
    进入发起项目菜单
    进入-小AI-五要素基线用例专用作业--发起提交    ${req_random}
    任务详情跳报表  ${req_random}   任务详情跳转报表
    Sleep   3
    关闭页签  报表
    任务详情跳基础资料  任务详情跳转基础资料
    Sleep   3
    关闭业务数据录入页签
    项目详情跳报表  ${req_random}   项目详情跳转报表
    Sleep   3
    关闭页签   报表
    项目详情跳任务详情  项目详情跳转任务详情
    Sleep   3
    切换项目/任务详情列表   小AI-五要素基线用例专用-勿动_项目_0001
    项目详情跳基础资料   项目详情跳转基础资料
    Sleep   3
    关闭业务数据录入页签
    #关闭页签  业务数据录入

子页面按钮
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='muihuawei.test'   TestAthenaAutoTestAi001     #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='muihuawei.test'   TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${req_random}    Set Variable    ${唯一标识}
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    按钮_子页面按钮
    Sleep   3
    点击新增打开子页签
    打开子页面弹窗  打开子页面弹窗
    关闭子页面弹窗
    FOR    ${buttonName}    ${placement}    IN
    ...    打开子页面抽屉左   left
    ...    打开子页面抽屉右   right
    ...    打开子页面抽屉顶部   top
    ...    打开子页面抽屉底部   bottom
        子页面抽屉位置检查      ${buttonName}    ${placement}
    END
    按钮类型检查  打开子页面弹窗  primary
    按钮类型检查  打开子页面抽屉左  default
    按钮类型检查  打开子页面抽屉右  dashed
    按钮类型检查  打开子页面抽屉顶部  text
    按钮类型检查  打开子页面抽屉底部  link
    关闭业务数据录入页签

提交按钮组
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='muihuawei.test'   TestAthenaAutoTestAi001     #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='muihuawei.test'   TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${req_random}    Set Variable    ${唯一标识}
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    按钮_提交按钮组
    Sleep   3
    按钮间距检查  20px
    展开更多按钮
    关闭更多按钮

带参跳转到报表并自动查询报表内容
#工程化组 2025S4需求#46316
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='muihuawei.test'   TestAthenaAutoTestAi001     #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='muihuawei.test'   TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${req_random}    Set Variable    ${唯一标识}
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    按钮单档多栏模型驱动测试
    Sleep   3
    基础资料带参跳转到ABI/TBB报表  tbb
    基础资料带参跳转到ABI/TBB报表  abi

任务/项目卡详情跳转报表并自动查询报表内容
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='muihuawei.test'   TestAthenaAutoTestAi001     #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='muihuawei.test'   TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${req_random}    Set Variable    ${唯一标识}
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    #任务卡详情跳tbb报表
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    请假单按钮测试
    生成带参跳转任务卡
    关闭业务数据录入页签
    任务卡详情跳转ABI/TBB报表  请假单任务卡   tbb
    项目卡详情跳转ABI/TBB报表  请假单审批流程  tbb
    关闭带参跳转任务卡
    #任务卡详情跳abi报表
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    请假单按钮测试
    生成带参跳转任务卡
    关闭业务数据录入页签
    任务卡详情跳转ABI/TBB报表  请假单任务卡   abi
    项目卡详情跳转ABI/TBB报表  请假单审批流程  abi
    关闭带参跳转任务卡
    [Teardown]    Run Keywords    关闭浏览器




