*** Settings ***
Resource          ../系统关键字/web.robot
Resource          首页.robot
Resource          智能入口.robot
Resource          待办.robot

*** Keywords ***
登陆鼎捷云
    [Arguments]    ${username}    ${password}
    打开网页    ${DIGIWIN_CLOULD}    ${browser}
    输入    //input[@placeholder='请输入用户帐号或手机号或E-mail']    ${username}
    输入    //input[@placeholder='请输入密码']    ${password}
    点击    //button[@class='login-form-button ant-btn ant-btn-primary']


点击左侧菜单
    [Arguments]    ${menu}
    点击    //span[contains(text(),'${menu}')]

权限配置
    [Arguments]    ${username}
    点击    //label[@class='ant-radio-button-wrapper']//span[contains(text(),'用户')]
    点击    //span[@class='targetSty'][contains(text(),'${username}')]
    输入    //input[@class='ant-select-selection-search-input ng-untouched ng-pristine ng-valid']    项目中控台
    点击    //div[@class='ant-select-item-option-content']
    点击    //span[contains(text(),'基础资料')]/preceding::button[1]
    点击    //span[contains(text(),'报表')]/preceding::button[1]
    点击    //span[contains(text(),'TBB图表')]/preceding::button[1]
    点击    //span[contains(text(),'编辑')]
    Sleep    2
    ${text}    获取元素属性值    //span[@class='ng-star-inserted'][text()='发起项目']/following::label[1]    class
    ${result}=    Run Keyword And Return Status    Should Contain    ${text}    checked    ignore_case=True
    IF    ${result}
        点击    //span[@class='ng-star-inserted'][text()='发起项目']/following::label[2]
    END
    点击    //span[contains(text(),'保存')]
    Sleep    2