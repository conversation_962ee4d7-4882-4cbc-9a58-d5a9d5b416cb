from browsermobproxy import Server

from selenium import webdriver
from selenium.webdriver.chrome.options import Options


# 指定browsermob - proxy的可执行文件路径（根据实际安装位置调整）
server = Server("/Users/<USER>/Downloads/browsermob-proxy-2.1.4/bin/browsermob-proxy")
server.start()
# 创建一个代理实例
proxy = server.create_proxy()
chrome_options = Options()
abc=proxy.proxy
# 设置代理服务器的地址和端口
chrome_options.add_argument('--ignore-certificate-errors')
chrome_options.add_argument('--proxy-server=%s' % abc)
# 创建ChromeDriver实例，并传入配置好的选项
driver = webdriver.Chrome(options = chrome_options)

proxy.new_har("abc")
driver.get("https://www.baidu.com")
# 这里的"example - har"是HAR文件的名称，可以根据需要进行更改

har = proxy.har
# print(har)
# 可以对har进行分析，例如查看请求的URL、响应状态码等信息
print(har['log']['entries'])
for entry in har['log']['entries']:
    if  entry['response']['status']==204:
        print(entry['request']['url'])
        print(entry['response']['status'])

driver.quit()
server.stop()
