import hashlib
import uuid
import time


def generate_token(username, password):
    # 基础数据准备
    timestamp = str(int(time.time() * 1000))  # 当前时间戳(毫秒级)
    unique_id = str(uuid.uuid4())  # 生成唯一标识符

    # 组合待哈希字符串
    # 格式: 用户名+密码+时间戳+唯一ID+盐值
    salt = "AthenaSecuritySalt2025"  # 盐值需与服务端保持一致
    combined = f"{username}{password}{timestamp}{unique_id}{salt}"

    # 生成SHA-256哈希
    sha256 = hashlib.sha256()
    sha256.update(combined.encode('utf-8'))
    hash_digest = sha256.hexdigest()

    # 转换为UUID格式
    # 取前32位哈希值，按UUID格式重新组合
    formatted_token = f"{hash_digest[:8]}-{hash_digest[8:12]}-{hash_digest[12:16]}-{hash_digest[16:20]}-{hash_digest[20:32]}"

    return formatted_token


# 测试用例
if __name__ == "__main__":
    username = "TestAthenaAutoTestAi001"
    password = "TestAthenaAutoTestAi001"

    generated_token = generate_token(username, password)
    print(f"生成的Token: {generated_token}")
    print(f"目标Token:   a4cafc9b-712b-419e-9332-f0117310cc43")

    # 由于时间戳的存在，每次生成的Token都会不同
    # 此代码仅演示生成逻辑，实际验证需服务端配合