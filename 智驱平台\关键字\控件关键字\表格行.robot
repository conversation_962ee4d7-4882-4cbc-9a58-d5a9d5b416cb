*** Settings ***
Library           SeleniumLibrary
Resource          ../系统关键字/web.robot
Resource          ../../元素/Athena平台元素.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../业务关键字/作业授权.robot
Resource          ../系统关键字/接口.robot

*** Keywords ***
自动新增行
     [Arguments]    ${rowname}
     当前页面可见   //app-dynamic-input-display[@placeholder='${rowname}']
     鼠标悬停  //app-dynamic-input-display[@placeholder='${rowname}']
     #当前页面可见   //input[@placeholder='${rowname}']
     点击  //app-dynamic-input-display[@placeholder='${rowname}']
     输入  //input[@placeholder='${rowname}']    202401
     Sleep    3
     Press Keys   //input[@placeholder='${rowname}']    ENTER
     Sleep    3
     当前页面可见  //div[@class='ath-tag-inner']/span[text()='未保存']
     当前页面可见  //app-dynamic-input-display[@placeholder='${rowname}']
     鼠标悬停  //app-dynamic-input-display[@placeholder='${rowname}']
     点击  //app-dynamic-input-display[@placeholder='${rowname}']
     输入  //input[@placeholder='${rowname}']    202402
     Sleep     3
     当前页面可见   //button/span[contains(text(),'保存')]
     点击   //button/span[contains(text(),'保存')]
     Sleep     3
     当前页面不可见元素  //div[@class='ath-tag-inner']/span[text()='未保存']

判断表格存在数据则删除
     [Arguments]    ${columnkey}
     ${table_element_exists}    判断元素是否可见    xpath=//*[@col-id='${columnkey}']/div/span/cell-renderer/div/div/div/app-dynamic-input-display[@title!='']/ath-control-wrapper/div
     Run Keyword If    ${table_element_exists}   删除表格数据

删除表格数据
    点击  //i[@nzpopoverplacement='bottomLeft']
    当前页面可见   //li[contains(text(),'选择本页')]
    点击   //li[contains(text(),'选择本页')]
    元素存在则点击     //dynamic-data-delete-button/div/button/span[text()='删除']
    Sleep    3
    当前页面可见    //div[contains(@class,'ant-modal-content')]
    元素存在则点击    //button/span[contains(text(),'确定')]
    当前页面可见字符    操作成功


手动新增行
     [Arguments]    ${rowname}     ${content}
     当前页面可见   //button/span[contains(text(),'新增行')]
     鼠标悬停  //button/span[contains(text(),'新增行')]
     js点击  //button/span[contains(text(),'新增行')]
     当前页面可见  //span[@class='error-text-tip sync-errors-tip ng-star-inserted'] 
     当前页面可见  //app-dynamic-input-display[@placeholder='${rowname}']
     鼠标悬停   //app-dynamic-input-display[@placeholder='${rowname}']
     点击  //app-dynamic-input-display[@placeholder='${rowname}']
     输入  //input[@placeholder='${rowname}']     ${content}
     Sleep    3
     当前页面可见  //button/span[contains(text(),'保存')]
     点击   //button/span[contains(text(),'保存')]
     Sleep    3
     当前页面不可见元素  //div[@class='ath-tag-inner']/span[text()='未保存']

单元格开窗新增行
     进入发起项目菜单
     点击  //div[@id='card_M9d63_b4e2_mainline_project_0001']
     Sleep    10
     打开非输入型开窗  product_no
     # 当前页面可见   //dynamic-operation-editor[@class='table-cell-dynamic-form-control ng-star-inserted']
     # 鼠标悬停  //dynamic-operation-editor[@class='table-cell-dynamic-form-control ng-star-inserted']
     # 当前页面可见   //span[@class='edit hidden-icon']  3
     # 鼠标悬停   //span[@class='edit hidden-icon']
     # 点击  //span[@class='edit hidden-icon']
     # Sleep  10
     # 当前页面可见   //ath-open-window   3
     点击   //div[@class='ath-input-content-box ng-star-inserted']
     输入  //div[@class='ath-input-content-box ng-star-inserted']/input  智能音箱
     Press Keys   //div[@class='ath-input-content-box ng-star-inserted']/input   ENTER
     Sleep    3
     鼠标悬停  //*[@id="myGrid"]/ag-grid-angular/div/div[2]/div[2]/div[1]/div[1]/div/div/ath-patch-header-renderer/label/span[1]/span
     点击  //*[@id="myGrid"]/ag-grid-angular/div/div[2]/div[2]/div[1]/div[1]/div/div/ath-patch-header-renderer/label/span[1]/span
     当前页面可见   //div[@class='cdk-virtual-scroll-content-wrapper']
     点击  //button[@class='ath-btn ant-btn ant-btn-primary ng-star-inserted active']
     当前页面可见   //span[@class='ellipsis'][@title='智能音箱']

checkbox全选
     [Arguments]    ${datanum}
     点击  //i[@nzpopoverplacement='bottomLeft']
     当前页面可见   //li[contains(text(),'选择全部')]
     点击   //li[contains(text(),'选择全部')]
     Comment   当前页面可见   //div[@class='status-bar']/span[contains(text(),'${datanum}')]
     当前页面可见    //ath-patch-header-renderer/label/span[@class='ant-checkbox ant-checkbox-checked']
checkbox多选
     鼠标悬停  //div[@aria-label='Press SPACE to select this row.'][1]/div[@col-id="$$_patch"]
     点击  //div[@aria-label='Press SPACE to select this row.'][1]/div[@col-id="$$_patch"]
     鼠标悬停  //div[@aria-label='Press SPACE to select this row.'][2]/div[@col-id="$$_patch"]
     点击  //div[@aria-label='Press SPACE to select this row.'][2]/div[@col-id="$$_patch"]
     当前页面可见  //ath-patch-header-renderer/label/span[@class='ant-checkbox ant-checkbox-indeterminate']

checkbox取消全选
     点击  //i[@nzpopoverplacement='bottomLeft']
     当前页面可见   //li[contains(text(),'取消全选')]
     点击  //li[contains(text(),'取消全选')]
     当前页面可见    //div[@class='status-bar']/span/span[contains(text(),'0')]

     
通栏合计行
     [Arguments]    ${rowname}   ${content}  
     当前页面可见   //button/span[contains(text(),'新增行')]
     点击  //button/span[contains(text(),'新增行')]
     当前页面可见  //div[@class='cell-padding white-bg right']
     鼠标悬停  //app-dynamic-input-display[@placeholder='${rowname}']
     点击  //app-dynamic-input-display[@placeholder='${rowname}']
     输入  //input[@placeholder='${rowname}']  ${content}
     Press Keys    //input[@placeholder='${rowname}']   ENTER
     当前页面可见  //div[contains(text(),'合计行:$${content}%')]
     元素存在则点击    //button/span[contains(text(),'保存')]
     Sleep   5
     当前页面不可见元素  //div[@class='ath-tag-inner']/span[text()='未保存']          

分栏合计行
     [Arguments]    ${rowname1}   ${rowname2}  ${content}  
     当前页面可见   //button/span[contains(text(),'新增行')]
     点击  //button/span[contains(text(),'新增行')]
     当前页面可见  //*[@col-id="${rowname1}"]/div/span/app-total-line-renderer/div
     #合计字段1
     鼠标悬停  //div[@col-id='${rowname1}']/div/span/cell-renderer
     点击  //div[@col-id="${rowname1}"]/div/span/cell-renderer
     输入  //input[@name='${rowname1}']  ${content}
     #合计字段2
     鼠标悬停  //div[@col-id='${rowname2}']/div/span/cell-renderer
     点击  //div[@col-id='${rowname2}']/div/span/cell-renderer
     输入   //div/input[contains(@class,'ant-input-number-input')]   ${content}
     Press Keys    //div/input[contains(@class,'ant-input-number-input')]    ENTER
     当前页面可见  //div[@title='合计行1:$${content}']
     元素存在则点击    //button/span[contains(text(),'保存')]
     Sleep    10
     当前页面不可见元素  //div[@class='ath-tag-inner']/span[text()='未保存']

表头图标
     #必填项
     当前页面可见  css:span.ag-header-cell-text.required
     #字段说明
     当前页面可见  //span[@class='ag-icon ag-icon-shuoming']
     鼠标悬停  //span[@class='ag-icon ag-icon-shuoming']
     点击  //span[@class='ag-icon ag-icon-shuoming']
     当前页面可见  //div[@class='ath-table-head-description ng-star-inserted']
     点击  //div[@class='title'][contains(text(),'表格_高级查询作业测试')]
     #复合字段说明
     当前页面可见  //span[@ref='subtitle']
     鼠标悬停  //span[@ref='subtitle']
     点击  //span[@ref='subtitle']
     当前页面可见  //div[contains(@class,'ant-tooltip-inner')]


发起手动拆行项目卡
     [Arguments]  ${rowkey2}    ${rowkey3}
     ${rowkey1}    生成毫秒时间戳
     点击顶部菜单    全部
     点击    //span[contains(text(),'发起项目')]
     当前页面可见    //div[@id='card_PM_d86b52c210000615']
     点击    //div[@id='card_PM_d86b52c210000615']
     Sleep   10
     元素存在则点击  //div/ath-input-text/ath-input-group
     输入  //input[@name='product_no']  ${rowkey1}
     Sleep    3
     元素存在则点击  //div/app-dynamic-input-display[@placeholder='产品名称']
     输入  //input[@placeholder='产品名称']  ${rowkey2}
     Sleep    3
     元素存在则点击  //div/app-dynamic-amount-input-display
     输入  //input[@placeholder='产品价格']  10
     Sleep    3
     元素存在则点击  //div/app-dynamic-input-display[@placeholder='产品数量']
     输入    //div[@class='ant-input-number-input-wrap']/input    ${rowkey3}
     点击    //span[@class='create-project-des']
     Sleep    3
     元素存在则点击  //button/span[contains(text(),'提交')]
     当前页面可见  //div[contains(@class,'ant-modal-confirm-conten')]
     元素存在则点击  //button/span[contains(text(),'确定')]
     当前页面可见字符  发起成功

任务卡表格手动拆行
     #进入任务卡详情
    判断后跳转到目标页面    /todo/task
    Sleep   10
    点击    //div[@class='toolbar']/app-todo-filter
    切换筛选类型   常规筛选
    点击  //button[@class='ath-btn ant-btn ant-btn-default']/span[text()='重置']
    #筛选拆行任务卡
    输入  //div/app-todo-comm-filter/div[1]/app-todo-filter-item/ath-tree-select/div/nz-select-search/input  产品手动拆行
    元素存在则点击   //div/span[not(contains(text(), '采购'))]/span[text()='产品手动拆行']
    点击  //div/app-todo-comm-filter/div[1]/app-todo-filter-item/ath-tree-select/div/nz-select-search/input 
    输入    //div[@class="filter-list-container ng-star-inserted"]/app-todo-comm-filter/div[2]/app-todo-filter-item/ath-tree-select/div/nz-select-search/input    未读
    鼠标悬停    //span[@class='font-highlight'][text()='未读']
    点击  //span[@class='font-highlight'][text()='未读']
    当前页面可见    //div[contains(@class,'athena-selected-panel')]
    点击    //button[@class='ath-btn ant-btn ant-btn-primary']
    当前页面可见  //div[@class='todo-card-item-container ng-star-inserted']
    点击  //div[@class='todo-card-item-container ng-star-inserted']
    Sleep   10 
    #操作拆行
    当前页面可见  //button/span[contains(text(),'表格拆行')]
    点击  //button/span[contains(text(),'表格拆行')]
    当前页面可见  //div[@class='split-row-img ng-star-inserted']
    点击  //div[@class='split-row-img ng-star-inserted']
    当前页面可见  //span[@class='error-text-tip sync-errors-tip ng-star-inserted']
    点击  //button/span[contains(text(),'取消拆行')]
    Sleep    3
    当前页面不可见元素  //span[@class='error-text-tip sync-errors-tip ng-star-inserted']
    #提交手动拆行
    当前页面可见  //button/span[contains(text(),'表格拆行')]
    js点击    //button/span[contains(text(),'表格拆行')]
    当前页面可见  //div[@class='split-row-img ng-star-inserted']
    点击  //div[@class='split-row-img ng-star-inserted']
    元素存在则点击  //div[@class='ng-star-inserted']/dynamic-ant-editor
    输入  //div[@row-index='1']/*//input[@name='product_number']  10
    Press Keys  //div[@row-index='1']/*//input[@name='product_number']     ENTER
    当前页面可见  //span[@title='10']
    元素存在则点击  //button/span[contains(text(),'提交')]
    当前页面可见  //div[contains(@class,'ant-modal-confirm-conten')]
    元素存在则点击  //button/span[contains(text(),'确定')]
    当前页面可见字符  上传成功

重置任务筛选
     判断后跳转到目标页面    /todo/task
     Sleep   10
     点击    //div[@class='toolbar']/app-todo-filter
     当前页面可见  //button[@class='ath-btn ant-btn ant-btn-default']/span[text()='重置']
     点击  //button[@class='ath-btn ant-btn ant-btn-default']/span[text()='重置']
     元素存在则点击  //button[@class='ath-btn ant-btn ant-btn-primary']
     当前页面不可见元素  //app-todo-filter/app-todo-common-tool/span/div

任务卡表格自动拆行
    判断后跳转到目标页面    /todo/task
    Sleep   10
    点击    //div[@class='toolbar']/app-todo-filter
    点击  //button[@class='ath-btn ant-btn ant-btn-default']/span[text()='重置']
    #筛选拆行任务卡
    输入  //div/app-todo-comm-filter/div[1]/app-todo-filter-item/ath-tree-select/div/nz-select-search/input  产品自动拆行
    元素存在则点击  //div/span/span[text()='产品自动拆行']
    点击  //div/app-todo-comm-filter/div[1]/app-todo-filter-item/ath-tree-select/div/nz-select-search/input 
    输入    //div[@class="filter-list-container ng-star-inserted"]/app-todo-comm-filter/div[2]/app-todo-filter-item/ath-tree-select/div/nz-select-search/input    未读
    鼠标悬停    //span[@class='font-highlight'][text()='未读']
    点击  //span[@class='font-highlight'][text()='未读']
    当前页面可见    //div[contains(@class,'athena-selected-panel')]
    点击    //button[@class='ath-btn ant-btn ant-btn-primary']
    当前页面可见  //div[@class='todo-card-item-container ng-star-inserted']
    点击  //div[@class='todo-card-item-container ng-star-inserted']
    Sleep   10 
    #操作拆行
    元素存在则点击  //div/app-dynamic-amount-input-display
    Press Keys  //cell-renderer/div/div/div/dynamic-amount-input  \\8
    Sleep   3
    Press Keys  //cell-renderer/div/div/div/dynamic-amount-input  ENTER
    当前页面可见  //div[@row-index='1']/*//cell-renderer/div/div/div/app-dynamic-amount-input-display
    #提交拆行
    元素存在则点击  //button/span[contains(text(),'提交')]
    当前页面可见  //div[contains(@class,'ant-modal-confirm-conten')]
    元素存在则点击  //button/span[contains(text(),'确定')]
    当前页面可见字符  上传成功







   



