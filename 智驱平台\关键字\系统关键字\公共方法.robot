*** Settings ***
Library           DateTime
Library           OperatingSystem
Library           String
Library           SeleniumLibrary
Library           Collections
Library           OperatingSystem
Library           BrowserMobProxyLibrary

*** Keywords ***
生成毫秒时间戳
    ${timeStamp}    evaluate    int(time.time() * 1000)
    #为了解决连续两次生成时间戳造成的时间戳值相同的问题，此处加上0.1s的等待
    Sleep    0.1
    RETURN    ${timeStamp}

生成秒时间戳
    ${timeStamp}    evaluate    int(time.time())
    #为了解决连续两次生成时间戳造成的时间戳值相同的问题，此处加上0.1s的等待
    Sleep    0.1
    RETURN    ${timeStamp}

生成随机字符
    ${random_str}    Generate Random String    8    [LETTERS][NUMBERS]
    RETURN    ${random_str}

获取随机数
    ${random}    Evaluate    random.randint(0,100)    random
    Log    ${random}
    Set Global Variable    ${random}
    RETURN    ${random}

获取指定格式时间
    ${CURRENT_TIME}    Evaluate    time.strftime("%Y-%m-%d %H:%M:%S")
    Log    ${CURRENT_TIME}
    Set Global Variable    ${CURRENT_TIME}
    RETURN    ${CURRENT_TIME}

字符串替换
    [Arguments]    ${ORIGINAL_STRING}    ${OLD_SUBSTRING}    ${NEW_SUBSTRING}
    ${replaced_string}    Replace String    ${ORIGINAL_STRING}    ${OLD_SUBSTRING}    ${NEW_SUBSTRING}
    Log    ${replaced_string}
    RETURN    ${replaced_string}

接口检查[废弃]
    [Arguments]    ${browsermob-proxy}    ${OLD_SUBSTRING}    ${NEW_SUBSTRING}
    ## 启动代理服务
    Start Local Server    /Users/<USER>/Downloads/browsermob-proxy-2.1.4/bin/browsermob-proxy
    ## 创建代理
    ${BrowserMob_Proxy}=    Create Proxy
    log    ${BrowserMob_Proxy.proxy}
    New Har    har
    Open Browser    https://www.baidu.com    chrome    options=add_argument("--proxy-server=${BrowserMob_Proxy.proxy}"); add_argument("--ignore-certificate-errors")
    # 在此处添加你的测试操作，如元素点击、文本输入等
    ${har}=    Get Har As JSON
    log    ${har}
    create file    ${EXECDIR}${/}file.har    ${har}
    log to console    Browsermob Proxy HAR file saved as ${EXECDIR}${/}file.har
    Stop Local Server

字符串分割
    [Arguments]    ${ORIGINAL_STRING}    ${DELIMITER}
    ${split_list}    Split String    ${ORIGINAL_STRING}    ${DELIMITER}
    Log    ${split_list}
    RETURN    ${split_list}

