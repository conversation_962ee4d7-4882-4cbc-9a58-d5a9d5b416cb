*** Settings ***
Library    DatabaseLibrary
Library    RequestsLibrary
Library    Collections
Library    String

*** Variables ***
# 数据库连接信息
${DB_HOST}    172.16。2.154
${DB_PORT}    3306
${DB_USER}    root
${DB_PASSWORD}    Password123@mysql
${DB_NAME}    metersphere
# 企业微信 Webhook URL
#${WEBHOOK_URL}    /cgi-bin/webhook/send?key=8d14b45b-29a2-421a-b709-2b411151c18a
${WEBHOOK_URL}    /cgi-bin/webhook/send?key=28698546-b4f6-430b-ba90-987f9ddec431

*** Test Cases ***
Query And Send Scores
    # 连接到数据库
    Connect To Database    pymysql    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${DB_HOST}    ${DB_PORT}
    # 执行 SQL 查询
    ${query_result}=    Query    select person_name,remaining_score from remaining_scores
    # 关闭数据库连接
    Disconnect From Database
    # 构建 Markdown 表格
    ${table_header}=    Catenate    SEPARATOR=     **姓名  剩余分数**
    ${table_content}=    Set Variable    ${table_header}
    FOR    ${row}    IN    @{query_result}
        ${name}=    Get From List    ${row}    0
        ${score}=    Get From List    ${row}    1
        ${table_row}=    Catenate    SEPARATOR=    > ${name} <font color="comment">${score}</font>
        ${table_content}=    Catenate    SEPARATOR=\n    ${table_content}    ${table_row}
    END
    # 构建企业微信消息体
    ${markdown_message}=    Catenate    SEPARATOR=    UI自动化剩余分数\n\n    ${table_content}
    ${body}    Create Dictionary    content=${markdown_message}
    ${message_body}=    Create Dictionary    msgtype=markdown    markdown=${body}
    # 发送消息到企业微信 Webhook
    Log Many    ${message_body}
    ${headers}    Create Dictionary    Content-Type=application/json
    Create Session    mysession    https://qyapi.weixin.qq.com    headers=${headers}    disable_warnings=1
    ${response}=    POST On Session    mysession    url=${WEBHOOK_URL}    json=${message_body}
    # 检查响应状态码
    Should Be Equal As Strings    ${response.status_code}    200
    Log    消息发送结果: ${response.json()}