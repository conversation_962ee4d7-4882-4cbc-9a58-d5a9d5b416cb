from selenium import webdriver
from selenium.webdriver.common.by import By
import time
import requests

# 启动浏览器
driver = webdriver.Chrome()
# 创建WebDriver实例，这里以Chrome为例，你可以根据实际情况更换浏览器
driver = webdriver.Chrome()
# 打开指定网页，将URL替换成你实际要操作的页面地址
driver.get("https://athena.digiwincloud.com.cn/login")
# 通过name属性定位用户ID文本框，并输入内容
user_id_textbox = driver.find_element(By.NAME, "userId")
user_id_textbox.send_keys("<EMAIL>")
time.sleep(2)
# 通过name属性定位密码文本框，并输入内容
password_textbox = driver.find_element(By.NAME, "password")
password_textbox.send_keys("@cjm820412000")
time.sleep(2)
# 通过class属性定位按钮并点击，注意这里用By.CLASS_NAME时如果class有多个值，要使用其中一个唯一能区分的部分
# 通过xpath定位按钮元素
button = driver.find_element(By.XPATH, "//button[@class='ant-btn ant-btn-primary']")
# 点击定位到的按钮
button.click()
time.sleep(10)
# 可以添加适当的等待时间，便于观察操作后的页面情况（实际应用中可能需要更合理的等待策略，比如显式等待等）
driver.get("https://athena.digiwincloud.com.cn/todo/task")
# 获取当前页面的URL
current_url = driver.current_url
time.sleep(10)
# 使用requests库发送HEAD请求获取header信息
response = requests.get(current_url)
headers = response.request.headers
# 打印header信息
print(headers)