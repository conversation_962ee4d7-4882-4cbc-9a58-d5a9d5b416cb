*** Settings ***
Suite Setup       Run Keywords    环境设定
Suite Teardown    Run Keywords    关闭浏览器
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/业务关键字/开发平台.robot
Resource          ../关键字/业务关键字/公共方法.robot
Resource          ../配置/域名.robot
Resource          ../配置/全局参数.robot
Resource          ../元素/开发平台元素.robot
Library           Collections
Library           OperatingSystem
Resource          ../关键字/系统关键字/接口.robot

*** Variables ***

*** Test Cases ***
机制增删改查
    [Documentation]    陈金明
    登录开发平台    ${username}    ${password}    ${tenantId}
    点击顶部菜单    解决方案中心
    搜索应用    purchase99
    进入应用配置页    purchase99
    选择浏览器窗体    -1
    点击左侧菜单    机制设计
    新增机制
    机制列表搜索    ${mechanismName}
    编辑机制    ${mechanismName}
    复制机制    ${mechanismName}
    删除机制    ${mechanismNameCopy}
    删除机制    ${mechanismName}
    [Teardown]    Run Keywords    关闭浏览器

机制原理增删改
    [Documentation]    陈金明
    登录开发平台    ${username}    ${password}    ${tenantId}
    点击顶部菜单    解决方案中心
    搜索应用    purchase99
    进入应用配置页    purchase99
    选择浏览器窗体    -1
    点击左侧菜单    机制设计
    新增机制
    进入机制设计页    ${mechanismName}
    新增机制原理    机制原理名称    机制原理描述
    修改机制原理    机制原理名称    机制原理名称修改    机制原理描述修改
    删除机制原理    机制原理名称修改
    回到上一页
    机制列表搜索    ${mechanismName}
    删除机制    ${mechanismName}
    [Teardown]    Run Keywords    关闭浏览器

指派能力-按人员ID
    [Documentation]    陈金明
    登录开发平台    ${username}    ${password}    ${tenantId}
    点击顶部菜单    解决方案中心
    搜索应用    purchase99
    进入应用配置页    purchase99
    选择浏览器窗体    -1
    点击左侧菜单    机制设计
#    新增机制
#    进入机制设计页    ${mechanismName}
#    新增机制原理    机制原理名称    机制原理描述
#    新增指派能力-特定人员    指派能力-按人员ID    指派能力描述    人员    何元园    #支持,人员,职能和部门成员
    #只支持发测试区,未完待续
    应用发布    阿里测试区    AthenaQCTestW
    [Teardown]    Run Keywords    关闭浏览器

指派能力-按职能ID
    [Documentation]    陈金明
    登录开发平台    ${username}    ${password}    ${tenantId}
    点击顶部菜单    解决方案中心
    搜索应用    purchase99
    进入应用配置页    purchase99
    选择浏览器窗体    -1
    点击左侧菜单    机制设计
    新增机制
    进入机制设计页    ${mechanismName}
    新增机制原理    机制原理名称    机制原理描述
    新增指派能力-特定人员    指派能力-按职能ID    指派能力描述    职能    应付会计    #支持,人员,职能和部门成员
    回到上一页
    机制列表搜索    ${mechanismName}
    删除机制    ${mechanismName}
    [Teardown]    Run Keywords    关闭浏览器

指派能力-按部门人员
    [Documentation]    陈金明
    登录开发平台    ${username}    ${password}    ${tenantId}
    点击顶部菜单    解决方案中心
    搜索应用    purchase99
    进入应用配置页    purchase99
    选择浏览器窗体    -1
    点击左侧菜单    机制设计
    新增机制
    进入机制设计页    ${mechanismName}
    新增机制原理    机制原理名称    机制原理描述
    新增指派能力-特定人员    指派能力-按部门人员    指派能力描述    部门人员    预设部门    #支持,人员,职能和部门成员
    回到上一页
    机制列表搜索    ${mechanismName}
    删除机制    ${mechanismName}
    [Teardown]    Run Keywords    关闭浏览器

#机制名称输入限制校验
#    登录开发平台    ${username}    ${password}    ${tenantId}
#    点击顶部菜单    解决方案中心
#    搜索应用    purchase99
#    进入应用配置页    purchase99
#    选择浏览器窗体    -1
#    点击左侧菜单    机制设计
#    机制名称输入校验    为空
#    机制名称输入校验    长度超过50
#
#test用例
#    #${token}    Set Variable    055434dc-00e4-4f53-a343-5955d32021f7
#    #Set Global Variable    ${token}
#    #接口.POST    knowledgemaps-test.apps.digiwincloud.com.cn    /restful/service/knowledgegraph/Mechanism/MechanismExecuteParamsUpdate    {"tenantId": "TestAthenaAutoTestKm001"}    AthenaAutoTest
