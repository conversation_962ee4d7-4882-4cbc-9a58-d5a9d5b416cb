*** Settings ***
Library           SeleniumLibrary
Resource          ../../元素/开发平台元素.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/web.robot
Resource          公共方法.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../系统关键字/web.robot
Resource          Athena平台控件.robot
Resource          Athena平台.robot
Resource          首页.robot
Resource          待办.robot
Resource          ../业务关键字/PCC.robot

*** Keywords ***
生成多人执行任务卡
    [Arguments]    ${story_code}    ${story_name}
    点击    //*[contains(text(),"小AI-五要素基线用例专用-勿动_项目_0001")]
    Sleep    2
    点击    //app-dynamic-input-display[@placeholder='需求编号']
    Sleep    1
    Input Text    //input[@placeholder="需求编号"]    ${story_code}
    Sleep    1
    点击    //app-dynamic-input-display[@placeholder='需求名称']
    Sleep    1
    Input Text    //input[@placeholder="需求名称"]    ${story_name}
    Sleep    1
    点击    //span[contains(text(),"提交")]
    Sleep    1
    点击    //span[contains(text(),"确定")]
    Sleep    3

团队任务-进入任务卡详情转派给自己存在重复卡
    [Arguments]    ${task_name}     ${req_random}    ${ownerUsername}
    判断后跳转到目标页面    /todo/team-tasks
    重置待办筛选  团队任务
    按任务/项目类型筛选    ${task_name}     团队任务
    交接任务/项目名称搜索   ${req_random}
    Sleep    3
    点击    //div[contains(text(),'New')]
    当前页面可见    //span[contains(text(),'${task_name}')]
    js点击    //div[@class='ant-dropdown-trigger'][@nztrigger='click']
    当前页面可见    //li[contains(text(),'转派')]
    js点击    //li[contains(text(),'转派')]
    当前页面可见    //p[@class='notice']
    点击    //app-person-in-charge-select/div/child::*
    当前页面可见    //*[@class='cdk-overlay-pane']/ath-option-container
    输入    //app-person-in-charge-select/div/child::*//input[contains(@class,'ant-select-selection-search-input')]     ${ownerUsername}
    当前页面可见    //span[text()='${ownerUsername}']
    点击    //span[text()='${ownerUsername}']
    当前页面可见    //ath-select-item/span[contains(text(),'${ownerUsername}')]
    元素存在则点击    //span[contains(text(),'确定')]
    当前页面可见    //div[text()='被转派人员的任务中存在相同的任务卡片，继续转派则会保留一张卡片，不影响原业务进行。是否继续转派？']
    元素存在则点击    //span[contains(text(),'继续')]
    
断言重复卡转派结果
    [Arguments]    ${req_random}
    点击顶部菜单    全部
    Sleep    3
    点击    //div[contains(text(),'历史项目/任务')]
    当前页面可见    //span[contains(text(),'${req_random}')]
    

团队任务-进入任务卡详情转派给自己，无重复卡
    [Arguments]    ${task_name}     ${ownerUsername}
    判断后跳转到目标页面    /todo/team-tasks
    重置待办筛选  团队任务
    按任务/项目类型筛选    ${task_name}     团队任务
    点击    //div[contains(text(),'New')]
    当前页面可见    //span[contains(text(),'${task_name}')]
    js点击    //div[@class='ant-dropdown-trigger'][@nztrigger='click']
    当前页面可见    //li[contains(text(),'转派')]
    js点击    //li[contains(text(),'转派')]
    当前页面可见    //p[@class='notice']
    点击    //app-person-in-charge-select/div/child::*
    当前页面可见    //*[@class='cdk-overlay-pane']/ath-option-container
    输入    //app-person-in-charge-select/div/child::*//input[contains(@class,'ant-select-selection-search-input')]     ${ownerUsername}
    当前页面可见    //span[text()='${ownerUsername}']
    点击    //span[text()='${ownerUsername}']
    当前页面可见    //ath-select-item/span[contains(text(),'${ownerUsername}')]
    元素存在则点击    //span[contains(text(),'确定')]
    当前页面可见字符  转派成功

断言任务卡转派结果
    [Arguments]    ${task_name}
    判断后跳转到目标页面    /todo/task
    视图模式设定    列表
    重置待办筛选  我的任务
    按任务/项目类型筛选    ${task_name}     我的任务
    按任务来源筛选  转派
    当前页面可见    //div[@row-index='0']//*[@class='ag-cell-value']/cell-render/div/div/cell-text/div[contains(@title,'转派')]

被转派人-提交任务
    [Arguments]    ${tagname}
    Sleep    5
    跳转到待办页    /todo/task
    任务/项目名称搜索    ${tagname}
#    判断交接来源是否正常展示
    鼠标悬停    //span[contains(text(),"${tagname}")]
    判断元素是否可见    //span[contains(text(),"供应商1")]
    点击将要转派的任务卡    ${tagname}
    Sleep    3
    点击    //span[contains(text(),"提交")]
    Sleep    3
    点击    //span[contains(text(),"确定")]


点击将要转派的任务卡
    [Arguments]    ${tagname}
    Sleep    2
    点击    //span[contains(text(),"${tagname}")]




