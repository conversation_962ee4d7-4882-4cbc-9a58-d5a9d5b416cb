*** Settings ***
Documentation     高伟
Library           SeleniumLibrary
Resource          ../关键字/业务关键字/Athena平台控件.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/公共方法.robot
Test Setup        Run Keywords    环境设定
Test Teardown     Close Browser



*** Test Cases ***
IM-私聊消息发出
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${TenantName}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    ${Dept}    Set Variable If    '${ENV}'=='paas'    小AI平台部门    '${ENV}'=='huawei.test'    小AI平台部门    '${ENV}'=='huawei.prod'    小AI平台部门    '${ENV}'=='microsoft.prod'    小AI平台部门
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    点击即时对话
    点击通讯录
    点击通讯录中租户名称    ${TenantName}
    点击通讯录中租户下的部门名称    ${Dept}
    点击租户下部门的人员名称    高伟
    点击发消息入口
    点击输入框并输入私聊内容    你好


IM-私聊消息发出后，标记为结论后取消标记
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${TenantName}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    ${Dept}    Set Variable If    '${ENV}'=='paas'    小AI平台部门    '${ENV}'=='huawei.test'    小AI平台部门    '${ENV}'=='huawei.prod'    小AI平台部门    '${ENV}'=='microsoft.prod'    小AI平台部门
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    点击即时对话
    点击通讯录
    点击通讯录中租户名称    ${TenantName}
    点击通讯录中租户下的部门名称    ${Dept}
    点击租户下部门的人员名称    高伟
    点击发消息入口
    点击输入框并输入私聊内容    把我标记为结论${唯一标识}
    标记私聊内容为结论后取消标记并撤回    把我标记为结论${唯一标识}


IM-通过私聊创建私人群聊
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    wgzy0001    '${ENV}'=='huawei.prod'    qcuser009    '${ENV}'=='microsoft.prod'    Quality001
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    Wgzy0001    '${ENV}'=='huawei.prod'    qcuser009    '${ENV}'=='microsoft.prod'    Quality001
    ${TenantName}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    ${Dept}    Set Variable If    '${ENV}'=='paas'    小AI平台部门    '${ENV}'=='huawei.test'    小AI平台部门    '${ENV}'=='huawei.prod'    小AI平台部门    '${ENV}'=='microsoft.prod'    小AI平台部门
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    点击即时对话
    点击通讯录
    点击通讯录中租户名称    ${TenantName}
    点击通讯录中租户下的部门名称    ${Dept}
    点击租户下部门的人员名称    高伟
    点击发消息入口
    点击添加成员入口后点击添加成员
    点击添加群聊时弹出框下的部门名称    ${Dept}
    点击添加群聊时弹出况下部门人员名称    黄蕾    
    添加群聊成员后，点击确定
    解散群聊    黄蕾


IM-私聊消息中创建行事历
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${TenantName}    Set Variable If    '${ENV}'=='paas'    自动化测试环境     '${ENV}'=='huawei.test'    自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    ${Dept}    Set Variable If    '${ENV}'=='paas'    小AI平台部门    '${ENV}'=='huawei.test'    小AI平台部门    '${ENV}'=='huawei.prod'    小AI平台部门    '${ENV}'=='microsoft.prod'    小AI平台部门
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    点击即时对话
    点击通讯录
    点击通讯录中租户名称    ${TenantName}
    点击通讯录中租户下的部门名称    ${Dept}
    点击租户下部门的人员名称    黄蕾
    点击发消息入口
    点击新建任务    测试私聊创建行事历


