*** Settings ***
Library           SeleniumLibrary
#Library           AutoItLibrary
#Library           RFLib/Base.py
Resource          ../../元素/开发平台元素.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/web.robot
Resource          公共方法.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../系统关键字/web.robot
Resource          Athena平台控件.robot
Resource          Athena平台.robot
Resource          首页.robot
Resource          待办.robot
Resource          ../控件关键字/表格行.robot



*** Keywords ***
#单档多栏
点击作业名称
    [Arguments]    ${jobname}    ${application_name}
    Sleep    3
    滑动元素到可见区域    //*[text()='${jobname}']
    Sleep    3
    ${status}    Run Keyword And Return Status    当前页面可见    //div[@hostkey="base-data-common" and @id="A5d65ea27cAT"]    8
    Sleep    2
    Run Keyword If    ${status}==False    点击    //*[text()='${jobname}']    ELSE    Log    nothing to do
    Sleep    2  
    点击    //span[contains(text(),"${jobname}")]
    Sleep    3



输入-单档多栏作业字段
    [Arguments]    ${orderid}    ${supplierid}    ${date}    ${quota}
    Sleep    3
    ${ordervalue_exist}    Get Element Count    //span[@class="ant-checkbox"]//input[@type="checkbox"]
    Sleep    3
#    Run Keyword If    '${ordervalue_exist}'>'1'    点击删除作业    ${orderid}    ELSE    Log    nothing to do
    Run Keyword If    '${ordervalue_exist}'>'1'    浏览页直接删除全部数据    ELSE    Log    nothing to do    
    点击    //app-dynamic-input-display[@placeholder='请输入订单ID']
    输入    //input[@placeholder="请输入订单ID"]    ${orderid}
    ${cliksupplier}    按顺序获取元素    //app-dynamic-input-display[@placeholder='请输入供应商ID']    0
    Sleep    1
    点击    ${cliksupplier}
    输入    //input[@placeholder="请输入供应商ID"]    ${supplierid}
    点击    //span[contains(text(),"请输入订单日期")]
    输入    //input[@placeholder="请输入订单日期"]     ${date}
    ${clikquota}    按顺序获取元素    //app-dynamic-input-display[@placeholder='请输入总金额']    0
    Sleep    1
    点击    ${clikquota}
    Sleep    2
    输入    //input[@placeholder="请输入总金额"]    ${quota}
   
点击保存作业
    Sleep    2
    点击    //span[contains(text(),"保存")]


点击数据导出
    Sleep    1
    点击    //span[contains(text(),"数据导出")]
    点击    //li[contains(text(),"导出全部")]
    点击    //span[contains(text(),"确定")]

判断数据导出状态
    Sleep    3
    当前页面可见    //*[contains(text(),"已发起导出，导出结果请在【导出记录】中查看")]
    点击    //*[contains(text(),"我知道了")]

点击删除作业
    [Arguments]    ${id}
    Sleep    3
    ${startTimeLoc}    按顺序获取元素    //span[@class="ant-checkbox-inner"]    0
    Sleep    3
    点击    ${startTimeLoc}
    点击    //span[contains(text(),"删除")]
    点击    //span[contains(text(),"确定")]
    #    判断作业删除成功
    Sleep    3
    ${fieldvalue}    Get Element Count    //div[contains(text(),'${id}')]
    Sleep    3
    Should Be True    '${fieldvalue}'=='0'    数据删除失败



#单档
输入-单档作业字段
    [Arguments]    ${school_id}    ${school_name}    ${shcool_address}    ${date}
#    ${ordervalue_exist}    Run Keyword And Return Status    当前页面可见    //div[contains(text(),'${school_id}')]    8
#    Sleep    2
#    Run Keyword If    '${ordervalue_exist}'=='True'    点击删除作业    ${school_id}    ELSE    Log    nothing to do
#    Sleep    2
    ${ordervalue_exist}    Get Element Count    //span[@class="ant-checkbox"]//input[@type="checkbox"]
    Sleep    3
    Run Keyword If    '${ordervalue_exist}'>'1'    浏览页直接删除全部数据    ELSE    Log    nothing to do
    点击    //span[contains(text(),'新增')]
    输入    //input[@placeholder='请输入学校ID']    ${school_id}
    输入    //input[@placeholder='请输入学校名称']    ${school_name}
    输入    //input[@placeholder='请输入学校地址']    ${shcool_address}
    输入    //input[@placeholder='请输入建校日期']    ${date}

点击-新建
    Sleep    2
    点击    //span[contains(text(),"新建")]

切换作业浏览页Tab
    [Arguments]    ${jobname}
    Sleep    2
    点击    //div[contains(text(),"${jobname}")]

切换维护页Tab
#    [Arguments]    ${tabname}
    Sleep    3
    点击    //div[contains(text(),"维护")]

删除作业-单档
    Sleep    3
    点击    //div[@class='ant-tabs-tabpane ng-star-inserted ant-tabs-tabpane-active']//span[contains(text(),'删除')]
    Sleep    3
    点击    //span[contains(text(),"确定")]

#双档

输入-双档作业字段
    [Arguments]    ${employeeId}    ${Employee_name}    ${positionId}    ${positionName}    ${salary}
    #    判断是否存在数据
#    Sleep    3
#    ${name_exist}    Run Keyword And Return Status    当前页面包含元素    //*[contains(text(),"${Employee_name}")]    8
#    ${name_exist_edit}    Run Keyword And Return Status    当前页面包含元素    //*[contains(text(),"李明")]    8
#    Sleep    2
#    Run Keyword If    ${name_exist}    点击删除作业    ${Employee_name}    ELSE    Log    nothing to do
#    Sleep    2
#    Run Keyword If    ${name_exist_edit}    点击删除作业    ${Employee_name}    ELSE    Log    nothing to do
    ${ordervalue_exist}    Get Element Count    //span[@class="ant-checkbox"]//input[@type="checkbox"]
    Sleep    3
    Run Keyword If    '${ordervalue_exist}'>'1'    浏览页直接删除全部数据    ELSE    Log    nothing to do
    点击    //span[contains(text(),'新增')]
    Sleep    3
    输入    //input[@placeholder='请输入员工ID']    ${employeeId}
    输入    //input[@placeholder='请输入员工姓名']    ${Employee_name}
    点击    //span[contains(text(),"新增行")]
    Sleep    3
    点击    //app-dynamic-input-display[@placeholder='请输入职位ID']
    输入    //input[@placeholder='请输入职位ID']    ${positionId}
    点击    //app-dynamic-input-display[@placeholder='请输入职位名称']
    输入    //input[@placeholder='请输入职位名称']    ${positionName}
    点击    //app-dynamic-input-display[@placeholder='请输入薪水']
    输入    //input[@placeholder='请输入薪水']    ${salary}
    点击-新建

删除作业-双档
    Sleep    3
    点击    //div[@class='dynamic-layout-content-container']//dynamic-data-delete-button[@class='ng-star-inserted']//span[contains(text(),"删除")]
    Sleep    3
    点击    //span[contains(text(),"确定")]


# 多档
新增多档单头数据
    [Arguments]    ${supplier_id}    ${supplier_name}    ${shcool_address}
#    Sleep    3
#    ${element_exists}    Run Keyword And Return Status    当前页面可见字符    ${supplier_name}    8
#    Run Keyword If    ${element_exists}    浏览页删除全部数据    多档的作业    ELSE     Log    nothing to do
    ${ordervalue_exist}    Get Element Count    //span[@class="ant-checkbox"]//input[@type="checkbox"]
    Sleep    3
    Run Keyword If    '${ordervalue_exist}'>'1'    浏览页直接删除全部数据    
    Sleep    3
    点击    //span[contains(text(),'新增')]
    输入     //input[@placeholder='请输入供应商编号']    ${supplier_id}
    输入    //input[@placeholder='请输入供应商名称']    ${supplier_name}
    输入    //input[@placeholder='请输入供应商联系方式']    ${shcool_address}

新增多档单身数据
    [Arguments]    ${commodity_type}    ${plan_volume}    ${purchase_price}    ${date}
    Sleep    3
    点击    //*[@class="ath-tab-normal-title ng-star-inserted"]//*[contains(text(),"商品信息")]
#    ${new_row_one}    按顺序获取元素    //*[contains(text(),"新增行")]    0
#    Sleep    1
#    点击    ${new_row_one}
    点击    //*[contains(text(),'采购信息')]/following::span[contains(text(),'新增行')][1]
    点击    //app-dynamic-input-display[@placeholder='请输入商品种类']
    输入    //input[@placeholder='请输入商品种类']    ${commodity_type}
    点击    //app-dynamic-input-display[@placeholder='请输入计划采购量']
    输入    //input[@placeholder='请输入计划采购量']    ${plan_volume}
    点击    //*[@class="ath-tab-normal-title ng-star-inserted"]//*[contains(text(),"采购信息")]
    点击     //*[contains(text(),'采购信息')]/following::span[contains(text(),'新增行')][2]
    点击    //app-dynamic-input-display[@placeholder='请输入采购单价']
    输入    //input[@placeholder='请输入采购单价']    ${purchase_price}
    点击    //span[@class="placeholder ng-star-inserted" and contains(text(),'请输入入库时间')]
    输入    //input[@placeholder='请输入入库时间']    ${date}

删除作业-多档
    Sleep    3
    点击    //div[@class='dynamic-layout-content-container']//dynamic-data-delete-button[@class='ng-star-inserted']//span[contains(text(),"删除")]
    点击    //span[contains(text(),"确定")]

进入维护-删除多档数据
    Sleep    3
    点击    //button[@class='ath-btn ant-btn ath-btn-operation ng-star-inserted']//span[contains(text(),"维护")]
    Sleep    3
    删除作业-多档


#作业查询
基础数据录入作业列表页，根据作业名称查询
    [Arguments]    ${job_name}    ${appli_name}    ${text}
    Sleep    3
    点击    //div[contains(text(),"搜索")]
    Sleep    3
    输入    //input[@class="ant-select-selection-search-input ng-untouched ng-pristine ng-valid"]    ${job_name}
    Sleep    3
    点击    //span[@class='font-highlight']
    Sleep    3
    Page Should Contain    ${text}
    


#整单操作

点击复制(整单操作)
    [Arguments]    ${inputcontent}
#    Wait Until Element Is Visible    //span[contains(text(),"复制")]
    Sleep    3
    点击    //ath-header//span[contains(text(),'复制')]
    Sleep    2
    输入    //input[@placeholder='${inputcontent}']    2


点击编辑(整单操作)
    [Arguments]    ${inpputcontent}    ${inputname}
#    Wait Until Element Is Visible    //span[contains(text(),"编辑")]
    点击    //ath-header//span[contains(text(),'编辑')]
    Sleep    2
    Clear Element Text    //input[@placeholder='${inpputcontent}']
    输入    //input[@placeholder='${inpputcontent}']    ${inputname}
    点击    (//*[@class="ag-center-cols-viewport"])[2]
    点击    //span[contains(text(),"保存")]


点击新增(整单操作)
#    Wait Until Element Is Visible    //ath-header//span[contains(text(),'新增')]
    点击    //ath-header//span[contains(text(),'新增')]
    Sleep    3


输入单头-多档（整单操作）
    [Arguments]    ${supplier_id}    ${supplier_name}    ${shcool_address}
    Sleep    5
    输入     //input[@placeholder='请输入供应商编号']    ${supplier_id}
    Sleep    3
    输入    //input[@placeholder='请输入供应商名称']    ${supplier_name}
    Sleep    3
    输入    //input[@placeholder='请输入供应商联系方式']    ${shcool_address}

输入单身-多档（整单操作）
    [Arguments]    ${commodity_type}    ${plan_volume}    ${purchase_price}    ${date}
    点击    //*[@class="ath-tab-normal-title ng-star-inserted"]//*[contains(text(),"商品信息")]
#    ${new_row_one}    按顺序获取元素    //span[contains(text(),"新增行")]    0
#    Sleep    1
#    点击    ${new_row_one}
    点击    //*[contains(text(),'采购信息')]/following::span[contains(text(),'新增行')][1]
    Sleep    2
    点击    //app-dynamic-input-display[@placeholder='请输入商品种类']
    输入    //input[@placeholder='请输入商品种类']    ${commodity_type}
    点击    //app-dynamic-input-display[@placeholder='请输入计划采购量']
    输入    //input[@placeholder='请输入计划采购量']    ${plan_volume}
    点击    //*[@class="ath-tab-normal-title ng-star-inserted"]//*[contains(text(),"采购信息")]
    Sleep    2
#    ${new_row_two}    按顺序获取元素    //span[contains(text(),"新增行")]    1
#    Sleep    1
#    点击     ${new_row_two}
#    Sleep    2
    点击    //*[contains(text(),'采购信息')]/following::span[contains(text(),'新增行')][2]
    点击    //app-dynamic-input-display[@placeholder='请输入采购单价']
    输入    //input[@placeholder='请输入采购单价']    ${purchase_price}
    Sleep    2
    点击    //span[@class="placeholder ng-star-inserted" and contains(text(),'请输入入库时间')]
    输入    //input[@placeholder='请输入入库时间']    ${date}


输入-双档(整单操作)
    [Arguments]    ${employeeId}    ${Employee_name}    ${positionId}    ${positionName}    ${salary}
    Sleep    2
    输入    //input[@placeholder='请输入员工ID']    ${employeeId}
    输入    //input[@placeholder='请输入员工姓名']    ${Employee_name}
    点击    //span[contains(text(),"新增行")]
    Sleep    3
    点击    //app-dynamic-input-display[@placeholder='请输入职位ID']
    输入    //input[@placeholder='请输入职位ID']    ${positionId}
    点击    //app-dynamic-input-display[@placeholder='请输入职位名称']
    输入    //input[@placeholder='请输入职位名称']    ${positionName}
    点击    //app-dynamic-input-display[@placeholder='请输入薪水']
    输入    //input[@placeholder='请输入薪水']    ${salary}
    点击-新建


导入导出中心展示断言
    Sleep    3
    当前页面可见字符    导入记录
    当前页面可见字符    导出记录
    当前页面可见字符    2

导入记录
    点击    //*[contains(text(),"导入记录")]
    Sleep    2
    当前页面可见字符    导入记录名称
    当前页面可见字符    执行状态
    点击    //div[@class="ant-tabs-tab ant-tabs-tab-active ng-star-inserted"]/div/div/button/i

导出记录
    点击    //span[contains(text(),"导出记录")]
    Sleep    2
    当前页面可见字符    导出记录名称
    当前页面可见字符    执行状态
    点击     //div[@class="advanced-condition"]/div[1]/ath-select/ath-select-arrow/i
    点击    //div[@class="cdk-overlay-connected-position-bounding-box"]/div//span[contains(text(),"成功")]
    点击     //div[@class="advanced-condition"]/div[2]/ath-select/ath-select-arrow/i
    点击    //div[@class="cdk-overlay-connected-position-bounding-box"]/div//span[contains(text(),"业务数据录入")]
    点击    //span[contains(text(),"搜索")]
    当前页面可见字符    成功
    #打开所属模块
    点击    //div[@class="ag-center-cols-container"]/div[1]/div[@col-id="type_2"]//*[contains(text(),"业务数据录入")]
    当前页面可见字符    回收站
    点击    //*[contains(text(),"导入导出中心")]


浏览页删除全部数据
    [Arguments]    ${jobname}
    点击    //div[contains(text(),"${jobname}")]
    Sleep    1
    点击    //ath-patch-header-renderer[@class='ng-star-inserted']//span[@class='ant-checkbox-inner']
    Sleep    2
    点击    //span[contains(text(),"删除")]
    Sleep    2
    点击    //span[contains(text(),"确定")]

浏览页直接删除全部数据
    点击    //ath-patch-header-renderer[@class='ng-star-inserted']//span[@class='ant-checkbox-inner']
    Sleep    2
    点击    //span[contains(text(),"删除")]
    Sleep    2
    点击    //span[contains(text(),"确定")]


数据导入
    [Arguments]    ${filename}
    Sleep    3
    ${ordervalue_exist}    Get Element Count    //span[@class="ant-checkbox"]//input[@type="checkbox"]
    Sleep    3
    Run Keyword If    '${ordervalue_exist}'>'1'    浏览页直接删除全部数据    ELSE    Log    nothing to do
    ${filepath}    Set Variable    ${EXECDIR}\\File\\${filename}
    点击    //span[contains(text(),"数据导入")]
    点击    //div[contains(text(),"选择或拖拽文件到这里上传")]
    Sleep    3
    UploadFile    "${filepath}"
    Sleep    1
    ${exist}    Run Keyword And Return Status    当前页面包含元素    //*[contains(text(),"文件类型不符合要求")]    2
    IF    ${exist}    
        点击    //*[contains(text(),"取消")]
    ELSE
        点击    //span[contains(text(),"确定")]
    END
    

点击查看记录
    Sleep    3
    点击    //span[contains(text(),"查看记录")]

返回业务数据录入菜单
    点击    //li[contains(text(),"业务数据录入")]


导入异常文件判断
    当前页面可见    //*[contains(text(),"文件类型不符合要求")]


导入空文件判断
    当前页面可见    //*[contains(text(),"导入文件数据为空")]

                
进入导入记录
    Sleep    3
    点击    //*[contains(text(),"导入记录")]

查看异常页面
    Sleep    2
    点击    (//*[contains(text(), "查看异常")])[1]


关闭维护页签
    Sleep    2
    点击    //div[contains(text(),'维护')]/following::button[1]
    ${exist}    Run Keyword And Return Status    当前页面包含元素     //div[contains(@class,'ant-modal-content')]   3
    Run Keyword If      ${exist}  点击    //button/span[contains(text(),'确定')]    ELSE    Log    nothing to do


生效/失效单档多栏作业数据
    [Arguments]    ${manage_status}
    Run Keyword If  '${manage_status}'=='生效'    当前页面可见    //button[@disabled='true']/span[text()='生效']
    Run Keyword If  '${manage_status}'=='失效'    当前页面可见    //div[@class='ath-tag-inner']/span[text()='生效']
    #前端筛选  manage_status  未生效
    checkbox全选  1
    当前页面可见    //button/span[text()='${manage_status}']
    点击    //button/span[text()='${manage_status}']
    当前页面可见    //div[contains(@class,'ant-modal-content')]
    点击    //button/span[contains(text(),'确定')]
    当前页面可见    //div[@class='ath-tag-inner']/span[text()='${manage_status}']


重新生效/取消失效单档多栏作业数据
   [Arguments]    ${manage_status}
   Run Keyword If  '${manage_status}'=='重新生效'
   ...    Run Keywords    当前页面可见    //button[@disabled='true']/span[text()='重新生效']
   ...    AND    checkbox全选  1
   ...    AND    当前页面可见    //button/span[text()='${manage_status}']
   ...    AND    点击    //button/span[text()='${manage_status}']
   ...    AND    当前页面可见    //div[contains(@class,'ant-modal-content')]
   ...    AND    点击    //button/span[contains(text(),'确定')]
   ...    AND    当前页面可见    //div[@class='ath-tag-inner']/span[text()='生效']
   Run Keyword If  '${manage_status}'=='取消生效'
   ...    Run Keywords    当前页面可见    //button[@disabled='true']/span[text()='取消生效']
   ...    AND    checkbox全选  1
   ...    AND    当前页面可见    //button/span[text()='${manage_status}']
   ...    AND    点击    //button/span[text()='${manage_status}']
   ...    AND    当前页面可见    //div[contains(@class,'ant-modal-content')]
   ...    AND    点击    //button/span[contains(text(),'确定')]
   ...    AND    当前页面可见    //div[@class='ath-tag-inner']/span[text()='未生效'] 

#下列为同一作业操作关键字
点击员工信息维护作业新增按钮
    点击    //span[contains(text(),"新增")]

点击员工信息维护作业详情子表新增行并在子表第一个单元格输入内容
    [Arguments]    ${job_level}
    点击    (//span[contains(text(),"新增行")])[1]
    点击    //app-dynamic-input-display[@placeholder='请输入员工的职级']
    输入    //input[@placeholder='请输入员工的职级']    ${job_level}

点击子子表新增行并校验是否正常新增行
    [Arguments]    ${dynamic_skills}
    点击    (//span[contains(text(),"新增行")])[2]
    点击    //app-dynamic-input-display[@placeholder='请输入员工掌握的技能']
    输入    //input[@placeholder='请输入员工掌握的技能']    ${dynamic_skills}