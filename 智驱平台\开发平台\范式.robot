*** Settings ***
Suite Setup       Run Keywords    环境设定
Suite Teardown    Run Keywords    关闭浏览器
Library           Collections
Resource          ../关键字/业务关键字/开发平台.robot
Resource          ../关键字/业务关键字/交付设计器.robot
Resource          ../关键字/业务关键字/Athena平台.robot

*** Test Cases ***
范式增删改查
    [Documentation]    陈金明
    登录开发平台    ${username}    ${password}    ${tenantId}
    开发平台.点击顶部菜单    解决方案中心
    搜索应用    purchase99
    进入应用配置页    purchase99
    选择浏览器窗体    -1
    点击左侧菜单    范式设定
    新增范式
    范式列表搜索    ${paradigmName}
    删除范式    ${paradigmName}
    [Teardown]    Run Keywords    关闭浏览器

范式组件清单
    [Documentation]    陈金明
    登录开发平台    ${username}    ${password}    ${tenantId}
    开发平台.点击顶部菜单    解决方案中心
    搜索应用    purchase99
    进入应用配置页    purchase99
    选择浏览器窗体    -1
    点击左侧菜单    范式设定
    范式进入组件清单    范式名称不可删除
    添加组件
    删除组件
    范式清单
    组件清单

范式设定内使用共享范式
    [Documentation]    张欣雨
    #    需求范式发版41220
    #    新增共享范式-新增机制-机制原理-机制能力-发布机制-生效机制-发起项目-检查机制能力是否生效
    ${username}    Set Variable If    '${ENV}'=='paas'    ${username}    '${ENV}'=='huawei.test'    ${username}    '${ENV}'=='huawei.prod'    ${username}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${password}    Set Variable If    '${ENV}'=='paas'    ${password}    '${ENV}'=='huawei.test'    ${password}    '${ENV}'=='huawei.prod'    ${password}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${application}    Set Variable If    '${ENV}'=='paas'    自动化使用应用    '${ENV}'=='huawei.test'    采购管理    '${ENV}'=='huawei.prod'    采购管理88CN
    ${appCode}    Set Variable If    '${ENV}'=='paas'    auto_appCN    '${ENV}'=='huawei.test'    purchase88CN    '${ENV}'=='huawei.prod'    purchase88CN
    ${tenant}    Set Variable If    '${ENV}'=='paas'    智驱中台工作台    '${ENV}'=='huawei.test'    智驱中台工作台    '${ENV}'=='huawei.prod'    智驱中台工作台
    ${tenantId}    Set Variable If    '${ENV}'=='paas'    IntelligentDriveCenterWorkbench    '${ENV}'=='huawei.test'    IntelligentDriveCenterWorkbench    '${ENV}'=='huawei.prod'    IntelligentDriveCenterWorkbench
    ${duty}    Set Variable If    '${ENV}'=='paas'    测试职能    '${ENV}'=='huawei.test'    测试职能    '${ENV}'=='huawei.prod'    测试职能
    ${deployToENV}    Set Variable If    '${ENV}'=='paas'    阿里测试区    '${ENV}'=='huawei.test'    阿里测试区    '${ENV}'=='huawei.prod'    大陆正式区
    ${token}    Set Variable If    '${ENV}'=='paas'    fb6f63fb-e9a0-4968-95ea-76e9481c29c3    '${ENV}'=='huawei.test'    fb6f63fb-e9a0-4968-95ea-76e9481c29c3    '${ENV}'=='huawei.prod'    661c4af4-2b02-4c7a-9d47-e3b9836236c1
    Set Global Variable    ${topic}    自动化测试议题
    Set Global Variable    ${token}
    Set Global Variable    ${tenantId}
    # 共享范式新建
    # 范式名称和机制名称定义
    ${time}    生成秒时间戳
    Set Global Variable    ${paradigmName}    ${time}
    Set Global Variable    ${mechanismName}    ${time}
    登录开发平台    ${username}    ${password}    ${tenant}
    开发平台.点击顶部菜单    资产中心
    点击左侧菜单    范式管理
    Sleep    5
    新增资产中心范式（不绑定应用）    ${paradigmName}
    搜索范式    ${paradigmName}
    范式发布    ${paradigmName}    ${deploy_to_env}
    开发平台.点击顶部菜单    解决方案中心
    搜索应用    ${application}
    进入应用配置页    ${appCode}
    sleep    5
    选择浏览器窗体    -1
    点击左侧菜单    范式设定
    引用资产中心的共享范式    ${paradigmName}
    范式列表搜索    ${paradigmName}
    点击左侧菜单    机制设计
    新增机制    ${mechanismName}    ${paradigmName}
    进入机制设计页    ${mechanismName}
    新增机制原理    机制原理名称    机制原理描述
    新增指派能力-特定人员    指派能力-按职能ID    指派能力描述    职能    ${duty}    #支持,人员,职能和部门成员
    应用发布    ${deployToENV}    ${tenant}
    #    以下为调试代码
    Comment    Set Global Variable    ${paradigmCode}    PD_b8d174021000086d
    Comment    Set Global Variable    ${mechanismCode}    ud_m_3863746b3bdb4214abccbf0f64ab2375
    Comment    Set Global Variable    ${mechanismName}    1736902144
    Comment    Set Global Variable    ${paradigmName}    1736902144
    #    以上为调试代码
   #购买范式（采用接口形式）定义范式购买的请求入参
    ${data}    Set Variable     {"code":"${appCode}","operationUnit":null,"showType":"app"}
    ${mechanismCode_list}    查询已购买机制列表加需购买列表    ${data}
    ${mechanismCode_list}    Evaluate    json.dumps(${mechanismCode_list})    modules=json
    ${data}    Set Variable    {"appCode":"${appCode}","paradigm":"${paradigmCode}","mechanismCodes":${mechanismCode_list}}
    Log    ${data}
    购买范式    ${data}
    # \ \ Athena平台生效机制
    登录Athena平台    ${username}    ${password}
    租户切换    ${tenant}
    Athena平台.点击顶部菜单    全部
    进入交付设计器    交付设计器
    选择浏览器窗体    -1
    机制生效    ${application}    ${mechanismName}    ${topic}
    关闭浏览器
    ## \ \ 发起项目检查机制逻辑，通过接口方式
    ${projectCode}    Set Variable If    '${ENV}'=='paas'    purchase_project_0001    '${ENV}'=='huawei.test'    purchase88CN_PU_HLnhr4A8    '${ENV}'=='huawei.prod'    purchase88CN_PU_HLnhr4A8
    ${data}    Set Variable    {"projectCode":"${projectCode}","process_EOC":{},"variables":{"classification_mode":"1","from_inquiry":"false","is_inquiry":"false"},"dispatchData":[{}]}
    发起项目    ${data}
    ${serialNumber}    Set Variable    ${response_dict["response"]["serialNumber"]}
    Sleep    20
    ${data}    Set Variable    {"locale":"zh_CN","serialNumber":"${serialNumber}"}
    获取项目详情    ${data}
    ${taskUid}    Set Variable    ${response_dict["response"]["taskInstances"][0]["data"]["taskUid"]}
    ${data}    Set Variable    {"taskUid":"${taskUid}"}
    获取任务详情    ${data}
    ${performerId}    Set Variable    ${response_dict["response"]["list"][0]["steps"][0]["performerId"]}
    #*******************为测试职能设置的人员，在鼎捷云配置
    指派能力断言    <EMAIL>    ${performerId}

    #注释：完成机制能力校验后的数据清理，删除机制和范式
    #    以下为调试代码
    Comment    Set Global Variable    ${paradigmCode}    PD_2a424fc210000288
    Comment    Set Global Variable    ${mechanismCode}    ud_m_28a8b9e7132540cdbae900fa47a23b1e
    Comment    Set Global Variable    ${mechanismName}    1736999001
    Comment    Set Global Variable    ${paradigmName}    1736999001
    #    以上为调试代码
    登录开发平台    ${username}    ${password}    ${tenant}
    开发平台.点击顶部菜单    解决方案中心
    搜索应用    ${application}
    进入应用配置页    ${appCode}
    选择浏览器窗体    -1
    点击左侧菜单    机制设计
    机制列表搜索    ${mechanismName}
    删除机制    ${mechanismName}
    跳转到首页
    开发平台.点击顶部菜单    资产中心
    点击左侧菜单    范式管理
    Sleep    5
    搜索范式    ${paradigmName}
    共享范式解绑    ${paradigmName}    ${application}
    删除共享范式    ${paradigmName}
    范式发布    ${paradigmName}    ${deploy_to_env}


范式设定内使用复制范式
    #    需求范式发版41220
    #    新增共享范式-新增机制-机制原理-机制能力-发布机制-生效机制-发起项目-检查机制能力是否生效
    ${username}    Set Variable If    '${ENV}'=='paas'    ${username}    '${ENV}'=='huawei.test'    ${username}    '${ENV}'=='huawei.prod'    ${username}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${password}    Set Variable If    '${ENV}'=='paas'    ${password}    '${ENV}'=='huawei.test'    ${password}    '${ENV}'=='huawei.prod'    ${password}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${application}    Set Variable If    '${ENV}'=='paas'    自动化使用应用    '${ENV}'=='huawei.test'    采购管理    '${ENV}'=='huawei.prod'    采购管理88CN
    ${appCode}    Set Variable If    '${ENV}'=='paas'    auto_appCN    '${ENV}'=='huawei.test'    purchase88CN    '${ENV}'=='huawei.prod'    purchase88CN
    ${tenant}    Set Variable If    '${ENV}'=='paas'    智驱中台工作台    '${ENV}'=='huawei.test'    智驱中台工作台    '${ENV}'=='huawei.prod'    智驱中台工作台
    ${tenantId}    Set Variable If    '${ENV}'=='paas'    IntelligentDriveCenterWorkbench    '${ENV}'=='huawei.test'    IntelligentDriveCenterWorkbench    '${ENV}'=='huawei.prod'    IntelligentDriveCenterWorkbench
    ${duty}    Set Variable If    '${ENV}'=='paas'    测试职能    '${ENV}'=='huawei.test'    测试职能    '${ENV}'=='huawei.prod'    测试职能
    ${deployToENV}    Set Variable If    '${ENV}'=='paas'    阿里测试区    '${ENV}'=='huawei.test'    阿里测试区    '${ENV}'=='huawei.prod'    大陆正式区
    ${token}    Set Variable If    '${ENV}'=='paas'    fb6f63fb-e9a0-4968-95ea-76e9481c29c3    '${ENV}'=='huawei.test'    fb6f63fb-e9a0-4968-95ea-76e9481c29c3    '${ENV}'=='huawei.prod'    661c4af4-2b02-4c7a-9d47-e3b9836236c1
    Set Global Variable    ${topic}    自动化测试议题
    Set Global Variable    ${token}
    Set Global Variable    ${tenantId}
    # \ \ 共享范式新建
    # \ \ 范式名称和机制名称定义
    ${time}    生成秒时间戳
    Set Global Variable    ${paradigmName}    ${time}
    Set Global Variable    ${mechanismName}    ${time}
    登录开发平台    ${username}    ${password}    ${tenant}
    开发平台.点击顶部菜单    资产中心
    点击左侧菜单    范式管理
    Sleep    5
    新增资产中心范式（不绑定应用）    ${paradigmName}
    搜索范式    ${paradigmName}
    范式发布    ${paradigmName}    ${deploy_to_env}
    开发平台.点击顶部菜单    解决方案中心
    搜索应用    ${application}
    进入应用配置页    ${appCode}
    sleep    5
    选择浏览器窗体    -1
    点击左侧菜单    范式设定
    引用资产中心的复制范式（普通范式）    ${paradigmName}
    范式列表搜索    ${paradigmName}
    点击左侧菜单    机制设计
    新增机制    ${mechanismName}    ${paradigmName}
    进入机制设计页    ${mechanismName}
    新增机制原理    机制原理名称    机制原理描述
    新增指派能力-特定人员    指派能力-按职能ID    指派能力描述    职能    ${duty}    #支持,人员,职能和部门成员
    应用发布    ${deployToENV}    ${tenant}

    #购买范式（采用接口形式）定义范式购买的请求入参
    ${data}    Set Variable     {"code":"${appCode}","operationUnit":null,"showType":"app"}
    ${mechanismCode_list}    查询已购买机制列表加需购买列表    ${data}
    ${mechanismCode_list}    Evaluate    json.dumps(${mechanismCode_list})    modules=json
    ${data}    Set Variable    {"appCode":"${appCode}","paradigm":"${paradigmCode}","mechanismCodes":${mechanismCode_list}}
    Log    ${data}
    购买范式    ${data}
    # \ \ Athena平台生效机制
    登录Athena平台    ${username}    ${password}
    租户切换    ${tenant}
    Athena平台.点击顶部菜单    全部
    进入交付设计器    交付设计器
    选择浏览器窗体    -1
    机制生效    ${application}    ${mechanismName}    ${topic}
    关闭浏览器
    ## \ \ 发起项目检查机制逻辑，通过接口方式
    ${projectCode}    Set Variable If    '${ENV}'=='paas'    purchase_project_0001    '${ENV}'=='huawei.test'    purchase88CN_PU_HLnhr4A8    '${ENV}'=='huawei.prod'    purchase88CN_PU_HLnhr4A8
    ${data}    Set Variable    {"projectCode":"${projectCode}","process_EOC":{},"variables":{"classification_mode":"1","from_inquiry":"false","is_inquiry":"false"},"dispatchData":[{}]}
    发起项目    ${data}
    ${serialNumber}    Set Variable    ${response_dict["response"]["serialNumber"]}
    Sleep    20
    ${data}    Set Variable    {"locale":"zh_CN","serialNumber":"${serialNumber}"}
    获取项目详情    ${data}
    ${taskUid}    Set Variable    ${response_dict["response"]["taskInstances"][0]["data"]["taskUid"]}
    ${data}    Set Variable    {"taskUid":"${taskUid}"}
    获取任务详情    ${data}
    ${performerId}    Set Variable    ${response_dict["response"]["list"][0]["steps"][0]["performerId"]}
    #*******************为测试职能设置的人员，在鼎捷云配置
    指派能力断言    <EMAIL>    ${performerId}
    #注释：完成机制能力校验后的数据清理，删除机制和范式
    登录开发平台    ${username}    ${password}    ${tenant}
    开发平台.点击顶部菜单    解决方案中心
    搜索应用    ${application}
    进入应用配置页    ${appCode}
    选择浏览器窗体    -1
    点击左侧菜单    机制设计
    机制列表搜索    ${mechanismName}
    删除机制    ${mechanismName}
    跳转到首页
    开发平台.点击顶部菜单    资产中心
    点击左侧菜单    范式管理
    Sleep    5
    搜索范式    ${paradigmName}
    删除共享范式    ${paradigmName}
