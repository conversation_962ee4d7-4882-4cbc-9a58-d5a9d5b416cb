*** Settings ***
Resource          ../系统关键字/web.robot
Resource          首页.robot
Resource          智能入口.robot
Resource          待办.robot

*** Keywords ***
任务/项目名称搜索
    [Arguments]    ${name}    ${state}=null
    #代办出来慢，刷一下
#    刷新页面
    视图模式设定    卡片
    清空筛选
    #等待页面查询完成的loading消失
    当前页面不可见元素    //span[@class='ant-spin-dot ant-spin-dot-spin ng-star-inserted']
    元素存在则点击    //div[contains(@class,'todo-search-container')]//span[contains(text(),'搜索')]    5
    输入    //input[@placeholder='可用空格分隔关键词']    ${name}
    待办搜索轮询    ${name}
    #    当前页面可见    //div[@class='todo-card-item-container ng-star-inserted']//*[contains(text(),'${name}')]
    状态断言    ${state}

任务/项目名称搜索(繁体)
    [Arguments]    ${name}    ${state}=null
    #代办出来慢，刷一下
#    刷新页面
#    视图模式设定    卡片
    清空筛选(繁体)
    #等待页面查询完成的loading消失
    当前页面不可见元素    //span[@class='ant-spin-dot ant-spin-dot-spin ng-star-inserted']
    元素存在则点击    //div[contains(@class,'todo-search-container')]//span[contains(text(),'搜尋')]    20
    输入    //input[@placeholder='可用空格分隔關鍵詞']    ${name}
    待办搜索轮询    ${name}
    #    当前页面可见    //div[@class='todo-card-item-container ng-star-inserted']//*[contains(text(),'${name}')]
#    状态断言    ${state}

状态断言
    [Arguments]    ${state}    ${timeout}=${timeout}
    Run Keyword If    '${state}' != 'null'    当前页面可见    //div[@class='todo-card-item-top-container']//*[contains(text(),'${state}')]    ${timeout}

待办搜索轮询
    [Arguments]    ${name}
    点击    //span[@type='addon']
    FOR    ${index}    IN RANGE    3
        ${element_exists}    Run Keyword And Return Status    当前页面可见    //div[@class='todo-card-item-container ng-star-inserted']//*[contains(text(),'${name}')]    60
        Run Keyword If    ${element_exists}    Exit For Loop
        ...    ELSE    刷新页面
    END

租户切换
    [Arguments]    ${tenant}
#    ${text}    获取元素字符    //i[contains(@class,'iconzuhu1')]/following-sibling::span
#    #如果当前租户已是目标租户则不切换
#    IF    '${tanent}'!='${text}'
#        Run Keywords    鼠标悬停    //i[contains(@class,'iconzuhu1')]
#        ...    AND    点击    //span[text()='${tanent}' and @class='option-name']
#        ...    AND    sleep    10
#    END


    ${status}    Run Keyword And Return Status    当前页面可见字符    ${tenant}    10
    IF    '${status}' == 'False'
        Run Keywords    鼠标悬停    //i[@class='icon font-entrance icongongsi']
        ...    AND    点击    //span[text()='${tenant}' and @class='option-name']
        ...    AND    sleep    5
    END

登录Athena平台
    [Arguments]    ${username}    ${password}    ${alias}=None    ${tenant}=None    ${type}=极简门户    ${language}=简体
    打开网页    ${ATHENA_ENV}    ${browser}    ${alias}
    IF    '${ENV}'=='microsoft.prod'
        当元素不可见则刷新页面    1    //input[@name="userId"]    10
    END
    #如果登录页白屏就尝试刷新2次
    当元素不可见则刷新页面    2    //input[@name="userId"]
    输入    //input[@name="userId"]    ${username}
    输入    //input[@name="password"]    ${password}
    点击    //div[@class="action"]/button/span
    #判断首页是否完全加载，判断待办是否显示
    当元素不可见则刷新页面    2    //i[@class='icon font-entrance icongongsi']
    Run Keyword If    '${tenant}'!='None'    租户切换    ${tenant}
    元素存在则点击    //span[contains(text(),'我知道了')]    1
    当元素不可见则刷新页面    2    //i[@class='icon font-entrance icongongsi']
    语言设定    ${language}
    门户主题    ${type}
    当元素不可见则刷新页面    2    //i[@class='icon font-entrance icongongsi']
    #如果娜娜打开则关闭
    关闭娜娜窗口

退出当前登录,切换账号登录
    [Arguments]    ${username}    ${password}    ${type}=极简门户    ${language}=简体
    Sleep    3
    点击    //*[@class="icon font-entrance iconrenyuan1"]
    Sleep    3
    点击    //span[contains(text(),"登出")]
    Sleep    3
    点击    //span[contains(text(),"确认")]
    #    清空用户名输入框
    Sleep    3
    Clear Element Text    //input[@name="userId"]
    Sleep    3
    输入    //input[@name="userId"]    ${username}
    #    清空密码输入框
    Clear Element Text    //input[@name="password"]
    Sleep    3
    输入    //input[@name="password"]    ${password}
    点击    //div[@class="action"]/button/span
    Sleep    15
    语言设定    ${language}
    门户主题    ${type}



点击顶部菜单
    [Arguments]    ${menu}
    点击    //*[text()='${menu}']
    Sleep    2
#    当前页面不存在元素    //span[@class='ant-spin-dot ant-spin-dot-spin ng-star-inserted']

点击右侧菜单
    [Arguments]    ${menu}
    点击    //*[contains(text(),'${menu}')]
    Sleep    5
    Run Keyword If    '${menu}'=='IT运维模组'    iframe选择

打开IT运维模组
#    点击    //i[contains(@class,'iconyonghu')]
    点击    //i[contains(@class,'icon font-entrance iconrenyuan1')]
    点击    //*[contains(text(),'管理后台')]
    选择浏览器窗体    -1
    点击    //div[contains(text(),'系统管理')]
    点击    //span[contains(text(),'IT运维模组')]
    当前页面包含元素    //iframe
    iframe选择

进入基线标准作业
    [Arguments]    ${name}
    点击    //span[contains(text(),'${name}')]

企业员工登录Athena平台
    [Arguments]    ${tenantId}    ${username}    ${password}    ${type}=极简门户
    打开网页    ${ATHENA_ENV}    ${browser}
    当前页面若文本不存在自动刷新页面    3    请输入用户账号
    点击    //span[@class='title']
    Sleep    3
    ${status}=    Run Keyword And Return Status    点击    //*[contains(text(),'企业用户帐号')]
    Log    执行状态: ${status}
    Run Keyword If    '${status}'=='False'    Run Keywords    Reload Page
    ...    AND    点击    //span[@class='title']
    ...    AND    点击    //span[@class='title']
    ...    AND    点击    //*[contains(text(),'企业用户帐号')]
    输入    //input[@name='tenantId']    ${tenantId}
    输入    //input[@name="userId"]    ${username}
    输入    //input[@name="password"]    ${password}
    点击    //div[@class="action"]/button/span
#    当前页面若文本不存在自动刷新页面    3    数智视图
    #进入首页如果白屏则刷新页面
    当元素不可见则刷新页面    2    //div[@class='common-text zh']    
    元素存在则点击    //span[contains(text(),'我知道了')]    3
    门户主题    ${type}
    关闭娜娜窗口

待办任务/项目名称搜索
    [Arguments]    ${tabName}    ${search_name}
    点击    //div[contains(text(),'待办')]
    sleep    3
    点击    //div[contains(text(),'${tabName}')]
    sleep    3
    任务/项目名称搜索    ${search_name}

跳转到待办页
    [Arguments]    ${url}
    跳转网页    ${url}
    FOR    ${index}    IN RANGE    5
        ${element_exists}    Run Keyword And Return Status    当前页面可见字符    首页    30
        Run Keyword If    ${element_exists}    Exit For Loop
        ...    ELSE    刷新页面
    END
    当前页面不可见元素    //span[@class='ant-spin-dot ant-spin-dot-spin ng-star-inserted']

页面巡检
    FOR    ${url}    IN    ${url_collection}
        跳转网页    ${url}

任务/项目名称搜索（暂无数据）
    [Arguments]    ${name}
    #此处后面看怎么优化
    视图模式设定    卡片
    清空筛选
    #点击    //span[contains(text(),'搜索')]
    ${element_exists}    判断元素是否可见    //span[contains(text(),'搜索')]    3
    Run Keyword If    ${element_exists}    点击    //span[contains(text(),'搜索')]
    ...    ELSE    Log    Element does not exist.
    输入    //input[@placeholder='可用空格分隔关键词']    ${name}
    待办搜索轮询    ${name}
    #Sleep    3
    #当前页面可见    //span[contains(text(),'${name}')]
    #当前页面可见    //span[@class='card-item-name-title'][contains(text(),'${name}')]
    当前页面不可见元素    //div[@class='todo-card-item-container ng-star-inserted']//*[contains(text(),'${name}')]

打开应用的作业
    [Arguments]    ${appCode}    ${name}
    点击    //div[@id='${appCode}']//*[text()='${name}']

关注取消关注断言
#
    ${visual}    判断元素是否可见    //div[@class='important ng-star-inserted']    30
    #失去焦点，测试tip
    鼠标悬停    //li[@class='ath-tab-menus-li ng-star-inserted']   
#    Run Keyword If    ${visual}    Run Keywords    鼠标悬停    //div[@class='important ng-star-inserted']
#    ...    AND    当前页面可见字符    关注    5    AND    点击    //div[@class='important ng-star-inserted']    AND    当前页面可见字符    已设置特别关注    5
#    Run Keyword If    '${visual}'=='False'    Run Keywords    鼠标悬停    //div[@class='important ng-star-inserted active']
#    ...    AND    当前页面可见字符    已关注    5    AND    点击    //div[@class='important ng-star-inserted active']    AND    当前页面可见字符    已取消特别关注    5
    #by cjm
    IF    ${visual}
        鼠标悬停    //div[@class='important ng-star-inserted']
        当前页面可见字符    关注    5
        Sleep    2
        点击    //div[@class='important ng-star-inserted']    
        当前页面可见字符    已设置特别关注    5
        Sleep    3
    ELSE
        #此处被耽误了几个小时
        鼠标悬停    //div[contains(@class, 'important') and contains(@class, 'active')]
        当前页面可见字符    已关注    5
        Sleep    2
        点击    //div[contains(@class, 'important') and contains(@class, 'active')]
        当前页面可见字符    已取消特别关注    5
        Sleep    3
    END




#
#    鼠标悬停    //i[@class='font-class importance-button']
#    当前页面可见字符    关注
#    点击    //i[@class='font-class importance-button']
#    当前页面可见    //*[contains(text(),'已设置特别关注')]    10
#    Sleep    5
#    点击    //i[@class='font-class importance-button']
#    当前页面可见    //*[contains(text(),'已取消特别关注')]    10

关闭页签
    [Arguments]    ${tabName}
    元素存在则点击    //li[contains(text(),'${tabName}')]/child::i[@nztype='close']
    当前页面不可见元素    //li[contains(text(),'${tabName}')]/child::i[@nztype='close']

切换项目/任务详情列表
    [Arguments]    ${projectTaskname}
    元素存在则点击    //div[@aria-selected='false']/div/div[contains(text(),'${projectTaskname}')]
    当前页面可见    //div[@aria-selected='true']/div/div[contains(text(),'${projectTaskname}')]

打开消息页
    js点击    //i[@class='icon font-entrance iconxiaoxi2']

#有title的消息需必须要传入title，比如传入新增，知会
#无title直接传入消息内容
消息检查
    [Arguments]    ${content}    ${title}=null
    IF    '${title}'!='null'
        当前页面可见    //span[@class='message-content ng-star-inserted']//span[contains(text(),'${content}')]/preceding::ath-tag[1]//span[contains(text(),'${title}')]
    ELSE
        当前页面可见    xpath=(//div[@class='remind-item-content']//span[contains(text(),'${content}')])[1]
    END

通用搜索框搜索并勾选
    [Arguments]    ${content}
    输入    //div[@class='ath-input-content-box ng-star-inserted']//input[@type='text']    ${content}
    当前页面不可见字符    数据加载中
    点击    //span[@class='ng-star-inserted'][contains(text(),'搜索')]
    sleep    3
    点击    //span[contains(text(),'${content}')]/preceding::input[@type='radio']/parent::span
    #Sleep    1
    点击    //button[@class='ath-btn ant-btn ant-btn-primary ng-star-inserted active']//span[@class='ng-star-inserted'][contains(text(),'提交')]

关闭指定标签页
    [Arguments]    ${label}
    点击    //li[contains(text(),'${label}')]/i
    Sleep    3
    当前页面不可见字符    ${label}

提交任务卡
    Wait Until Element Is Enabled    //span[contains(text(),'提交')]
    Sleep    5
    点击    	//span[contains(text(),'提交')]
    点击    //span[contains(text(),'确定')]
    当前页面可见    //*[text()='-提交-']
    当前页面不可见字符    请稍等
    
模版切换
    [Arguments]    ${text}
    鼠标悬停    //i[@class='icon font-entrance iconmenhu']
    点击    //li[contains(text(),'${text}')]