*** Settings ***
Suite Setup       Run Keywords    环境设定
Suite Teardown    Run Keywords    关闭浏览器
Test Teardown     Run Keywords    错误处理    AND    关闭浏览器
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/业务关键字/Athena平台控件.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/智能入口.robot
Resource          ../关键字/业务关键字/员工状态.robot
Resource          ../关键字/系统关键字/公共方法.robot

*** Variables ***
${appName}        【质量组】【UI】侦测排程发起项目

*** Test Cases ***
项目卡/任务卡-员工离职自动交接
    ${user}    Set Variable If    '${ENV}'=='paas'    manager001    '${ENV}'=='huawei.test'    manager001    '${ENV}'=='huawei.prod'    manager001    '${ENV}'=='microsoft.prod'    manager001
    ${passwd}    Set Variable If    '${ENV}'=='paas'    Digiwin008    '${ENV}'=='huawei.test'    Digiwin008    '${ENV}'=='huawei.prod'    Digiwin008    '${ENV}'=='microsoft.prod'    Digiwin008
    ${tenantId}    Set Variable If    '${ENV}'=='paas'    AthenaAutoTest    '${ENV}'=='huawei.test'    AthenaAutoTest    '${ENV}'=='huawei.prod'    AthenaAutoProdHw    '${ENV}'=='microsoft.prod'    AthenaAutoProdWr
    ${tenantName}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    ${athena_title}    Set Variable If    '${ENV}'=='paas'    鼎捷雅典娜    '${ENV}'=='huawei.test'    鼎捷雅典娜    '${ENV}'=='huawei.prod'    鼎捷雅典娜    '${ENV}'=='microsoft.prod'    鼎新METIS
    ${projectName}    Set Variable    设置离职交接人侦测
    企业员工登录Athena平台    ${tenantId}    ${user}    ${passwd}
    ${start_time}    应用发起侦测并返回发起时间    ${tenantName}    ${appName}    ${projectName}
    选择窗体    ${athena_title}
    打开消息页
#    ${消息中心定位元素列表}    按环境获取元素定位    ${ENV}    xpath=//*[contains(text(),'新任务 - 表单提交(1)')][1]/../../div[2]    xpath=//span[contains(text(),'【质量组】【UI】设置离职交接场景')][1]/../../div[2]/span[2]    xpath=//span[contains(text(),'【质量组】【UI】设置离职交接场景')][1]/../../div[2]/span[2]
#    消息检查
#    ${cardNameKey}    消息中心轮询指定任务卡并提交    10    ${消息中心定位元素列表}    ${start_time}1
    消息中心检查消息并进入任务卡详情页提交任务卡    离职交接场景；场景名称：

项目卡/任务卡-执行人收到任务卡并提交
    ${user}    Set Variable If    '${ENV}'=='paas'    manager001    '${ENV}'=='huawei.test'    manager001    '${ENV}'=='huawei.prod'    manager001    '${ENV}'=='microsoft.prod'    manager001
    ${passwd}    Set Variable If    '${ENV}'=='paas'    Digiwin008    '${ENV}'=='huawei.test'    Digiwin008    '${ENV}'=='huawei.prod'    Digiwin008    '${ENV}'=='microsoft.prod'    Digiwin008
    ${tenantId}    Set Variable If    '${ENV}'=='paas'    AthenaAutoTest    '${ENV}'=='huawei.test'    AthenaAutoTest    '${ENV}'=='huawei.prod'    AthenaAutoProdHw    '${ENV}'=='microsoft.prod'    AthenaAutoProdWr
    ${tenantName}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    ${athena_title}    Set Variable If    '${ENV}'=='paas'    鼎捷雅典娜    '${ENV}'=='huawei.test'    鼎捷雅典娜    '${ENV}'=='huawei.prod'    鼎捷雅典娜    '${ENV}'=='microsoft.prod'    鼎新METIS
    ${projectName}    Set Variable If    '${ENV}'=='paas'    设置代理人侦测    '${ENV}'=='huawei.test'    设置代理人侦测    '${ENV}'=='huawei.prod'    设置代理人侦测    '${ENV}'=='microsoft.prod'    设置代理人侦测
    企业员工登录Athena平台    ${tenantId}    ${user}    ${passwd}
    ${start_time}    应用发起侦测并返回发起时间    ${tenantName}    ${appName}    ${projectName}
    选择窗体    ${athena_title}
    打开消息页
    ${消息中心定位元素列表}    按环境获取元素定位    ${ENV}    xpath=c    xpath=//span[contains(text(),'【质量组】【UI】设置代理人场景')][1]/../../div[2]/span[2]    xpath=//span[contains(text(),'【质量组】【UI】设置代理人场景')][1]/../../div[2]/span[2]
    ${cardNameKey}    消息中心轮询指定任务卡标识    10    ${消息中心定位元素列表}    ${start_time}
    任务卡/项目卡列表标签检查    我的项目    ${cardNameKey}    被代理人：
    任务卡/项目卡列表标签检查    我的任务    ${cardNameKey}    被代理人：
    任务卡列表打开指定标识任务卡并检查详情标签    ${cardNameKey}    true    xpath=//*[@*[local-name()='href' and .='#icondai']]
    关闭浏览器
    ${user}    Set Variable If    '${ENV}'=='paas'    proxyUser001    '${ENV}'=='huawei.test'    proxyUser001    '${ENV}'=='huawei.prod'    proxyUser001    '${ENV}'=='microsoft.prod'    proxyUser001
    ${passwd}    Set Variable If    '${ENV}'=='paas'    Digiwin008    '${ENV}'=='huawei.test'    Digiwin008    '${ENV}'=='huawei.prod'    Digiwin008    '${ENV}'=='microsoft.prod'    Digiwin008
    ${tenantId}    Set Variable If    '${ENV}'=='paas'    AthenaAutoTest    '${ENV}'=='huawei.test'    AthenaAutoTest    '${ENV}'=='huawei.prod'    AthenaAutoProdHw    '${ENV}'=='microsoft.prod'    AthenaAutoProdWr
    ${athena_title}    Set Variable If    '${ENV}'=='paas'    鼎捷雅典娜    '${ENV}'=='huawei.test'    鼎捷雅典娜    '${ENV}'=='huawei.prod'    鼎捷雅典娜    '${ENV}'=='microsoft.prod'    鼎新METIS
    企业员工登录Athena平台    ${tenantId}    ${user}    ${passwd}
    首页按待办类型搜索指定任务卡    我的任务    ${cardNameKey}
    当前页面不可见元素    xpath=/span[contains(text(),'${cardNameKey}')]    10
    打开消息页
    当前页面可见字符    ${cardNameKey}

项目卡/任务卡-代理人收到任务卡并提交
    ${user}    Set Variable If    '${ENV}'=='paas'    proxyUser001    '${ENV}'=='huawei.test'    proxyUser001    '${ENV}'=='huawei.prod'    proxyUser001    '${ENV}'=='microsoft.prod'    proxyUser001
    ${passwd}    Set Variable If    '${ENV}'=='paas'    Digiwin008    '${ENV}'=='huawei.test'    Digiwin008    '${ENV}'=='huawei.prod'    Digiwin008    '${ENV}'=='microsoft.prod'    Digiwin008
    ${tenantId}    Set Variable If    '${ENV}'=='paas'    AthenaAutoTest    '${ENV}'=='huawei.test'    AthenaAutoTest    '${ENV}'=='huawei.prod'    AthenaAutoProdHw    '${ENV}'=='microsoft.prod'    AthenaAutoProdWr
    ${athena_title}    Set Variable If    '${ENV}'=='paas'    鼎捷雅典娜    '${ENV}'=='huawei.test'    鼎捷雅典娜    '${ENV}'=='huawei.prod'    鼎捷雅典娜    '${ENV}'=='microsoft.prod'    鼎新METIS
    ${tenantName}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    ${athena_title}    Set Variable If    '${ENV}'=='paas'    鼎捷雅典娜    '${ENV}'=='huawei.test'    鼎捷雅典娜    '${ENV}'=='huawei.prod'    鼎捷雅典娜    '${ENV}'=='microsoft.prod'    鼎新METIS
    ${projectName}    Set Variable If    '${ENV}'=='paas'    设置代理人侦测    '${ENV}'=='huawei.test'    设置代理人侦测    '${ENV}'=='huawei.prod'    设置代理人侦测    '${ENV}'=='microsoft.prod'    设置代理人侦测
    企业员工登录Athena平台    ${tenantId}    ${user}    ${passwd}
    ${start_time}    应用发起侦测并返回发起时间    ${tenantName}    ${appName}    ${projectName}
    选择窗体    ${athena_title}
    进入消息中心
    ${消息中心定位元素列表}    按环境获取元素定位    ${ENV}    xpath=//*[contains(text(),'新任务 - 表单提交(2)')][1]/../../../remind-content/div[2]    xpath=//span[contains(text(),'【质量组】【UI】设置代理人场景')][1]/../../div[2]/span[2]    xpath=//span[contains(text(),'【质量组】【UI】设置代理人场景')][1]/../../div[2]/span[2]
    ${cardNameKey}    消息中心轮询指定任务卡并提交    10    ${消息中心定位元素列表}    ${start_time}
    当前页面不可见元素    xpath=//*[@*[local-name()='href' and .='#icondai']]
    关闭浏览器
    ${user}    Set Variable If    '${ENV}'=='paas'    manager001    '${ENV}'=='huawei.test'    manager001    '${ENV}'=='huawei.prod'    manager001    '${ENV}'=='microsoft.prod'    manager001
    ${passwd}    Set Variable If    '${ENV}'=='paas'    Digiwin008    '${ENV}'=='huawei.test'    Digiwin008    '${ENV}'=='huawei.prod'    Digiwin008    '${ENV}'=='microsoft.prod'    Digiwin008
    ${tenantId}    Set Variable If    '${ENV}'=='paas'    AthenaAutoTest    '${ENV}'=='huawei.test'    AthenaAutoTest    '${ENV}'=='huawei.prod'    AthenaAutoProdHw    '${ENV}'=='microsoft.prod'    AthenaAutoProdWr
    企业员工登录Athena平台    ${tenantId}    ${user}    ${passwd}
    首页按待办类型搜索指定任务卡    我的任务    ${cardNameKey}
    当前页面不可见元素    xpath=/span[contains(text(),'${cardNameKey}')]    10
    进入消息中心
    当前页面可见字符    ${cardNameKey}

项目卡/任务卡-团队任务直属主管收到任务卡&收藏并提交
    ${user}    Set Variable If    '${ENV}'=='paas'    teamUser001    '${ENV}'=='huawei.test'    teamUser001    '${ENV}'=='huawei.prod'    teamUser001    '${ENV}'=='microsoft.prod'    teamUser001
    ${passwd}    Set Variable If    '${ENV}'=='paas'    Digiwin008    '${ENV}'=='huawei.test'    Digiwin008    '${ENV}'=='huawei.prod'    Digiwin008    '${ENV}'=='microsoft.prod'    Digiwin008
    ${tenantId}    Set Variable If    '${ENV}'=='paas'    AthenaAutoTest    '${ENV}'=='huawei.test'    AthenaAutoTest    '${ENV}'=='huawei.prod'    AthenaAutoProdHw    '${ENV}'=='microsoft.prod'    AthenaAutoProdWr
    ${athena_title}    Set Variable If    '${ENV}'=='paas'    鼎捷雅典娜    '${ENV}'=='huawei.test'    鼎捷雅典娜    '${ENV}'=='huawei.prod'    鼎捷雅典娜    '${ENV}'=='microsoft.prod'    鼎新METIS
    ${tenantName}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    ${athena_title}    Set Variable If    '${ENV}'=='paas'    鼎捷雅典娜    '${ENV}'=='huawei.test'    鼎捷雅典娜    '${ENV}'=='huawei.prod'    鼎捷雅典娜    '${ENV}'=='microsoft.prod'    鼎新METIS
    ${projectName}    Set Variable If    '${ENV}'=='paas'    设置直属主管侦测    '${ENV}'=='huawei.test'    设置直属主管侦测    '${ENV}'=='huawei.prod'    设置直属主管侦测    '${ENV}'=='microsoft.prod'    设置直属主管侦测
    企业员工登录Athena平台    ${tenantId}    ${user}    ${passwd}
    ${start_time}    应用发起侦测并返回发起时间    ${tenantName}    ${appName}    ${projectName}
    选择窗体    ${athena_title}
    进入消息中心
    ${消息中心定位元素列表}    按环境获取元素定位    ${ENV}    xpath=//*[contains(text(),'新任务 - 表单提交(3)')][1]/../../div[2]    xpath=//span[contains(text(),'【质量组】【UI】设置主管场景')][1]/../../div[2]/span[2]    xpath=//span[contains(text(),'【质量组】【UI】设置主管场景')][1]/../../div[2]/span[2]
    ${cardNameKey}    消息中心轮询指定任务卡进入详情    10    ${消息中心定位元素列表}    ${start_time}
    关闭浏览器
    ${user}    Set Variable If    '${ENV}'=='paas'    manager001    '${ENV}'=='huawei.test'    manager001    '${ENV}'=='huawei.prod'    manager001    '${ENV}'=='microsoft.prod'    manager001
    ${passwd}    Set Variable If    '${ENV}'=='paas'    Digiwin008    '${ENV}'=='huawei.test'    Digiwin008    '${ENV}'=='huawei.prod'    Digiwin008    '${ENV}'=='microsoft.prod'    Digiwin008
    ${tenantId}    Set Variable If    '${ENV}'=='paas'    AthenaAutoTest    '${ENV}'=='huawei.test'    AthenaAutoTest    '${ENV}'=='huawei.prod'    AthenaAutoProdHw    '${ENV}'=='microsoft.prod'    AthenaAutoProdWr
    企业员工登录Athena平台    ${tenantId}    ${user}    ${passwd}
    首页按待办类型搜索指定任务卡    团队任务    ${cardNameKey}
    点击    //span[contains(text(),'${cardNameKey}')]
    sleep    3
    点击    //span[@class='ng-star-inserted'][contains(text(),'提交')]
    sleep    6
    点击    //span[@class='ng-star-inserted'][contains(text(),'确定')]
    sleep    6
    当前页面可见字符    -提交-
    sleep    3
    js点击    //*[@id='task-detail-body']/div/div
    sleep    3
    js点击    //span[contains(text(),'收藏')]
    sleep    3
    输入    xpath=//div/input[@type='text']    ${cardNameKey}(收藏卡)
    sleep    3
    点击    //span[contains(text(),'确认')]
    sleep    6
    点击    //li[contains(text(),'首页')]
    sleep    6
    点击    //div/p[contains(text(),'收藏')]
    sleep    3
    点击    //div[@class='types']//div[contains(text(),'任务')]
    sleep    3
    输入    xpath=//input[@placeholder='请输入收藏名称或来源单号']    ${cardNameKey}
    sleep    3
    当前页面可见    xpath=//span[@class='display-name ng-star-inserted' and contains(text(), '${cardNameKey}')]
    sleep    3

项目卡/任务卡-团队任务分享人查看
    ${user}    Set Variable If    '${ENV}'=='paas'    teamUser001    '${ENV}'=='huawei.test'    teamUser001    '${ENV}'=='huawei.prod'    teamUser001    '${ENV}'=='microsoft.prod'    teamUser001
    ${passwd}    Set Variable If    '${ENV}'=='paas'    Digiwin008    '${ENV}'=='huawei.test'    Digiwin008    '${ENV}'=='huawei.prod'    Digiwin008    '${ENV}'=='microsoft.prod'    Digiwin008
    ${tenantId}    Set Variable If    '${ENV}'=='paas'    AthenaAutoTest    '${ENV}'=='huawei.test'    AthenaAutoTest    '${ENV}'=='huawei.prod'    AthenaAutoProdHw    '${ENV}'=='microsoft.prod'    AthenaAutoProdWr
    ${athena_title}    Set Variable If    '${ENV}'=='paas'    鼎捷雅典娜    '${ENV}'=='huawei.test'    鼎捷雅典娜    '${ENV}'=='huawei.prod'    鼎捷雅典娜    '${ENV}'=='microsoft.prod'    鼎新METIS
    ${tenantName}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    ${athena_title}    Set Variable If    '${ENV}'=='paas'    鼎捷雅典娜    '${ENV}'=='huawei.test'    鼎捷雅典娜    '${ENV}'=='huawei.prod'    鼎捷雅典娜    '${ENV}'=='microsoft.prod'    鼎新METIS
    ${projectName}    Set Variable If    '${ENV}'=='paas'    设置直属主管侦测    '${ENV}'=='huawei.test'    设置直属主管侦测    '${ENV}'=='huawei.prod'    设置直属主管侦测    '${ENV}'=='microsoft.prod'    设置直属主管侦测
    企业员工登录Athena平台    ${tenantId}    ${user}    ${passwd}
    ${start_time}    应用发起侦测并返回发起时间    ${tenantName}    ${appName}    ${projectName}
    选择窗体    ${athena_title}
    进入消息中心
    ${消息中心定位元素列表}    按环境获取元素定位    ${ENV}    xpath=//*[contains(text(),'新任务 - 表单提交(3)')][1]/../../div[2]    xpath=//span[contains(text(),'【质量组】【UI】设置主管场景')][1]/../../div[2]/span[2]    xpath=//span[contains(text(),'【质量组】【UI】设置主管场景')][1]/../../div[2]/span[2]
    ${cardNameKey}    消息中心轮询指定任务卡进入详情    10    ${消息中心定位元素列表}    ${start_time}
    关闭浏览器
    ${user}    Set Variable If    '${ENV}'=='paas'    manager001    '${ENV}'=='huawei.test'    manager001    '${ENV}'=='huawei.prod'    manager001    '${ENV}'=='microsoft.prod'    manager001
    ${passwd}    Set Variable If    '${ENV}'=='paas'    Digiwin008    '${ENV}'=='huawei.test'    Digiwin008    '${ENV}'=='huawei.prod'    Digiwin008    '${ENV}'=='microsoft.prod'    Digiwin008
    ${tenantId}    Set Variable If    '${ENV}'=='paas'    AthenaAutoTest    '${ENV}'=='huawei.test'    AthenaAutoTest    '${ENV}'=='huawei.prod'    AthenaAutoProdHw    '${ENV}'=='microsoft.prod'    AthenaAutoProdWr
    企业员工登录Athena平台    ${tenantId}    ${user}    ${passwd}
    待办任务/项目名称搜索    团队任务    ${cardNameKey}
    点击    //span[contains(text(),'${cardNameKey}')]
    sleep    3
    js点击    //*[@id='task-detail-body']/div/div
    sleep    3
    js点击    //span[contains(text(),'添加至他人Athena')]
    sleep    3
    点击    //span[contains(text(),'被分享人')]
    sleep    3
    点击    //span[contains(text(),'确定')]
    sleep    5
    关闭浏览器
    ${user}    Set Variable If    '${ENV}'=='paas'    share001    '${ENV}'=='huawei.test'    share001    '${ENV}'=='huawei.prod'    share001    '${ENV}'=='microsoft.prod'    share001
    ${passwd}    Set Variable If    '${ENV}'=='paas'    Digiwin008    '${ENV}'=='huawei.test'    Digiwin008    '${ENV}'=='huawei.prod'    Digiwin008    '${ENV}'=='microsoft.prod'    Digiwin008
    ${tenantId}    Set Variable If    '${ENV}'=='paas'    AthenaAutoTest    '${ENV}'=='huawei.test'    AthenaAutoTest    '${ENV}'=='huawei.prod'    AthenaAutoProdHw    '${ENV}'=='microsoft.prod'    AthenaAutoProdWr
    企业员工登录Athena平台    ${tenantId}    ${user}    ${passwd}
    待办任务/项目名称搜索    我的任务    ${cardNameKey}
    点击    //span[contains(text(),'${cardNameKey}')]
    sleep    3
    点击    //span[@class='ng-star-inserted'][contains(text(),'提交')]
    sleep    6
    点击    //span[@class='ng-star-inserted'][contains(text(),'确定')]
    sleep    6
    当前页面可见字符    -提交-
    sleep    3
