*** Settings ***
Suite Setup       Run Keywords    环境设定
Suite Teardown    Run Keywords    关闭浏览器
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/业务关键字/开发平台.robot
Resource          ../关键字/业务关键字/公共方法.robot
Resource          ../配置/域名.robot
Resource          ../配置/全局参数.robot
Resource          ../元素/开发平台元素.robot
Library           Collections
Library           OperatingSystem
#Library           ../../RFLib/Base.py

*** Variables ***

*** Test Cases ***
#分支切换-开发分支创建备份分支
#    登录开发平台    ${username}    ${password}    ${tenantId}
#    点击顶部菜单    解决方案中心
#    搜索应用    purchase99
#    进入应用配置页    purchase99
#    选择浏览器窗体    -1
#    点击左侧菜单    机制设计
#    新增机制
#    #${唯一标识}    生成秒时间戳    #生成分支的名称
#    开发分支创建备份分支    ${唯一标识}    开发分支创建备份分支描述${唯一标识}
#    切换分支    备份分支
#    切换分支后验证    ${mechanismName}
#    切换分支    开发分支
#    点击左侧菜单    机制设计
#    删除机制    ${mechanismName}
#    [Teardown]    Run Keywords    关闭浏览器

#分支切换-开发分支创建测试分支
#    登录开发平台    ${username}    ${password}    ${tenantId}
#    点击顶部菜单    解决方案中心
#    搜索应用    purchase99
#    进入应用配置页    purchase99
#    选择浏览器窗体    -1
#    点击左侧菜单    机制设计
#    新增机制
#    #${唯一标识}    生成秒时间戳    #生成分支的名称
#    开发分支创建测试分支    ${唯一标识}    开发分支创建测试分支描述${唯一标识}
#    切换分支    测试分支
#    切换分支后验证    ${mechanismName}
#    切换分支    开发分支
#    点击左侧菜单    机制设计
#    删除机制    ${mechanismName}
#    [Teardown]    Run Keywords    关闭浏览器


#分支切换-测试分支创建备份分支
#    登录开发平台    ${username}    ${password}    ${tenantId}
#    点击顶部菜单    解决方案中心
#    搜索应用    purchase99
#    进入应用配置页    purchase99
#    选择浏览器窗体    -1
#    切换分支    测试分支
#    点击左侧菜单    机制设计
#    新增机制
#    #${唯一标识}    生成秒时间戳    #生成分支的名称
#    测试分支创建备份分支    ${唯一标识}    测试分支创建备份分支描述${唯一标识}
#    切换分支    备份分支
#    切换分支后验证    ${mechanismName}
#    [Teardown]    Run Keywords    关闭浏览器

#分支切换-测试分支创建正式分支
#    登录开发平台    ${username}    ${password}    ${tenantId}
#    点击顶部菜单    解决方案中心
#    搜索应用    purchase99
#    进入应用配置页    purchase99
#    选择浏览器窗体    -1
#    切换分支    测试分支
#    点击左侧菜单    机制设计
#    新增机制
#    #${唯一标识}    生成秒时间戳    #生成分支的名称
#    测试分支创建正式分支    ${唯一标识}    正式分支描述${唯一标识}
#    切换分支    正式分支
#    切换分支后验证    ${mechanismName}
#    [Teardown]    Run Keywords    关闭浏览器

#分支切换-正式分支创建备份分支
#    登录开发平台    ${username}    ${password}    ${tenantId}
#    点击顶部菜单    解决方案中心
#    搜索应用    purchase99
#    进入应用配置页    purchase99
#    选择浏览器窗体    -1
#    切换分支    正式分支
#    点击左侧菜单    机制设计
#    新增机制
#    #${唯一标识}    生成秒时间戳    #生成分支的名称
#    正式分支创建备份分支    ${唯一标识}    正式分支创建备份分支描述${唯一标识}
#    切换分支    备份分支
#    切换分支后验证    ${mechanismName}
#    [Teardown]    Run Keywords    关闭浏览器

新增机制时新增范式
    登录开发平台    ${username}    ${password}    ${tenantId}
    点击顶部菜单    解决方案中心
    搜索应用    purchase99
    进入应用配置页    purchase99
    选择浏览器窗体    -1
    点击左侧菜单    机制设计
    新增机制时新增范式
#    点击左侧菜单    范式
#    范式列表搜索    ${范式名称}
#    删除范式    ${范式名称}
    [Teardown]    Run Keywords    关闭浏览器
