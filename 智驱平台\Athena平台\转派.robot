*** Settings ***
Documentation     zhengsza
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/公共方法.robot
Resource          ../关键字/业务关键字/交接.robot
Resource          ../配置/交接参数.robot
Resource          ../关键字/业务关键字/作业授权.robot
Resource          ../关键字/业务关键字/转派.robot
Resource          ../关键字/控件关键字/单元格.robot
Resource          ../关键字/业务关键字/交接.robot
Resource          ../配置/交接参数.robot
Test Setup        Run Keywords    环境设定
Test Teardown     Close Browser


*** Test Cases ***
#2025S4需求#44592自动化用例覆盖
团队任务-进入任务卡详情转派给自己存在重复卡
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02
    ${req_random}    Set Variable    ${唯一标识}
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    作业授权.进入发起项目菜单
    进入-小AI-五要素基线用例专用作业--发起提交    ${req_random}
    团队任务-进入任务卡详情转派给自己存在重复卡  测试需求评审     ${req_random}   ${ownerUsername}
    Sleep   5
    断言重复卡转派结果  ${req_random}



团队任务-任务详情转派给自己，无重复卡转派成功
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02
    #转派人
    ${reassignUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${reassignUsernamepwd}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    # Set Global Variable    ${projectname}    ${长唯一标识}
    登录ATHENA平台    ${reassignUsername}    ${reassignUsernamepwd}
    #生成团队任务任务卡
    发起合并单元格项目卡  2025    202501  202502
    关闭浏览器
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    团队任务-进入任务卡详情转派给自己，无重复卡  条款送审  ${ownerUsername}
    断言任务卡转派结果  条款送审
    


个人任务转派--检查转派来源
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    wgzy0001    '${ENV}'=='huawei.prod'    wgzy0001    '${ENV}'=='microsoft.prod'    wgzy0001
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    Wgzy0001    '${ENV}'=='huawei.prod'    Wgzy0001    '${ENV}'=='microsoft.prod'    Wgzy0001
    ${ownerUsername02}    Set Variable If    '${ENV}'=='huawei.test'    HL18271405997    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02
    ${ownerPassword02}    Set Variable If    '${ENV}'=='huawei.test'    HuangL0920    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02
    ${req_random}    Set Variable    ${唯一标识}
    ${hash_value}    Set Variable If    '${ENV}'=='huawei.test'    ${hash_value_test}    '${ENV}'=='huawei.prod'    ${hash_value_hwprod}    '${ENV}'=='microsoft.prod'    ${hash_value_wrprod}
    ${username02}    Set Variable If    '${ENV}'=='huawei.test'    hl_test    '${ENV}'=='huawei.prod'    生产华为环境自动化测试小AI入口
    Set Global Variable    ${hash_value}
    登录Athena平台     ${ownerUsername}    ${ownerPassword}
    进入发起项目菜单
    进入-小AI-五要素基线用例专用作业--发起提交    ${req_random}
    任务转派    ${req_random}    ${ownerUsername02}
    退出当前登录,切换账号登录    ${ownerUsername02}    ${ownerPassword02}
    被转派人-提交任务    ${req_random}


