*** Settings ***
Library           SeleniumLibrary
Resource          ../系统关键字/web.robot
Resource          ../../元素/Athena平台元素.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../系统关键字/接口.robot

*** Keywords ***
前端筛选
    [Arguments]    ${data-colid}    ${searchkey} 
    鼠标悬停    //div[@data-colid='${data-colid}']
    Sleep    3
    当前页面可见    //div[@data-colid='${data-colid}']/*/span[@ref='filter']
    鼠标悬停    //div[@data-colid='${data-colid}']/*/span[@ref='filter']
    Sleep    3
    点击    //div[@data-colid='${data-colid}']/*/span[@ref='filter']
    当前页面可见    //div[@class='ag-filter']
    点击    //input[@placeholder='请输入关键词']
    输入    //input[@placeholder='请输入关键词']    ${searchkey}
    Sleep    3
    当前页面可见    xpath=//div[@id='0${searchkey}']
    点击    //div[@id='0${searchkey}']
    Sleep    3
    当前页面可见    xpath=//button[@nztype='primary']
    点击    //button[@nztype='primary']
    当前页面可见    //span[@class='ag-icon active ag-icon-filtered']
    
重置筛选
    [Arguments]    ${data-colid}   ${searchkey} 
    元素存在则点击   //div[@data-colid='${data-colid}']/*/span[@ref="filter"]
    当前页面可见    //div[@class='ag-filter']
    点击  //span[contains(text(),'重置')]
    Sleep   3
    当前页面可见    //div[@data-colid='${data-colid}']/*/span[@ref="filter"]
    点击  //div[@data-colid='${data-colid}']

服务端筛选
    [Arguments]    ${sdata-colid}   ${ssearchkey}
    鼠标悬停   //div[@data-colid='${sdata-colid}']   3
    当前页面可见    //div[@data-colid='${sdata-colid}']/*/span[@ref='filter']
    鼠标悬停   //div[@data-colid='${sdata-colid}']/*/span[@ref='filter']   3
    点击  //div[@data-colid='${sdata-colid}']/*/span[@ref='filter']
    当前页面可见    //input[@placeholder='请输入关键词']
    点击  //input[@placeholder='请输入关键词']
    输入  //input[@placeholder='请输入关键词']    ${ssearchkey}
    Sleep  10
    Run Keyword If  '${ssearchkey}'=='功能测试'   点击  //div[@class='main-label'][contains(text(),'${ssearchkey}')]
    点击  //button[@nztype='primary']
    Sleep   5
    当前页面可见    //span[@class='ag-icon active ag-icon-filtered']

日期控件后端筛选
    [Arguments]    ${dataselect}    ${start-time}   ${end-time}
    鼠标悬停   //div[@data-colid='${dataselect}']   3
    当前页面可见    //div[@data-colid='${dataselect}']/div/span[@ref='filter']
    # 鼠标悬停  //div[@data-colid='${dataselect}']/*/span[@ref="filter"]
    # Sleep  3
    点击  //div[@data-colid='${dataselect}']/div/span[@ref='filter']
    当前页面可见  //input[@placeholder='开始日期']
    点击  //input[@placeholder='开始日期']
    Sleep  3
    输入   //input[@placeholder='开始日期']    ${start-time}
    点击   //input[@placeholder='结束日期']
    输入   //input[@placeholder='结束日期']    ${end-time}
    Press Keys    //input[@placeholder='结束日期']    RETURN
    Sleep  3
    点击  //button[@nztype='primary']
    当前页面可见    //span[@class='ag-icon active ag-icon-filtered']
    点击   //div[@data-colid='${dataselect}']/div/span[@ref="filter"]
    当前页面可见    //div[@class='ag-filter']
    点击  //span[contains(text(),'重置')]
    当前页面可见    //div[@data-colid='${dataselect}']/div/span[@ref='filter']
