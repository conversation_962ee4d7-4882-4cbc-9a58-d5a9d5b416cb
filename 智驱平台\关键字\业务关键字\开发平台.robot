*** Settings ***
Library           SeleniumLibrary
Resource          ../系统关键字/web.robot
Resource          ../系统关键字/接口.robot
Resource          ../../元素/开发平台元素.robot
Resource          ../系统关键字/公共方法.robot

*** Keywords ***
登录开发平台
    [Arguments]    ${userName}    ${passWord}    ${tenant}
    打开网页    ${PLATFORM_ENV}    ${browser}
    Sleep    2
    #登录时白屏刷新页面
#    当元素不可见则刷新页面    3    //input[@type='password']
    输入    //input[@formcontrolname='userId']    ${userName}
    输入    //input[@type='password']    ${passWord}
    点击    //span[@class='ng-star-inserted']
    点击    //span[contains(text(),'${tenant}')]
    弹窗处理

切换租户
    [Arguments]    ${tanent}
    Log    敬请期待

应用配置
    [Arguments]    ${application}
    Log    敬请期待

新增机制
    [Arguments]    ${mechanismName}=UI自动化机制${唯一标识}    ${paradigmName}=范式名称不可删除
    Set Global Variable    ${mechanismName}
    #在列表没有机制的时候，新增按钮是另外的样式，此处要加以判断
    ${visible}    判断元素是否可见    //div[@class='add-mechanism ng-star-inserted']    30
    Run Keyword If    ${visible}    点击    //div[@class='add-mechanism ng-star-inserted']
    ...    ELSE    点击    //span[contains(text(),'新增机制')]
    Sleep    2
    输入    //input[@class='ant-input ath-input ng-untouched ng-pristine ng-valid ng-star-inserted']    ${mechanismName}
    Sleep    2
    Click Element    css=.ant-select-selection-search-input
    Sleep    2
    Execute Javascript    document.querySelector('.ant-select-selection-search-input').removeAttribute('style');
    Execute Javascript    document.querySelector('.ant-select-selection-search-input').removeAttribute('readonly');
    输入    css=.ant-select-selection-search-input    ${paradigmName}
    点击    //span[contains(text(),'${paradigmName}')]
    点击    //span[contains(text(),'确定')]
    Sleep    5
    机制列表搜索    ${mechanismName}
    鼠标悬停    //div[@class='word'][@title='${mechanismName}']
    sleep    1
    #编辑图标
    点击    //div[contains(text(),'${mechanismName}')]/parent::div/following-sibling::i[@class='setIcon ant-dropdown-trigger']
    点击    //li[contains(text(),'编辑')]
    ${mechanismCode}    获取元素值    //input[@class='ant-input ath-input ng-untouched ng-pristine ng-star-inserted ant-input-disabled']
    #获取机制code，后面购买需要用到
    Set Global Variable    ${mechanismCode}
    点击    //span[contains(text(),'取消')]

跳转到首页
    点击    //div[@class='title ng-star-inserted']

新增机制时新增范式
    [Arguments]    ${mechanismName}=UI自动化机制${唯一标识}
    ${code}    生成毫秒时间戳
    #点击新增机制
    点击    //div[@class='add-mechanism ng-star-inserted']
    #点击范式输入框
    Click Element    css=.ant-select-selection-search-input
    点击    //a[contains(.,'新增范式')]
    #    Sleep    3
    点击    //span[@class='ant-checkbox ant-checkbox-checked']
    #输入范式名称
    Sleep    2
    ${loc}    按顺序获取元素    //input[@class='ant-input ath-input ng-untouched ng-pristine ng-valid ng-star-inserted']    1
    输入    ${loc}    机制处新增${code}
    Set Global Variable    ${范式名称}    机制处新增${code}
    Sleep    1
    ${loc}    按顺序获取元素    //span[contains(.,'确定')]    1
    点击    ${loc}
    Sleep    5
    点击    //span[contains(.,'取消')]
    Set Global Variable    ${pdname}    机制处新增${code}
    Sleep    2
    #以下为检查范式新增结果和删除范式
    点击左侧菜单    范式设定
    范式列表搜索    ${pdname}
    删除范式    ${pdname}

机制列表搜索
    [Arguments]    ${mechanismName}
    输入    //input[@placeholder="请输入机制名称或代号"]    ${mechanismName}
    Sleep    3
    #Page Should Not Contain    测试范式不可删除
    Sleep    5

删除机制
    [Arguments]    ${mechanismName}
    鼠标悬停    //div[@class='word'][@title='${mechanismName}']
    sleep    1
    #编辑图标
    #点击    //div[contains(text(),'${mechanismName}')]/parent::div/following-sibling::i[@class='setIcon ant-dropdown-trigger']
    点击    //div[text()=' ${mechanismName} ']/parent::div/following-sibling::i[@class='setIcon ant-dropdown-trigger']
    点击    //li[contains(text(),'删除')]
    Sleep    1
    点击    //span[contains(text(),'确定')]
    Sleep    5

新增机制原理
    [Arguments]    ${principle}    ${principleDetail}
    点击    //*[contains(text(),'新增机制原理')]
    输入    //input[@class='ant-input ath-input ng-untouched ng-pristine ng-valid ng-star-inserted']    ${principle}
    Sleep    1
    输入    //textarea[@class='ant-input ath-input ng-untouched ng-pristine ng-valid ng-star-inserted']    ${principleDetail}
    Sleep    1
    点击    //span[contains(text(),'确定')]
    Sleep    3
    Set Global Variable    ${principle}
    Set Global Variable    ${principleDetail}

发版
    [Arguments]    ${env}    ${tenant}
    点击    //*[text()=' 发版 ']
    Sleep    3
    ${text}    Get Text    //*[contains(text(),'选择环境')]/following::div[1]
    Run Keyword If    '${text}'!='${env}'
    ...    Run Keywords    点击    //*[contains(text(),'选择环境')]/following::div[1]
    ...    AND    点击    //*[contains(text(),'${env}')]
    ${text}    Get Text    //*[contains(text(),'选择租户')]/following::div[1]
    Run Keyword If    '${text}'!='${tenant}'
    ...    Run Keywords    点击    //*[contains(text(),'选择租户')]/following::div[1]
    ...    AND    点击    //*[contains(text(),'${tenant}')]
    输入    //*[contains(text(),'描述')]/following::*/textarea[1]    UI自动化发版测试
    点击    //span[contains(text(),'版本更新')]
    Wait Until Page Contains    更新成功，你可以去Athena上看下 运行结果    120

测试发版
    [Arguments]    ${tenant}
    #此处防止一个应用多次发布的时候导致的问题，等待上一次发布结束
    当前页面不可见元素    //button[contains(@class,'ant-btn-dangerous')]    300
    点击    //*[text()='大陆测试区']/following::span[text()='发 版'][1]
    当前页面不可见元素    //span[@class='ant-spin-dot ant-spin-dot-spin']
    ${text}    获取元素字符    //div[@class='tenant-name']
    #    ${res}    Evaluate    ${text} in '${tenant}'
    Run Keyword If    '${tenant}' not in '${text}'    选择租户    ${tenant}
    #    选择租户    ${tenant}
    Sleep    2
    点击    //span[contains(text(),'发 布')]
    当前页面可见字符    发布完成    240
    点击    //span[contains(text(),'确 定')]

正式发版
    [Arguments]    ${tenant}
    当前页面不可见元素    //button[contains(@class,'ant-btn-dangerous')]    300
    点击    //*[text()='大陆正式区']/following::span[text()='发 版'][1]/parent::button
    ${text}    获取元素字符    //div[@class='tenant-name']
    Run Keyword If    '${tenant}' not in '${text}'    选择租户    ${tenant}
    #    选择租户    ${tenant}
    Sleep    2
    点击    //span[contains(text(),'发 布')]
    当前页面可见字符    发布完成    240
    点击    //span[contains(text(),'确 定')]

选择租户
    [Arguments]    ${tenant}
    点击    //span[@class='add-tenant']
    输入    //input[@type='text']    ${tenant}
    点击    //span[contains(text(),'${tenant}')]
    点击    //span[contains(text(),'确 定')]

编译
    当前页面不可见元素    //button[contains(@class,'ant-btn-dangerous')]    300
    点击    //span[contains(text(),'编 译')]
    Sleep    3
    点击    //span[contains(text(),'确 定')]
    当前页面可见字符    编译成功！

应用发布
    [Arguments]    ${env}    ${tenant}    ${version}=1.0
    Run Keyword If    '${version}'=='1.0'    点击    //a[contains(@href,'/app/app-publish')]
    ...    ELSE    点击    //a[@class='ng-star-inserted']//span[contains(text(),'发布')]
    当前页面可见字符    编 译
    #    编译
    #    测试发版    ${tenant}
    #    切版    ${tenant}
    #    正式发版    ${tenant}
    Run Keyword If    '${env}'=='大陆测试区'    Run Keywords    编译
    ...    AND    测试发版    ${tenant}
    ...    AND    测试切版    ${tenant}
    ...    ELSE    Run Keywords    编译
    ...    AND    测试发版    ${tenant}
    ...    AND    测试切版    ${tenant}
    ...    AND    正式发版    ${tenant}
    ...    AND    正式切版    ${tenant}

编译发版
    [Arguments]    ${name}
    Log    敬请期待

测试切版
    [Arguments]    ${tenant}
    当前页面不可见元素    //button[contains(@class,'ant-btn-dangerous')]    300
    点击    //*[text()='大陆测试区']/following::span[text()='切 版'][1]
    Sleep    10
    #    ${text}    获取元素字符    //*[@class='tenant-name']
    #    #${res}    Evaluate    ${text} in '${tenant}'
    #    Run Keyword If '${tenant}' not in '${text}'    选择租户    ${tenant}
    选择租户    ${tenant}
    点击    //span[contains(text(),'发 布')]
    当前页面可见字符    发布完成    240
    点击    //span[contains(text(),'确 定')]

正式切版
    [Arguments]    ${tenant}
    当前页面不可见元素    //button[contains(@class,'ant-btn-dangerous')]    300
    点击    //*[text()='大陆正式区']/following::span[text()='切 版'][1]
    Sleep    10
    #    ${text}    获取元素字符    //*[@class='tenant-name']
    #    #${res}    Evaluate    ${text} in '${tenant}'
    #    Run Keyword If '${tenant}' not in '${text}'    选择租户    ${tenant}
    选择租户    ${tenant}
    点击    //span[contains(text(),'发 布')]
    当前页面可见字符    发布完成    240
    点击    //span[contains(text(),'确 定')]

点击顶部菜单
    [Arguments]    ${menu}
    点击    //span[contains(text(),'${menu}')]
    #loading提示
    当前页面不可见元素    //span[@class='ant-spin-dot ant-spin-dot-spin ng-star-inserted']    60

进入应用配置页
    [Arguments]    ${application}
    点击    //p[@class='code line-one'][contains(text(),'${application}')]
    Sleep    5

搜索应用
    [Arguments]    ${application}
    输入    //input[@placeholder="请输入解决方案名称"]    ${application}
    当前页面不可见元素    //span[@class='ant-spin-dot ant-spin-dot-spin ng-star-inserted']

点击左侧菜单
    [Arguments]    ${menu}
    当前页面可见字符    ${menu}
    Click Link    ${menu}
    #loading提示框
    当前页面不可见元素    //span[@class='ant-spin-dot ant-spin-dot-spin']

新增范式
    [Arguments]    ${autoAddComponents}=no
    ${paradigmCode}    生成毫秒时间戳
    ${paradigmName}    Set Variable    范式名称${paradigmCode}
    Set Global Variable    ${paradigmName}
    点击    ${新增范式}
    点击    //span[contains(text(),'新增范式')]
    点击    //span[contains(text(),'确定')]
    输入    //input[@class="ant-input ng-untouched ng-pristine ng-valid ng-star-inserted inner-label-input"]    ${paradigmName}
    Comment    ${autoAddComponents}    Get WebElements    //input[@class='ant-select-selection-search-input ng-untouched ng-pristine ng-valid']
    Comment    ${code}    Get From List    ${autoAddComponents}    1
    Comment    Input Text    ${code}    ${paradigmCode}
    点击    ${确定按钮}
    Sleep    3

范式进入组件清单
    [Arguments]    ${paradigmName}
    点击    //div[contains(text(),'${paradigmName}')]

添加组件
    点击    //span[contains(text(),'添加组件')]
    Sleep    1
    Click Element    //td[contains(text(),'purchase99_project_0001')]/preceding-sibling::*//input[@type='checkbox']
    Sleep    1
    点击    //span[contains(text(),'确定')]
    Sleep    3

删除组件
    #获取要删除的数据行
    Wait Until Element Is Visible    //div[contains(text(),'purchase99_project_0001')]/parent::div
    ${row}    Get Element Attribute    //div[contains(text(),'purchase99_project_0001')]/parent::div    row-index
    Click Element    //div[@class='ag-pinned-left-cols-container']//div[@row-index='${row}']//input[@type='checkbox']
    Sleep    1
    点击    //span[contains(text(),'移除')]
    Sleep    1
    点击    //span[@class='ng-star-inserted'][contains(text(),'确定')]

范式清单
    点击    //*[contains(text(),'范式清单')]

组件清单
    点击    //div[contains(text(),'组件清单')]
    #简易校验
    当前页面可见字符    采购管理_项目_0001
    点击    //div[contains(text(),'采购管理_项目_0002')]/following-sibling::*//a[contains(text(),'查看')]
    点击    //span[contains(text(),'关闭')]
    点击    //div[contains(text(),'任务')]
    当前页面可见字符    采购申请提交
    点击    //div[contains(text(),'异常回报')]/following-sibling::*//a[contains(text(),'查看')]
    点击    //span[contains(text(),'关闭')]
    点击    //div[contains(text(),'侦测')]
    当前页面可见字符    金明的数据侦测勿动
    点击    //div[contains(text(),'金明的数据侦测勿动')]/following-sibling::*//a[contains(text(),'查看')]
    点击    //span[contains(text(),'关闭')]
    点击    //div[contains(text(),'机制变量')]
    当前页面可见字符    指派能力
    点击    //div[contains(text(),'指派能力')]/following-sibling::*//a[contains(text(),'查看')]
    点击    //span[contains(text(),'关闭')]
    点击    //div[contains(text(),'数据录入')]
    当前页面可见字符    采购管理
    点击    //div[contains(text(),'测试机制')]/following-sibling::*//a[contains(text(),'查看')]
    点击    //span[contains(text(),'关闭')]
    点击    //div[contains(text(),'报表')]
    当前页面可见字符    自动化测试
    点击    //div[contains(text(),'自动化测试')]/following-sibling::*//a[contains(text(),'查看')]
    点击    //span[contains(text(),'关闭')]
    点击    //div[contains(text(),'服务编排')]
    当前页面可见字符    自动化测试
    点击    //div[contains(text(),'自动化测试')]/following-sibling::*//a[contains(text(),'查看')]
    点击    //span[contains(text(),'关闭')]
    Sleep    1

编辑机制
    [Arguments]    ${mechanismName}
    Mouse Over    //div[contains(text(),'${mechanismName}')]
    Sleep    2
    Click Element    css=.setIcon:nth-child(4) > .iconfont
    Sleep    2
    点击    //li[contains(text(),'编辑')]
    #${mechanismName}    Set Variable    修改后${mechanismName}
    Set Global Variable    ${mechanismName}    修改后${mechanismName}
    Sleep    1
    输入    //div[@class='ath-input-content-box ng-star-inserted']//input[@class='ant-input ath-input ng-untouched ng-pristine ng-valid ng-star-inserted']    ${mechanismName}
    Sleep    2
    点击    //span[contains(text(),'确定')]
    Sleep    10

复制机制
    [Arguments]    ${mechanismName}
    Mouse Over    //div[contains(text(),'${mechanismName}')]
    Sleep    2
    #点击图标
    Click Element    css=.setIcon:nth-child(4) > .iconfont
    Sleep    2
    点击    //li[contains(text(),'复制')]
    #输入    //div[@class='ath-input-content-box ng-star-inserted']//input[@class='ant-input ath-input ng-untouched ng-pristine ng-valid ng-star-inserted']    ${mechanismName}
    Sleep    2
    点击    //span[@class='ng-star-inserted'][contains(text(),'确定')]
    Sleep    10
    Set Global Variable    ${mechanismNameCopy}    ${mechanismName}_复制
    #检查复制结果
    Page Should Contain    ${mechanismNameCopy}

进入机制设计页
    [Arguments]    ${mechanismName}
    Comment    Mouse Over    //div[contains(text(),'${mechanismName}')]
    鼠标悬停    //div[@class='word'][@title='${mechanismName}']
    Sleep    2
    #点击    css=.editIcon > .iconfont
    点击    //i[@class='editIcon ng-star-inserted']//*[@class='iconfont']
    Comment    输入    //input[@placeholder="请输入确认密码"]    km2077
    Comment    点击    //span[contains(text(),'确定')]
    Sleep    3

新增限制能力-熟练度
    [Arguments]    ${capacityName}    ${capacityDesc}    ${specificPersonnel}    ${value}
    点击    ${新增机制能力}
    输入    //input[@class='ant-input ath-input ng-untouched ng-pristine ng-valid ng-star-inserted']    ${capacityName}
    输入    //textarea[@class='ant-input ath-input ng-untouched ng-pristine ng-valid ng-star-inserted']    ${capacityDesc}
    点击    //div[contains(text(),'+机制能力')]
    Sleep    2
    Click Element    css=.ant-select-selection-search-input
    点击    //span[contains(text(),'限制能力')]
    点击    ${确定按钮}
    点击    //div[@class='source-card']
    Click Element    css=.ant-cascader-picker-label    #点击工作零件工作下拉框
    点击    //span[contains(text(),'业务数据录入')]    #项目名称
    点击    //span[@class='ng-star-inserted'][contains(text(),'采购管理')]    #基础资料名称
    点击    //*[text()='请选择']/ancestor::div/i
    点击    //span[contains(text(),'打开时')]    #任务名称
    点击    //div[contains(text(),'2、触发限制的条件')]    #点击第二步
    点击    //div[@class='source-card']
    Sleep    1
    ${ele}    按顺序获取元素    //i[@class='anticon athena-select-arrow-down ng-star-inserted']    0
    点击    ${ele}
    点击    //span[contains(text(),'${specificPersonnel}')]    #人员,职能,部门成员
    点击    //ath-select-item[contains(text(),'等于')]
    点击    //span[contains(text(),'大于等于')]
    Sleep    1
    Click Element    css=.ant-select-selection-search > .ng-untouched    #后期看能不能优化定位
    Sleep    1
    点击    //span[contains(text(),'自定义值')]
    输入    //input[@class='ant-input-number-input ng-untouched ng-pristine ng-valid']    1
    点击    //div[contains(text(),'3、限制的行动 ')]    #点击第三步
    点击    //div[contains(text(),' 提示画面')]
    点击    //*[text()='请选择提示类型']/ancestor::div[contains(@class,'inline-container')]
    点击    //span[contains(text(),'自定义')]
    点击    css=.CodeMirror-line
    Press Key    css=.CodeMirror textarea    ${msg}
    点击    //span[contains(text(),'保存')]
    Sleep    10

新增指派能力-特定人员
    [Arguments]    ${capacityName}    ${capacityDesc}    ${specificPersonnel}    ${value}
    点击    ${新增机制能力}
    输入    //input[@class='ant-input ath-input ng-untouched ng-pristine ng-valid ng-star-inserted']    ${capacityName}
    输入    //textarea[@class='ant-input ath-input ng-untouched ng-pristine ng-valid ng-star-inserted']    ${capacityDesc}
    点击    //div[contains(text(),'+机制能力')]
    Sleep    2
    Click Element    css=.ant-select-selection-search-input
    点击    //span[contains(text(),'指派能力')]
    点击    ${确定按钮}
    点击    //div[@class='source-card']
    Click Element    css=.ant-cascader-picker-label    #点击工作零件工作下拉框
    点击    //span[contains(text(),'采购管理_项目_0001')]    #项目名称
    点击    //span[contains(text(),'采购申请提交')]    #任务名称
    点击    //*[contains(text(),' 2、指派规则 ')]    #点击第二步
    点击    //div[contains(text(),'特定人员')]
    Sleep    1
    Comment    点击    //*[text()='${specificPersonnel}']    #多语言存在bug
    Click Element    css=.athena-select-selection-item
    Sleep    1
    点击    //span[contains(text(),'${specificPersonnel}')]    #人员,职能,部门成员
    Sleep    1
    Click Element    css=.ant-select-selection-search > .ng-untouched    #后期看能不能优化定位
    Sleep    1
    Comment    输入    //input[@class='ant-select-selection-search-input ng-valid ng-touched ng-dirty']    陈金明
    点击    //span[contains(text(),'${value}')]
    点击    //span[contains(text(),'保存')]
    Sleep    10

删除机制原理
    [Arguments]    ${principle}
    点击    //*[contains(text(),'${principle}')]/following-sibling::*//div[contains(text(),'删除')]
    点击    //span[contains(text(),'确定')]

范式列表搜索
    [Arguments]    ${paradigmName}
    输入    //input[@placeholder="请输入范式名称或代号"]    ${paradigmName}
    Sleep    3
    当前页面不可见字符    范式名称不可删除
    #获取范式code，后续购买需要用到
    ${paradigmCode}    获取元素字符    //div[@class='paradigm-code']
    Set Global Variable    ${paradigmCode}

删除范式
    [Arguments]    ${paradigmName}
    鼠标悬停    //div[contains(text(),'${paradigmName}')]
    点击    //div[contains(text(),'${paradigmName}')]/following-sibling::div/i[contains(@iconfont,'icondelete')]
    点击    //span[@class='ng-star-inserted'][contains(text(),'确定')]
    Sleep    3

修改机制原理
    [Arguments]    ${befor_principle}    ${principle}    ${principleDetail}    # 修改前|修改后机制原理名称|修改后机制原理描述
    点击    //*[contains(text(),'${befor_principle}')]/following-sibling::*//div[contains(text(),'编辑')]
    输入    //input[@class='ant-input ath-input ng-untouched ng-pristine ng-valid ng-star-inserted']    ${principle}
    Sleep    1
    输入    //textarea[@class='ant-input ath-input ng-untouched ng-pristine ng-valid ng-star-inserted']    ${principleDetail}
    点击    //span[contains(text(),'确定')]
    Sleep    3
    Set Global Variable    ${principle}
    Set Global Variable    ${principleDetail}

开发分支创建备份分支
    [Arguments]    ${branchName}    ${branchDesc}
    点击    //span[contains(text(),'开发分支')]
    点击    //div[@class='name-and-info no-info']/following-sibling::*/i[1]
    分支信息填写    ${branchName}    ${branchDesc}
    点击    //span[contains(text(),'开发分支')]
    sleep    5
    Page Should Contain    ${branchName}
    点击    //span[contains(text(),'开发分支')]

开发分支创建测试分支
    [Arguments]    ${branchName}    ${branchDesc}
    点击    //span[contains(text(),'开发分支')]
    点击    //div[@class='name-and-info no-info']/following-sibling::*/i[2]
    分支信息填写    ${branchName}    ${branchDesc}
    点击    //span[contains(text(),'开发分支')]
    sleep    3
    Page Should Contain    ${branchName}
    点击    //span[contains(text(),'开发分支')]

切换分支后验证
    [Arguments]    ${mechanismName}
    [Documentation]    切换分支后验证机制是否复制
    点击左侧菜单    机制设计
    Sleep    5
    Page Should Contain    ${mechanismName}

切换分支
    [Arguments]    ${after}
    ${exist}    判断元素是否可见    //*[contains(text(),'${after}')]
    Run Keyword If    '${exist}'=='False'    分支切换操作    ${after}

分支切换操作
    [Arguments]    ${after}
    点击    //i[@type='down']
    当前页面可见字符    ${after}
    点击    //*[contains(text(),'${after}')]
    sleep    10

测试分支创建备份分支
    [Arguments]    ${branchName}    ${branchDesc}
    点击    //span[contains(text(),'测试分支')]
    Sleep    2
    点击    //*[contains(text(),'测试分支')]/following::i[1][@aria-hidden='true']    #点击创建备份分支
    分支信息填写    ${branchName}    ${branchDesc}
    点击    //span[contains(text(),'测试分支')]
    sleep    3
    Page Should Contain    ${branchName}
    点击    //span[contains(text(),'测试分支')]

测试分支创建正式分支
    [Arguments]    ${branchName}    ${branchDesc}
    点击    //span[contains(text(),'测试分支')]
    Sleep    2
    点击    //*[contains(text(),'测试分支')]/following::i[2][@aria-hidden='true']    #点击创建正式分支
    分支信息填写    ${branchName}    ${branchDesc}
    点击    //span[contains(text(),'测试分支')]
    sleep    3
    Page Should Contain    ${branchName}
    点击    //span[contains(text(),'测试分支')]

正式分支创建备份分支
    [Arguments]    ${branchName}    ${branchDesc}
    点击    //span[contains(text(),'正式分支')]
    Sleep    2
    点击    //*[contains(text(),'正式分支')]/following::i[1][@aria-hidden='true']    #点击创建备份分支
    分支信息填写    ${branchName}    ${branchDesc}
    点击    //span[contains(text(),'正式分支')]
    sleep    3
    Page Should Contain    ${branchName}
    点击    //span[contains(text(),'正式分支')]

机制名称输入校验
    [Arguments]    ${checkType}
    Run Keyword If    '${checkType}'=='长度超过50'    机制名称输入长度超50校验
    ...    ELSE IF    '${checkType}'=='为空'    机制名称为空提交

机制名称输入长度超50校验
    Reload Page
    点击    //div[@class='add-mechanism ng-star-inserted']
    sleep    3
    输入    //input[@class='ant-input ath-input ng-untouched ng-pristine ng-valid ng-star-inserted']    测试测试试测试测试试测试测试试测试测试试测试测试试测试测试试测试测试试测试测试试测试测试试测试测试试1
    Sleep    1
    Page Should Contain    请输入机制名称，且字符长度不超过50

机制名称为空提交
    点击    //div[@class='add-mechanism ng-star-inserted']
    sleep    3
    点击    ${确定按钮}
    sleep    1
    Page Should Contain    请输入机制名称，且字符长度不超过50

分支信息填写
    [Arguments]    ${branchName}    ${branchDesc}
    输入    //input[@placeholder="分支名称"]    ${branchName}
    #输入    //textarea[@class='ant-input ath-input ng-untouched ng-pristine ng-valid ng-star-inserted']    ${branchDesc}
    输入    //textarea    ${branchDesc}
    sleep    1
    点击    ${确定按钮}
    sleep    10

弹窗处理
    ${exist}    判断元素是否可见    //div[contains(text(),'升级提醒')]    3
    ${exist_message}    判断元素是否可见    //div[contains(text(),'系统通知')]    3
    ${exist_permission}    判断元素是否可见    //div[contains(text(),'权限申请通知')]    3
    Run Keyword If    ${exist} or ${exist_message} or ${exist_permission}    Run Keywords    点击    //span[contains(text(),'不再提示')]
    ...    AND    点击    //i[@class='anticon close']

新增共享和复制范式
    [Arguments]    ${paradigmName}    ${application}    ${autoAddComponent}=否
    点击    //span[contains(text(),'新增范式')]
    输入    //input[@class='ant-input ath-input ng-untouched ng-pristine ng-valid ng-star-inserted']    ${paradigmName}
    点击    //span[contains(text(),'共享范式')]
    点击    //label[contains(text(),'共享范式至')]/following::input[@placeholder='请选择']
    点击    //td[text()='${application}']/preceding-sibling::td/label
    点击    //button[contains(@class,'ath-btn ant-btn')]//span[@class='ng-star-inserted'][contains(text(),'确定')]
    点击    //span[contains(text(),'复制范式')]
    点击    //label[contains(text(),'复制范式至')]/following::input[@placeholder='请选择']
    点击    //td[text()='${application}']/preceding-sibling::td/label
    点击    //button[contains(@class,'ath-btn ant-btn')]//span[@class='ng-star-inserted'][contains(text(),'确定')]
    Run Keyword If    '${autoAddComponent}'=='否'    点击    //span[contains(text(),'自动添加组件')]
    点击    //span[contains(text(),'确定')]
    sleep    3
    #等待loading消失
    当前页面不存在元素    //span[@class="ant-spin-dot ant-spin-dot-spin ng-star-inserted"]

新增共享范式
    [Arguments]    ${paradigmName}    ${application}    ${autoAddComponent}=否
    Sleep    10
    点击    //span[contains(text(),'新增范式')]
    输入    //input[@class='ant-input ath-input ng-untouched ng-pristine ng-valid ng-star-inserted']    ${paradigmName}
    点击    //span[contains(text(),'共享范式')]
    点击    //input[@placeholder='请选择']
    输入    //input[@placeholder='请输入应用名称或代号']    ${application}
    sleep    5
    点击    //td[text()='${application}']/preceding-sibling::td/label
    点击    //button[contains(@class,'ath-btn ant-btn')]//span[@class='ng-star-inserted'][contains(text(),'确定')]
    Run Keyword If    '${autoAddComponent}'=='否'    点击    //span[contains(text(),'自动添加组件')]
    点击    //span[contains(text(),'确定')]
    sleep    3

新增复制范式
    [Arguments]    ${paradigmName}    ${application}    ${autoAddComponent}=否
    点击    //span[contains(text(),'新增范式')]
    输入    //input[@class='ant-input ath-input ng-untouched ng-pristine ng-valid ng-star-inserted']    ${paradigmName}
    点击    //span[contains(text(),'复制范式')]
    点击    //input[@placeholder='请选择']
    点击    //td[text()='${application}']/preceding-sibling::td/label
    点击    //button[contains(@class,'ath-btn ant-btn')]//span[@class='ng-star-inserted'][contains(text(),'确定')]
    Run Keyword If    '${autoAddComponent}'=='否'    点击    //span[contains(text(),'自动添加组件')]
    点击    //span[contains(text(),'确定')]
    sleep    3

编辑共享范式解绑
    [Arguments]    ${paradigmName}
    鼠标悬停    //div[contains(text(),'${paradigmName}')]/following::a[1]
    点击    //*[contains(text(),'编辑')]
    #    当前页面可见字符    已选择1个共享应用
    ${text}    Get Value    //label[contains(text(),'共享范式至')]/following::input[@placeholder='请选择']
    Should Be Equal As Strings    ${text}    已选择1个共享应用
    点击    //span[contains(text(),'共享范式')]
    点击    //span[contains(text(),'确定')]

编辑共享范式
    [Arguments]    ${paradigmName}
    鼠标悬停    //div[contains(text(),'${paradigmName}')]/following::a[1]
    点击    //*[contains(text(),'编辑')]
    #    当前页面可见字符    已选择1个共享应用
    ${value}    获取元素值    //label[contains(text(),'共享范式至')]/following::input[@placeholder='请选择']
    Should Be Equal As Strings    ${value}    已选择1个共享应用
    输入    //input[@class='ant-input ath-input ng-untouched ng-pristine ng-valid ng-star-inserted']    ${paradigmName}修改
    Sleep    1
    点击    //span[contains(text(),'确定')]
    sleep    3

删除共享范式
    [Arguments]    ${paradigmName}
    鼠标悬停    //div[contains(text(),'${paradigmName}')]/following::a[1]
    点击    //*[text()='删除']
    点击    //span[contains(text(),'确定')]
    #以下注释，兼容数据清理的场景
    #当前页面可见字符    删除成功

共享范式
    [Arguments]    ${paradigmName}
    鼠标悬停    //div[contains(text(),'${paradigmName}')]/following::a[1]
    点击    //*[contains(text(),'共享范式')]

搜索范式
    [Arguments]    ${paradigmName}
    Sleep    2
    #等待loading消失
    当前页面不存在元素    //span[@class='ant-spin-dot ant-spin-dot-spin']
    当前页面不存在元素    //span[@class="ant-spin-dot ant-spin-dot-spin ng-star-inserted"]
    输入    //input[@placeholder='请输入范式名称或代号']    ${paradigmName}
    当前页面包含元素    //div[contains(text(),'${paradigmName}')]
    sleep    3

范式发布
    [Arguments]    ${paradigmName}    ${ENV}
    #    点击    //span[contains(text(),'范式发布')]
    #    搜索范式    ${paradigmName}
    #    点击    //div[contains(text(),'${paradigmName}')]/preceding::label
    点击    //span[contains(text(),'范式发布')]
    Sleep    3
    Click Element    //input[@class='ant-select-selection-search-input ng-untouched ng-pristine ng-valid']
    点击    //div[text()='${ENV}']
    Sleep    3
    Click Element    //button//span[text()=' 发布 ']
    当前页面可见字符    发布成功

共享范式解绑
    [Arguments]    ${paradigmName}    ${application}
    鼠标悬停    //div[contains(text(),'${paradigmName}')]/following::a[1]
    点击    //li[contains(text(),'共享')]
    点击    //span[contains(text(),'${application}')]
    点击    //*[contains(text(),'确定')]

共享范式解绑后校验
    [Arguments]    ${paradigmName}
    搜索范式    ${paradigmName}
    鼠标悬停    //div[contains(text(),'${paradigmName}')]/following::a[1]
    点击    //*[contains(text(),'共享')]
    当前页面可见字符    当前还未关联应用，可在“编辑-范式设置”中进行添加！
    点击    //*[contains(text(),'取消')]

编辑共享范式解绑限制
    [Arguments]    ${paradigmName}    ${application}
    鼠标悬停    //div[contains(text(),'${paradigmName}')]/following::a[1]
    点击    //*[contains(text(),'编辑')]
    ${text}    Get Value    //label[contains(text(),'共享范式至')]/following::input[@placeholder='请选择']
    Should Be Equal As Strings    ${text}    已选择1个共享应用
    点击    //span[contains(text(),'共享范式')]
    点击    //span[contains(text(),'确定')]
    当前页面可见字符    当前已选择应用不可解绑，请先移除${application}中范式下关联的机制设计内容及组件清单

查询已购买机制列表加需购买列表
    [Arguments]    ${data}
    ${mechanisms_list}    Create List
    查询应用机制列表    ${data}    
    ${count}    Get Length    ${response_dict['data']['mechanisms']}
    Log    ${count}
    
    ${index}    Set Variable    0
    WHILE    ${index}<${count} and ${count} != 0
        ${mem_code}    Set Variable    ${response_dict['data']['mechanisms'][${index}]['code']}
        ${index}    Evaluate    ${index}+1
        Append To List    ${mechanisms_list}    ${mem_code}
    END
#    Append To List    ${mechanisms_list}     ${mechanismCode}
#    Log Many    ${mechanisms_list}
    RETURN    ${mechanisms_list}

查询应用机制列表
    [Arguments]    ${data}
    POST    ${isv_url}    /deliverydesigner/application/getMechanismsAndParadigm    ${data}

购买范式
    [Arguments]    ${data}
    清除租户范式    ${data}
    租户购买范式    ${data}

清除租户范式
    [Arguments]    ${data}
    POST    ${knowledgemaps}    /restful/service/knowledgegraph/component/clearTenantApplicationComponentListByMechanism    ${data}

租户购买范式
    [Arguments]    ${data}
    POST    ${knowledgemaps}    /restful/service/knowledgegraph/component/InitializeTenantApplicationComponentList    ${data}

发起项目
    [Arguments]    ${data}
    POST    ${taskengine}    /restful/standard/taskengine/api/project/create    ${data}

获取项目详情
    [Arguments]    ${data}
    POST    ${taskengine}    /restful/standard/taskengine/v1/projects/get-project-instance    ${data}

获取任务详情
    [Arguments]    ${data}
    POST    ${taskengine}    /restful/standard/taskengine/api/activity/list/view    ${data}

指派能力断言
    [Arguments]    ${goal}    ${actual}
    Should Be Equal As Strings    ${goal}    ${actual}
    #
    #新增共享范式从共享中心获取
    #
    #新增复制范式从共享中心获取
    #
    #新增共享和复制范式从共享中心获取

引用资产中心的共享范式
    [Arguments]    ${paradigmName}
    点击    //div[contains(text(),'+ 新增范式')]
    点击    //span[contains(text(),'从资产中心获取范式')]
    点击    //span[contains(text(),'共享范式')]
    #开窗选择共享范式
    点击    //input[@class='ant-input ng-star-inserted']
    #选择在共享中心创建的范式
    搜索共享范式    ${paradigmName}
    点击    //td[contains(text(),'${paradigmName}')]/preceding::label[@class='ant-checkbox-wrapper ng-untouched ng-pristine ng-valid ng-star-inserted']
    点击    //button[@class='ant-btn adp-btn mr10 ant-btn-primary']//span[@class='ng-star-inserted'][contains(text(),'确定')]
    点击    //span[contains(text(),'确定')]
    当前页面不可见字符    保存成功

搜索共享范式
    [Arguments]    ${paradigmName}
    Sleep    2
    #等待loading消失
    当前页面不存在元素    //span[@class='ant-spin-dot ant-spin-dot-spin']
    当前页面不存在元素    //span[@class="ant-spin-dot ant-spin-dot-spin ng-star-inserted"]
    输入    //div[@class='search-panel']//nz-input-group[@class='ant-input-affix-wrapper']//input[@type='text']    ${paradigmName}
    sleep    3

引用资产中心的复制范式（普通范式）
    [Arguments]    ${paradigmName}
    点击    //div[@class='add-paradigm']
    点击    //span[contains(text(),'从资产中心获取范式')]
    点击    //span[contains(text(),'复制范式')]
    #开窗选择共享范式
    点击    //input[@class='ant-input ng-star-inserted']
    #选择在共享中心创建的范式
    搜索共享范式    ${paradigmName}
    点击    //td[contains(text(),'${paradigmName}')]/preceding::label[@class='ant-checkbox-wrapper ng-untouched ng-pristine ng-valid ng-star-inserted']
    点击    //button[@class='ant-btn adp-btn mr10 ant-btn-primary']//span[@class='ng-star-inserted'][contains(text(),'确定')]
    点击    //span[contains(text(),'确定')]

新增资产中心范式（不绑定应用）
    [Arguments]    ${paradigmName}    ${autoAddComponent}=否
    点击    //span[contains(text(),'新增范式')]
    输入    //input[@class='ant-input ath-input ng-untouched ng-pristine ng-valid ng-star-inserted']    ${paradigmName}
    Run Keyword If    '${autoAddComponent}'=='否'    点击    //span[contains(text(),'自动添加组件')]
    点击    //span[contains(text(),'确定')]
    sleep    3

清理机制
    #将要删除的机制名称存储到列表
    ${elements}    获取元素集合    //div[@class='word']
    ${length}=    Get Length    ${elements}
    ${ele_list}    Create List
    FOR    ${index}    IN RANGE    ${length}
        ${ele}    Get From List    ${elements}    ${index}
        ${title}    获取元素属性值    ${ele}    title
        Run Keyword If    '${title}'!='测试机制不可删除'    Append To List    ${ele_list}    ${title}
    END
    #按名称循环删除机制
    ${length}=    Get Length    ${ele_list}
    FOR    ${index_1}    IN RANGE     ${length}
        ${ele}    Get From List    ${ele_list}    ${index_1}
        删除机制    ${ele}
    END
    Log Many    ${ele_list}

清理范式
    ${elements}    获取元素集合    //div[@class='title']
    ${length}=    Get Length    ${elements}
    ${ele_list}    Create List
    FOR    ${index}    IN RANGE    ${length}
        ${ele}    Get From List    ${elements}    ${index}
        ${title}    Get Text    ${ele}
        Run Keyword If    '${title}'!='预置共享范式不可删除'    Append To List    ${ele_list}    ${title}
    END
    ${length}=    Get Length    ${ele_list}
    FOR    ${index_1}    IN RANGE     ${length}
        ${ele}    Get From List    ${ele_list}    ${index_1}
        删除范式    ${ele}
        元素存在则点击    //span[contains(text(),'我知道了')]    1
    END
    Log Many    ${ele_list}

清理共享范式
    #    ${elements}    Get WebElements    //div[@class='title']
    #    ${length}=    Get Length    ${elements}
    #    FOR    ${index}    IN RANGE    ${length}
    #    ${ele}=    Get From List    ${elements}    ${index}
    #    ${title}    Get Text    ${ele}
    #    Run Keyword If    '${title}' != '预置共享范式不可删除'    删除共享范式    ${title}
    #    ...    # ELSE    Log    无范式可删除
    #    Log    ele at index ${index}: ${elements}
    #    END
    #将要删除的共享范式存储到列表
    Sleep    5
    ${elements}    获取元素集合    //div[@class='title']
    ${length}=    Get Length    ${elements}
    ${ele_list}    Create List
    FOR    ${index}    IN RANGE    ${length}
        ${ele}    Get From List    ${elements}    ${index}
        ${title}    获取元素字符    ${ele}
        Run Keyword If    '${title}'!='预置共享范式不可删除'    Append To List    ${ele_list}    ${title}
    END
    #按名称循环删除机制
    ${length}=    Get Length    ${ele_list}
    FOR    ${index_1}    IN RANGE     ${length}
        ${ele}    Get From List    ${ele_list}    ${index_1}
        删除共享范式    ${ele}
    END
    Log Many    ${ele_list}

限制能力验证
    [Arguments]    ${msg}
    当前页面可见字符    ${msg}

新增控制能力-按时完成-普通侦测
    [Arguments]    ${capacityName}    ${capacityDesc}    ${specificPersonnel}    ${value}
    点击    ${新增机制能力}
    输入    //input[@class='ant-input ath-input ng-untouched ng-pristine ng-valid ng-star-inserted']    ${capacityName}
    输入    //textarea[@class='ant-input ath-input ng-untouched ng-pristine ng-valid ng-star-inserted']    ${capacityDesc}
    点击    //div[contains(text(),'+机制能力')]
    Sleep    2
    Click Element    css=.ant-select-selection-search-input
    点击    //span[contains(text(),'控制能力')]
    点击    ${确定按钮}
    点击    //div[@class='source-card']
    Click Element    css=.ant-cascader-picker-label    #点击工作零件工作下拉框
    点击    //span[contains(text(),'采购管理_项目_0001')]    #项目名称
    点击    //span[contains(text(),'采购申请提交')]    #任务名称
    点击    //*[contains(text(),' 2、指派规则 ')]    #点击第二步
    点击    //div[contains(text(),'特定人员')]
    Sleep    1
    Comment    点击    //*[text()='${specificPersonnel}']    #多语言存在bug
    Click Element    css=.athena-select-selection-item
    Sleep    1
    点击    //span[contains(text(),'${specificPersonnel}')]    #人员,职能,部门成员
    Sleep    1
    Click Element    css=.ant-select-selection-search > .ng-untouched    #后期看能不能优化定位
    Sleep    1
    Comment    输入    //input[@class='ant-select-selection-search-input ng-valid ng-touched ng-dirty']    陈金明
    点击    //span[contains(text(),'${value}')]
    点击    //span[contains(text(),'保存')]
    Sleep    10

按量完成
    [Arguments]    ${type}    ${project}    ${task}    ${status}    ${control_field}    ${condition}    ${condition_value}    ${condition_value_type}    ${value}
    点击    //div[contains(text(),'按量完成')]
    点击    //*[contains(text(),'检查对象类型')]/ancestor::div[@class='edit-col']
    点击    	//span[contains(text(),'选择任务')] 
    点击    //*[text()='检查对象']/ancestor::div[@class='edit-col']
    点击    //span[contains(text(),'${project}')]
    点击    //span[contains(text(),'${task}')]
    点击    //ath-select-item[contains(text(),'待处理')]
    点击    //span[contains(text(),'${status}')]
    点击    //*[contains(text(),'选择控制的字段')]/ancestor::div[@class='mr10']
    #以下替换变量
    点击    //span[contains(text(),'${control_field}')]
    点击    //ath-select-item[contains(text(),'等于')]
    #以下替换变量
    点击    //span[text()='${condition}']
    点击    //ath-select-item[contains(text(),'固定值')]
    #以下替换变量
    点击    	//span[contains(text(),'${condition_value}')]
    点击    //ath-select-item[contains(text(),'自定义')]
    点击    //span[contains(text(),'${condition_value_type}')]
    IF    '${condition_value_type}'=='自定义'
        输入    //input[@class='ant-input-number-input ng-untouched ng-pristine ng-valid']    ${value}
    ELSE IF    '${condition_value_type}'=='机制参数'
        点击    //ath-select-inside-label-placeholder[@class='ant-select-selection-placeholder athena-select-selection-placeholder ng-star-inserted']
        点击    //span[text()='${value}']
    ELSE
        Log    位置类型
    END
    


    