*** Settings ***
Library           SeleniumLibrary
Resource          ../系统关键字/web.robot
Resource          ../../元素/Athena平台元素.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../系统关键字/接口.robot

*** Keywords ***
列排序
    [Arguments]    ${data-colid}    ${ssortkey}    ${hsortkey}
    #升序
    鼠标悬停    //div[@data-colid='${data-colid}']\n
    Sleep    3
    Comment    当前页面可见    //div[@data-colid='${data-colid}']/*/span[@ref="filter"]
    鼠标悬停    //div[@data-colid='${data-colid}']/*/span[@ref="sort"]
    Sleep    3
    点击    //div[@data-colid='${data-colid}']/*/span[@ref="sort"]
    Element Should Be Visible    //div[@class="ag-center-cols-viewport"]/div/div[1]/div[@col-id="${data-colid}"]/div/span/cell-renderer/div[1]/div[1]/div/app-dynamic-input-display[@title="${ssortkey}"]
    Comment    当前页面可见    //div[@class='filter-list ng-star-inserted']
    #降序
    点击    //div[@data-colid='${data-colid}']/*/span[@ref="sort"]
    Element Should Be Visible    //div[@class="ag-center-cols-viewport"]/div/div[4]/div[@col-id="${data-colid}"]/div/span/cell-renderer/div[1]/div[1]/div/app-dynamic-input-display[@title="${hsortkey}"]
    Sleep    3
    #恢复默认设置
    点击    //div[@data-colid='${data-colid}']/*/span[@ref="sort"]
    Sleep    3

复合排序
    [Arguments]    ${data-colid}    ${ssortkey}    ${hsortkey}
    刷新页面
    Sleep    10
    #升序
    鼠标悬停    //div[@data-colid='${data-colid}']
    Sleep    3
    Comment    当前页面可见    //div[@data-colid='${data-colid}']/*/span[@ref="filter"]
    鼠标悬停    //div[@data-colid='${data-colid}']/*/span[@ref="filter"]
    Sleep    3
    点击    //div[@data-colid='feedback_id']/*/span[4]
    点击    //div[@class="cdk-drag multi-item ng-star-inserted"]/ath-select[1]
    点击    //*[contains(text(),'分析报告的唯一编号')]
    点击    //div[@class="cdk-drag multi-item ng-star-inserted"]/ath-select[2]
    点击    //*[contains(text(),'升序')]
    点击    //*[contains(text(),'确定')]
    Sleep    3
    当前页面可见    //div[@class="ag-center-cols-viewport"]/div/div[1]/div[1]/div/span/cell-renderer/div/div/div/app-dynamic-input-display[@title="${ssortkey}"]    3
    #降序
    点击    //div[@data-colid='${data-colid}']/*/span[4]
    点击    //div[@class="cdk-drag multi-item ng-star-inserted"]/ath-select[2]
    点击    //*[contains(text(),'降序')]
    点击    //*[contains(text(),'确定')]
    Sleep    3
    当前页面可见    //div[@class="ag-center-cols-viewport"]/div/div[4]/div[1]/div/span/cell-renderer/div/div/div/app-dynamic-input-display[@title="${hsortkey}"]    3
    #重置
    点击    //div[@data-colid='${data-colid}']/*/span[4]
    点击    //*[contains(text(),'恢复默认排序')]
    Sleep    3

枚举时间排序
    [Arguments]    ${data-colid}    ${ssortkey}    ${hsortkey}    ${key}
    刷新页面
    Sleep    10
    #升序
    鼠标悬停    //div[@data-colid='${data-colid}']
    Sleep    3
    Comment    当前页面可见    //div[@data-colid='${data-colid}']/*/span[@ref="sort"]
    鼠标悬停    //div[@data-colid='${data-colid}']/*/span[@ref="sort"]
    Sleep    3
    点击    //div[@data-colid='${data-colid}']/*/span[@ref="sort"]
    当前页面可见    //div[@class="ag-center-cols-viewport"]/div/div[1]/div[@col-id="${data-colid}"]/div/span/cell-renderer/div[1]/div[1]/div/${key}/span[@title="${ssortkey}"]\n    3
    Comment    当前页面可见    //div[@class='filter-list ng-star-inserted']
    #降序
    点击    //div[@data-colid='${data-colid}']/*/span[@ref="sort"]
    当前页面可见    //div[@class="ag-center-cols-viewport"]/div/div[4]/div[@col-id="${data-colid}"]/div/span/cell-renderer/div[1]/div[1]/div/${key}/span[@title="${hsortkey}"]  3
    Sleep    3
    #恢复默认设置
    点击    //div[@data-colid='${data-colid}']/*/span[@ref="sort"]
    Sleep    3
