*** Settings ***
Documentation
Suite Setup       Run Keywords    环境设定
Test Teardown     Run Keywords    错误处理    关闭浏览器
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/系统关键字/公共方法.robot
Resource          ../关键字/业务关键字/开发平台.robot
Resource          ../关键字/业务关键字/数据分组.robot

*** Test Cases ***
数据分组
    #测试环境ai001 经理-ai002总经理-sd001副董-km001董事长
    #华为生产 ai01-sd01-豆渣-km
    #当责者账号

    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #总经理（GM）
    ${GMUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestSd01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01
    ${GMPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestSd01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01
    #副董（ED）
    ${EDsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    HL18271405997    '${ENV}'=='microsoft.prod'    HL18271405997
    ${EDPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    HuangL0920    '${ENV}'=='microsoft.prod'    HuangL0920
    #董事长（CB）
    ${CBsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestKm01
    ${CBPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestKm01
    #开发平台
    ${tenant}    Set Variable    智驱中台工作台
    ${deploy_to_tenant}     Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    ${deploy_to_env}    Set Variable If    '${ENV}'=='paas'    大陆正式区（阿里）    '${ENV}'=='huawei.test'    大陆测试区    '${ENV}'=='huawei.prod'    大陆正式区    '${ENV}'=='microsoft.prod'    非大陆正式区
    ${application}    Set Variable    模型驱动-采购合同管理
    ${appCode}    Set Variable    M9d63-b4e2
    #开发平台用户名密码
    ${username}    Set Variable    <EMAIL>
    ${password}    Set Variable    Huanglei@0920
    ${athena_tenant}    Set Variable If    '${ENV}'=='pressure'     自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'     自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境

    ${key}    生成毫秒时间戳
    Sleep    0.2
    ${key2}    生成毫秒时间戳


#    ${key}    Set Variable    1753759296426
#    ${key2}    Set Variable    1753759296735


    #发版场景
#    登录开发平台    ${username}    ${password}    ${tenant}
#    开发平台.点击顶部菜单    首页
#    搜索应用    ${application}
#    进入应用配置页    ${appCode}
#    选择浏览器窗体    -1
#    应用发布    ${deploy_to_env}    ${deploy_to_tenant}    2.0
#    关闭浏览器
    #金额3000，TestAthenaAutoTestAi001 -经理-TestAthenaAutoTestAi002-总经理
    登录Athena平台    ${ownerUsername}    ${ownerPassword}    tenant=${athena_tenant}
    手动发起项目    全部    发起项目    ${key}    ${key2}
    核决条件任务卡提交    ${key}    3000
    关闭浏览器
    #总经理审批
    登录Athena平台    ${GMUsername}    ${GMPassword}    tenant=${athena_tenant}
    签核人员信息    ${key}
    关闭浏览器
