<script>
    // 获取 canvas 元素
    const canvas = document.getElementById('canvas');
    const ctx = canvas.getContext('2d');

    // 设置 canvas 大小为窗口大小
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    // 生成星星的函数
    function generateStars(numStars) {
        const stars = [];
        for (let i = 0; i < numStars; i++) {
            const x = Math.random() * canvas.width;
            const y = Math.random() * canvas.height;
            const size = Math.random() * 3;
            stars.push({ x, y, size });
        }
        return stars;
    }

    // 绘制星星的函数，添加闪烁效果
    function drawStars(stars) {
        for (let star of stars) {
            // 随机透明度，产生闪烁效果
            const alpha = Math.random() * 0.5 + 0.5;
            ctx.fillStyle = `rgba(255, 255, 255, ${alpha})`;
            ctx.beginPath();
            // 绘制圆形作为星星
            ctx.arc(star.x, star.y, star.size, 0, 2 * Math.PI);
            ctx.fill();
        }
    }

    // 初始化函数
    function init() {
        const stars = generateStars(500);
        drawStars(stars);
        // 每隔一段时间重新绘制，实现闪烁效果
        setInterval(() => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawStars(stars);
        }, 100);
    }

    // 调用初始化函数
    init();
</script>