import requests
import time

def get_jsessionid():
    """
    发送登录请求，从响应头中获取 JSESSIONID
    :return: JSESSIONID 或 None
    """
    # 接口地址
    url = 'https://www.letuo.club/apis/auth/login'
    # 入参
    data = {
        "username": "<EMAIL>",
        "password": "Cjm820412000",
        "validateCode": "",
        "token": "519d6a2b-6577-460f-99ae-e85bcf85697a",
        "code": "RU5L"
    }
    try:
        # 发送 POST 请求
        response = requests.post(url, json=data)
        # 检查响应状态码
        response.raise_for_status()
        # 获取响应头中的 Set-Cookie 字段
        set_cookie_header = response.headers.get('Set-Cookie')
        if set_cookie_header:
            # 查找 JSESSIONID
            start_index = set_cookie_header.find('JSESSIONID=')
            if start_index != -1:
                start_index += len('JSESSIONID=')
                end_index = set_cookie_header.find(';', start_index)
                if end_index == -1:
                    end_index = len(set_cookie_header)
                jsessionid = set_cookie_header[start_index:end_index]
                print(f"JSESSIONID 值为: {jsessionid}")
                return jsessionid
            else:
                print("响应头的 Set-Cookie 中未找到 JSESSIONID。")
        else:
            print("响应头中没有 Set-Cookie 字段。")
    except requests.exceptions.RequestException as e:
        print(f"请求发生错误: {e}")
    except Exception as e:
        print(f"发生未知错误: {e}")
    return None

def get_eventid(jsessionid):
    """
    循环查询 eventId，直到获取到非零的 eventId
    :param jsessionid: JSESSIONID
    :return: eventId 或 0
    """
    # 请求的 URL
    url = "https://www.letuo.club/apis/orderEvent/query"
    # 请求的入参
    data = {
        "pageIndex": 1,
        "pageSize": 10,
        "sortField": "id",
        "sortType": "descending",
        "value": {
            "teamId": 11,
            "status": 0,
            "name": ""
        }
    }
    # 设置请求头，指定请求内容为 JSON 格式
    headers = {
        "Content-Type": "application/json",
        "Cookie": f"JSESSIONID={jsessionid}"
    }
    eventId = 0
    while eventId == 0:
        try:
            # 发送 POST 请求
            response = requests.post(url, json=data, headers=headers)
            # 检查响应状态码
            if response.status_code == 200:
                # 打印响应的 JSON 数据
                result = response.json()
                if result['totalCount'] != 0:
                    eventId = result['data'][0]['eventId']
            else:
                print(f"请求失败，状态码: {response.status_code}")
        except requests.RequestException as e:
            print(f"请求发生错误: {e}")
        time.sleep(0.5)
    return eventId

def submit_order(jsessionid, eventId):
    """
    提交订餐请求
    :param jsessionid: JSESSIONID
    :param eventId: eventId
    """
    url = "https://www.letuo.club/apis/order/submit"
    data = {
        "id": 0,
        "name": "紫燕夫妻肺片套餐",
        "description": "",
        "avatar": "https://www.letuo.club/meal/meal/36_896c0708258c4aefacedaee94d93361a.jpeg",
        "price": 3000,
        "teamId": 11,
        "shopId": 7,
        "createrId": 0,
        "createrName": "",
        "productId": 16,
        "productName": "紫燕夫妻肺片套餐",
        "eventId": eventId,
        "num": 1,
        "status": 0,
        "imgs": ""
    }
    headers = {
        "Content-Type": "application/json",
        "Cookie": f"JSESSIONID={jsessionid}"
    }
    try:
        response = requests.post(url, json=data, headers=headers)
        if response.status_code == 200:
            print(response.json())
        else:
            print(f"请求失败，状态码: {response.status_code}")
    except requests.RequestException as e:
        print(f"请求发生错误: {e}")

if __name__ == "__main__":
    # 获取 JSESSIONID
    jsessionid = get_jsessionid()
    if jsessionid:
        # 获取 eventId
        eventId = get_eventid(jsessionid)
        # 提交订餐请求
        submit_order(jsessionid, eventId)