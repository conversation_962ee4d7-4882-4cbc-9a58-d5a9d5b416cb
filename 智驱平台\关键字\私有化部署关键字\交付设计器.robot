*** Settings ***
Library           SeleniumLibrary
Resource          ../../元素/开发平台元素.robot
Resource          ../../元素/Athena平台元素.robot
Resource          ../系统关键字/web.robot
Resource          ../系统关键字/公共方法.robot

*** Keywords ***
交付设计器首页加载
    当前页面可见   //app-unit-card/div[@class='unit-card-container jituan']  3
    
打开导入数据开窗
    元素存在则点击  //span[contains(text(),'导入数据')]
    当前页面可见  //div[@class='ant-modal-title']  3

关闭导入数据开窗
    元素存在则点击  //span[@class='ant-modal-close-x']
    当前页面不可见元素  /div[@class='ant-modal-title']