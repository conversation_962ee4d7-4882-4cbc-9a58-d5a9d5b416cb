*** Settings ***
Documentation     高伟
Resource          ../关键字/业务关键字/作业授权.robot
Resource          ../关键字/业务关键字/基础数据录入.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/公共方法.robot
Resource          ../关键字/控件关键字/表格筛选.robot
Test Setup        Run Keywords    环境设定
Test Teardown     Run Keywords    错误处理    AND    关闭浏览器


*** Test Cases ***
新增单档多栏作业，导出数据，删除作业
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称    单档多栏的作业    多类作业功能测试
    输入-单档多栏作业字段    101    1    2024/08/15    5000
    点击保存作业
    点击数据导出
    判断数据导出状态
    点击删除作业    101


新增单档作业，导出数据，删除作业
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称    单档的作业    多类作业功能测试
    输入-单档作业字段    201    明德实验学校    南京市江宁区    20240815
    点击-新建
    切换作业浏览页Tab    单档的作业
    点击数据导出
    判断数据导出状态
    点击删除作业    201


新增双档作业，导出数据，删除作业
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称    双档的作业    多类作业功能测试
    输入-双档作业字段    301    李白    401    测试    10000
    切换作业浏览页Tab    双档的作业
    点击数据导出
    判断数据导出状态
    点击删除作业    301


新增多档数据，导出数据，删除作业
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称    多档的作业    多类作业功能测试
    新增多档单头数据    01    华彩    江苏南京
    新增多档单身数据    配件类    1000    520    20241127
    点击-新建
    切换作业浏览页Tab    多档的作业
    点击数据导出
    判断数据导出状态
    点击删除作业    华彩

作业列表查询作业
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    基础数据录入作业列表页，根据作业名称查询    双档的作业    UIAutoTest    员工姓名


#整单操作--多档
多档数据,整单操作-新增-复制-编辑
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称    多档的作业    多类作业功能测试
    新增多档单头数据    01    华彩    江苏南京
    新增多档单身数据    配件类    1000    520    20241127
    点击-新建
    点击新增(整单操作)
    输入单头-多档（整单操作）    02    华彩    江苏南京
    输入单身-多档（整单操作）    零件类    1000    520    20241127
    点击-新建
    浏览页删除全部数据    多档的作业
    关闭维护页签
    新增多档单头数据    01    华彩    江苏南京
    新增多档单身数据    配件类    1000    520    20241127
    点击-新建
    点击复制(整单操作)    请输入供应商编号
    点击-新建
    浏览页删除全部数据    多档的作业
    关闭维护页签
    新增多档单头数据    01    华彩    江苏南京
    新增多档单身数据    配件类    1000    520    20241127
    点击-新建
    点击编辑(整单操作)    请输入供应商名称    南京供应商
#    删除作业-多档
    浏览页删除全部数据    多档的作业



#整单操作--双档
双档数据,整单操作-新增-复制-编辑
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称    双档的作业    多类作业功能测试
    输入-双档作业字段    301    李白    401    测试    10000
    点击新增(整单操作)
    输入-双档(整单操作)    302    李白    401    测试    10001
    浏览页删除全部数据    双档的作业
    关闭维护页签
    输入-双档作业字段    301    李白    401    测试    10000
    点击复制(整单操作)    请输入员工ID
    点击-新建
    浏览页删除全部数据    双档的作业
    关闭维护页签
    输入-双档作业字段    301    李白    401    测试    10000
    点击编辑(整单操作)    请输入员工姓名    李明
    浏览页删除全部数据    双档的作业


#按钮标准化需求覆盖自动化#46821
单档多栏作业数据状态变更
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称    按钮标准化单档多栏作业    小AI-五要素基线用例专用-勿动
    前端筛选  test_input     按钮标准化需求测试
    生效/失效单档多栏作业数据   生效
    生效/失效单档多栏作业数据   失效
    重新生效/取消失效单档多栏作业数据  重新生效
    重新生效/取消失效单档多栏作业数据  取消生效

多档作业子子表新增行时报错子表未选中
    [Tags]    多档作业子子表新增行时报错子表未选中
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称    员工信息维护    人事管理通
    点击员工信息维护作业新增按钮
    点击员工信息维护作业详情子表新增行并在子表第一个单元格输入内容    1111
    点击子子表新增行并校验是否正常新增行    1111

