*** Settings ***
Library           SeleniumLibrary
Resource          ../../元素/Athena平台元素.robot
Resource          ../../配置/Athena平台.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/web.robot

*** Keywords ***
rescode与code一致
    [Arguments]    ${work}
    查询需要的作业    ${work}
    Sleep    10
    #    ${id}    Get Element Attribute    //body[@class="ath-default-theme"]/iframe    id
    #    Select Frame    ${id}
    Iframe选择    //iframe[contains(@src,'tbb')]
    Sleep    5
    点击    //div[@title="项目"]
    点击    //div[@class="athena-global"]/div[7]/div[2]/div/div[4]/div/dl/dd/div/div/ul/li[2]
    Unselect Frame
    点击    //div[@class='ant-tabs-tab ant-tabs-tab-active ng-star-inserted']//button

rescode为空
    [Arguments]    ${work}
    Reload Page
    查询需要的作业    ${work}
    Sleep    30
    #    ${id}    Get Element Attribute    //body[@class="ath-default-theme"]/iframe    id
    #    Select Frame    ${id}
    Iframe选择    //iframe[contains(@src,'tbb')]
    点击    //div[@title="项目"]
    Sleep    5
    点击    //i[@class='a-ico ico-glob-filter-refresh']
    Should Contain    //p[contains(text(),'重置成功')]    重置成功
    Sleep    5
    Unselect Frame
    点击    //div[@class='ant-tabs-tab ant-tabs-tab-active ng-star-inserted']//button

查询需要的作业
    [Arguments]    ${work}
    Sleep    5
    js点击    //ath-toolbar-tree-search[@class='ath-toolbar-tree-search']//i
    输入    //ath-toolbar-tree-search[@class='ath-toolbar-tree-search']//input    ${work}
    Sleep    5
    当前页面可见    //div[@class='cdk-overlay-pane']//span[contains(text(),'${work}')]
    点击    //div[@class='cdk-overlay-pane']//span[contains(text(),'${work}')]

工单任务追踪查询
    [Arguments]
    点击    //span[@class='bi-menu-item-text'][contains(text(),'查詢')]
    输入   //input[@placeholder='ECN確認日(起)']    2024/06/01
    点击    //span[@class='ng-star-inserted'][contains(text(),'查詢')]
    Iframe选择    //iframe[contains(@src,'digiwinabi-test')]
    当前页面可见字符    2000-202409110002


TBB报表内容展示
#    全屏取消全屏
    添加常用取消常用
    比对总览表内容展示是否正确    项目进度总览图

全屏取消全屏
    js点击    //div[@class='fullscreen-btn ng-star-inserted']
    js点击    //div[@class='exit-fullscreen ng-star-inserted']

添加常用取消常用
    Comment    点击    //div[@class='is-common-use']//*[@class='iconfont']
    Comment    点击    //*[@class='iconfont on']
    Comment    点击    //span[contains(text(),'确定')]
    Comment    Sleep    30
    ${visual}     判断元素是否可见    //div[@class='is-common-use checked']    20
    Run Keyword If    '${visual}'=='True'    Run Keywords    点击    //div[@class='is-common-use checked']
    ...    AND    点击    //span[contains(text(),'确定')]
    ...    AND    当前页面可见    //div[@class='is-common-use']
    Run Keyword If    '${visual}'=='False'    Run Keywords    点击    //div[@class='is-common-use']
    ...    AND    当前页面可见    //div[@class='is-common-use checked']    5

比对总览表内容展示是否正确
    [Arguments]    ${name}
    #    ${id}    Get Element Attribute    //body[@class="ath-default-theme"]/iframe    id
    #    Select Frame    ${id}
    Iframe选择    //iframe[contains(@src,'tbb')]
    ${compare}    获取元素字符    //span[contains(text(),'${name}')]
    Log    $(compare)
    Should Be Equal    ${compare}    ${name}
    Unselect Frame

关闭报表标签
    [Arguments]    ${close}
    ${close}
