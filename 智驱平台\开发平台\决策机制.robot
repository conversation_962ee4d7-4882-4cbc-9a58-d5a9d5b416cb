*** Settings ***
Suite Setup       Run Keywords    环境设定
Suite Teardown    Run Keywords    关闭浏览器
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/交付设计器.robot
Resource          ../关键字/业务关键字/开发平台.robot
Resource          ../关键字/业务关键字/公共方法.robot

*** Test Cases ***
新增共享范式
    [Documentation]    陈金明
    #需求范式发版41220
    #新增共享范式-新增机制-机制原理-机制能力-发布机制-生效机制-发起项目-检查机制能力是否生效
    ${username}    Set Variable If    '${ENV}'=='paas'    ${username}    '${ENV}'=='huawei.test'    ${username}    '${ENV}'=='huawei.prod'    ${username}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${password}    Set Variable If    '${ENV}'=='paas'    ${password}    '${ENV}'=='huawei.test'    ${password}    '${ENV}'=='huawei.prod'    ${password}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${application}    Set Variable If    '${ENV}'=='paas'    质量测试专用应用    '${ENV}'=='huawei.test'    采购管理88CN    '${ENV}'=='huawei.prod'    采购管理88CN
    ${appCode}    Set Variable If    '${ENV}'=='paas'    qctest001    '${ENV}'=='huawei.test'    purchase88CN    '${ENV}'=='huawei.prod'    purchase88CN
    ${tenant}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    智驱中台工作台    '${ENV}'=='huawei.prod'    智驱中台工作台
    ${tenantId}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    IntelligentDriveCenterWorkbench    '${ENV}'=='huawei.prod'    IntelligentDriveCenterWorkbench
    ${person_value}    Set Variable If    '${ENV}'=='paas'    测试职能    '${ENV}'=='huawei.test'    测试职能    '${ENV}'=='huawei.prod'    测试职能
    ${deploy_to_env}    Set Variable If    '${ENV}'=='paas'    大陆正式区（阿里）    '${ENV}'=='huawei.test'    大陆测试区    '${ENV}'=='huawei.prod'    大陆正式区
    ${token}    Set Variable If    '${ENV}'=='paas'    fb6f63fb-e9a0-4968-95ea-76e9481c29c3    '${ENV}'=='huawei.test'    1eee320f-c9f3-4f26-b827-7baa72d72ba8    '${ENV}'=='huawei.prod'    661c4af4-2b02-4c7a-9d47-e3b9836236c1
    Set Global Variable    ${topic}    自动化测试议题
    Set Global Variable    ${token}
    Set Global Variable    ${tenantId}
    #注释：共享范式新建
    #注释：范式名称和机制名称定义
    ${time}    生成秒时间戳
    Set Global Variable    ${paradigmName}    ${time}
    Set Global Variable    ${mechanismName}    ${time}
    登录开发平台    ${username}    ${password}    ${tenant}
   
    go to    https://athena-dev-platform.digiwincloud.com.cn/app/km/mechanism-set/design?appCode=purchase88CN&mechanismCode=ud_m_9ea2a24530b34c89bcbc796364e75080
    Sleep    20
    按量完成    任务    采购管理_项目_0001    采购申请提交    待处理    数量    等于    固定值    自定义    1111
    Sleep    1000
