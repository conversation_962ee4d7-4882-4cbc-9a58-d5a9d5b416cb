*** Settings ***
Documentation     沈飞飞
Suite Setup       Run Keywords    环境设定
Suite Teardown    Run Keywords    关闭浏览器
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/业务关键字/Athena平台控件.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/开发平台.robot
Resource          ../关键字/业务关键字/dtd_ttt融合项目.robot
Resource          ../关键字/业务关键字/PCC.robot


*** Test Cases ***
任务卡提交/签核同意/退回重签/向前/向后加签/退回重办/签核不同意/任务转派/流程终止
    #提交--签核同意--退回重签--向前加签---向后加签---退回重办--任务转派--流程终止---结束
    [Documentation]    沈飞飞
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01       '${ENV}'=='huawei.test'   TestAthenaAutoTestAi001
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01       '${ENV}'=='huawei.test'   TestAthenaAutoTestAi001
    ${projectCode}      Set Variable If    '${ENV}'=='huawei.prod'    PU_a96c302100025c6             '${ENV}'=='huawei.test'   'PU_a96c302100025c6'

    ${tenantId}               Set Variable If     '${ENV}'=='huawei.prod'    AthenaAutoProdHw                            '${ENV}'=='huawei.test'    AthenaAutoTest
    ${signForwardPersonnel}   Set Variable If     '${ENV}'=='huawei.prod'    15722677434生产华为环境自动化测试智驱入口         '${ENV}'=='huawei.test'    AthenaAutoTest
    ${signBackwardPersonnel}  Set Variable If     '${ENV}'=='huawei.prod'    15722677434生产华为环境自动化测试智驱入口         '${ENV}'=='huawei.test'    AthenaAutoTest
    ${transferPersonnel}      Set Variable If     '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02生产华为环境自动化测试小AI入口         '${ENV}'=='huawei.test'    AthenaAutoTest

    Set Global Variable    ${tenantId}
    Set Global Variable    ${story_id}      ${长唯一标识}
    Set Global Variable    ${story_name}    ${长唯一标识}
    Set Global Variable    ${content}       ${长唯一标识}

    #设定token
    ${token}    Set Variable If       '${ENV}'=='huawei.prod'    615e5f00-765c-4087-9ca0-107909bd74a2   '${ENV}'=='huawei.test'    46f9f7a8-6318-4568-9772-72ec61395d82
    Set Global Variable    ${token}

    # 接口发起项目
    ${data}    Set Variable    {"projectCode":"${projectCode}","process_EOC":{},"variables":{"classification_mode":"1","from_inquiry":"false","is_inquiry":"false","perfromId":"<EMAIL>"},"dispatchData":[{"story_id":"${story_id}","story_name":"${story_name}","time":"","executor":"<EMAIL>","content":"${content}","manage_status":"0"}]}
    发起项目    ${data}
    登录Athena平台    ${ownerUsername}    ${ownerPassword}
    语言设定    简体
    融合任务卡提交       ${story_id}
    融合任务卡同意       ${story_id}

    #退回重签
    退回重签            ${story_id}
    融合任务卡同意       ${story_id}
    #向前加签
    向前加签     ${story_id}     ${signForwardPersonnel}
    关闭浏览器

    #签核同意
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02
    登录Athena平台    ${ownerUsername}    ${ownerPassword}
    语言设定    简体
    融合任务卡同意       ${story_id}
    关闭浏览器

    #向后加签
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01
    登录Athena平台    ${ownerUsername}    ${ownerPassword}
    语言设定    简体
    加签     ${story_id}    ${signBackwardPersonnel}
    关闭浏览器

    #加签人同意
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02
    登录Athena平台    ${ownerUsername}    ${ownerPassword}
    语言设定    简体
    融合任务卡同意       ${story_id}
    关闭浏览器

    #任务卡退回重办
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01
    登录Athena平台    ${ownerUsername}    ${ownerPassword}
    语言设定    简体
    退回重办    ${story_id}      #退回重办配置成外层按钮？?????

    #退回节点dtd提交
    融合任务卡提交     ${story_id}
    #TTT第一张签核卡同意
    融合任务卡同意     ${story_id}
    #TTT第二张签核卡同意
    融合任务卡同意     ${story_id}
    #签核不同意退回到TTT
    签核不同意        ${story_id}
    #TTT第一张签核卡转派
    任务转派    ${story_id}    ${transferPersonnel}
    关闭浏览器

    #TTT第一张签核卡同意
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02
    登录Athena平台    ${ownerUsername}    ${ownerPassword}
    语言设定    简体
    融合任务卡同意     ${story_id}
    关闭浏览器
    #TTT第二张签核卡同意/ 接下来终止
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01
    登录Athena平台    ${ownerUsername}    ${ownerPassword}
    语言设定    简体
    融合任务卡同意     ${story_id}

    #融合项目卡里程碑检查
    融合项目详情里程碑切换    ${story_id}    <EMAIL>

    #融合项目卡添加至他人Athena
    项目卡添加至他人Athena   ${story_id}    生产华为环境自动化测试小AI入口
    关闭浏览器

    #项目卡被分享人检查卡片数据
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02
    登录Athena平台    ${ownerUsername}    ${ownerPassword}
    语言设定    简体
    融合项目被分享卡    ${story_id}    <EMAIL>
    关闭浏览器

     #-签核任务卡终止
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01
    登录Athena平台    ${ownerUsername}    ${ownerPassword}
    语言设定    简体
    融合任务卡终止     ${story_id}
    关闭浏览器
