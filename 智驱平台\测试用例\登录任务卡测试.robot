*** Settings ***
Documentation     登录并打开任务卡测试用例
Library           SeleniumLibrary
Test Setup        Run Keywords    设置测试环境
Test Teardown     Close All Browsers

*** Variables ***
${TEST_URL}       https://athena-test.digiwincloud.com.cn
${USERNAME}       qcuser001
${PASSWORD}       qcuser001
${browser}        gc

*** Test Cases ***
登录并打开任务卡
    [Documentation]    测试登录athena-test平台并打开任务卡功能
    [Tags]    登录    任务卡

    # 登录系统
    登录测试环境    ${USERNAME}    ${PASSWORD}

    # 验证登录成功
    Wait Until Element Is Visible    //i[@class='icon font-entrance icongongsi']    30s

    # 打开任务卡页面
    打开任务卡页面

    # 验证任务卡页面加载成功
    验证任务卡页面

*** Keywords ***
设置测试环境
    [Documentation]    设置测试环境变量

    # 设置全局变量
    Set Global Variable    ${browser}    gc
    Set Global Variable    ${timeout}    240
    Set Global Variable    ${ATHENA_ENV}    ${TEST_URL}

登录测试环境
    [Arguments]    ${username}    ${password}
    [Documentation]    登录到测试环境

    # 打开测试环境网页
    Open Browser    ${TEST_URL}    Chrome
    Maximize Browser Window

    # 等待登录页面加载
    Wait Until Element Is Visible    //input[@name="userId"]    30s

    # 输入用户名和密码
    Input Text    //input[@name="userId"]    ${username}
    Input Text    //input[@name="password"]    ${password}

    # 点击登录按钮
    Click Element    //div[@class="action"]/button/span

    # 等待首页加载完成
    Wait Until Element Is Visible    //i[@class='icon font-entrance icongongsi']    30s

    # 关闭可能出现的提示弹窗
    ${element_exists}=    Run Keyword And Return Status    Wait Until Element Is Visible    //span[contains(text(),'我知道了')]    5s
    Run Keyword If    ${element_exists}    Click Element    //span[contains(text(),'我知道了')]

    # 设置语言为简体中文（如果需要）
    Sleep    2s

打开任务卡页面
    [Documentation]    打开任务卡页面

    # 跳转到任务卡页面
    Go To    ${TEST_URL}/todo/task
    Sleep    5s

    # 点击我的任务
    Wait Until Element Is Visible    //div[contains(text(),'我的任务')]    30s
    Click Element    //div[contains(text(),'我的任务')]
    Sleep    3s

验证任务卡页面
    [Documentation]    验证任务卡页面是否正确加载

    # 验证页面标题或关键元素
    Wait Until Element Is Visible    //div[contains(text(),'我的任务')]    30s

    # 验证任务卡列表区域存在
    Wait Until Element Is Visible    //div[@class='todo-content']    30s

    Log    任务卡页面加载成功
