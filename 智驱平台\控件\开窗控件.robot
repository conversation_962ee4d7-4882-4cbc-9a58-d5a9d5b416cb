*** Settings ***
Documentation     郑苏振
Resource          ../关键字/业务关键字/业务数据录入.robot
Library           SeleniumLibrary
Resource          ../元素/Athena平台元素.robot
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/业务关键字/智能入口.robot
Resource          ../关键字/业务关键字/首页.robot
Resource          ../关键字/业务关键字/公共方法.robot
Resource          ../关键字/业务关键字/作业授权.robot
Resource          ../关键字/业务关键字/业务数据录入.robot
Resource          ../配置/全局参数.robot
Resource          ../关键字/控件关键字/表格筛选.robot
Resource          ../关键字/控件关键字/表格分页.robot
Resource          ../关键字/控件关键字/表格分组.robot
Resource          ../关键字/控件关键字/表格工具栏操作.robot
Resource          ../关键字/控件关键字/表格行.robot
Resource          ../关键字/控件关键字/表格列.robot
Resource          ../关键字/控件关键字/表格排序.robot
Resource          ../关键字/控件关键字/单元格.robot
Resource          ../关键字/控件关键字/控件公共关键字.robot
Test Setup        Run Keywords    环境设定
Test Teardown     Close Browser

*** Test Cases ***
表格单选开窗
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='muihuawei.test'   TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='muihuawei.test'   TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    表格_开窗作业测试
    Sleep   10
    点击新增行  
    快捷输入开窗结果为空    ceshi   dynamic-operation-editor
    快捷输入开窗    202501    dynamic-operation-editor
    submit保存
    Sleep   3
    submit删除
    点击新增行
    打开单选开窗
    搜索开窗数据    202503
    选择单选开窗数据    202503
    回显数据检查    202503
    清除回显数据    202503    dynamic-operation-editor
    打开单选开窗
    开窗快捷入口

表格多选开窗
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='muihuawei.test'   TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001   #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='muihuawei.test'   TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001   #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    表格_多选开窗作业测试
    Sleep   10
    点击新增行
    点击单元格字段      case_tid
    输入单元格字段值    202510010   case_tid
    快捷输入开窗结果为空    ceshi   dynamic-form-operation-editor
    快捷输入开窗   1008611   dynamic-form-operation-editor
    submit保存
    Sleep   3
    submit删除
    点击新增行
    打开多选开窗窗口    case_open_windowa
    搜索开窗数据    1008611
    选择多选开窗数据    1008611   tableow
    Sleep   3
    多选开窗回显数据检查    1008611   case_open_windowa
    清除回显数据  1008611  dynamic-form-operation-editor

表单开窗
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='muihuawei.test'   TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='muihuawei.test'   TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    表单_开窗作业测试
    Sleep   10
    #表单单选开窗
    点击新增打开子页签
    快捷输入开窗结果为空  ceshi  dynamic-operation-editor
    快捷输入开窗  202503  dynamic-operation-editor
    清除回显数据  202503  dynamic-operation-editor
    打开表单开窗  dynamic-operation-editor
    搜索开窗数据  202503
    选择单选开窗数据  202503
    表单开窗回显数据检查  202503
    清除回显数据  202503  dynamic-operation-editor
    单选开窗默认选中第一条
    #表单多选开窗
    快捷输入开窗结果为空  null  formlist-operation-editor
    快捷输入开窗  1008610  formlist-operation-editor
    清除表单开窗回显数据  1008610  dynamic-form-operation-editor
    打开表单开窗  dynamic-form-operation-editor
    搜索开窗数据  1008610
    选择多选开窗数据  1008610   formlistow
    清除表单开窗回显数据  1008610  dynamic-form-operation-editor

单选开窗场景
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='muihuawei.test'   TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='muihuawei.test'   TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    打开发起项目录入界面  card_M9d63_b4e2_mainline_project_0004
    单选开窗根据输入关键字展示内容  001
    关闭开窗窗口
    单选开窗根据输入关键字展示内容  null
    关闭开窗窗口
    单选开窗根据输入关键字展示内容  测试
    选择condition单选开窗数据  测试
    清除condition开窗数据  测试
    单选开窗功能属性检查
    开窗查询条件筛选  供应商手机号  等于  183
    开窗查询条件筛选  供应商名称-供应商编号  包含  5509
    开窗查询条件筛选  全部   开头   中国移动
    关闭开窗窗口

多选新增行
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='muihuawei.test'   TestAthenaAutoTestAi001     '${ENV}'=='paas'    TestAthenaAutoTestAi001   #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='muihuawei.test'   TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001   #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    打开发起项目录入界面  card_M9d63_b4e2_mainline_project_0001
    打开非输入型开窗    product_no
    选择多选开窗数据    A001   rowow


多选开窗场景
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='muihuawei.test'   TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='muihuawei.test'   TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    打开发起项目录入界面  card_M9d63_b4e2_mainline_project_0001
    多选开窗根据输入关键字展示内容   A0005
    关闭开窗窗口
    多选开窗根据输入关键字展示内容   null
    关闭开窗窗口
    多选开窗根据输入关键字展示内容   A0010
    关闭开窗窗口
    清除condition开窗数据  A0010
    打开非输入型开窗    product_no
    多选开窗功能属性检查
    开窗查询条件筛选  产品数量  等于  10
    关闭开窗窗口

开窗规则校验
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='muihuawei.test'   TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001    #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001   '${ENV}'=='muihuawei.test'   TestAthenaAutoTestAi001   '${ENV}'=='paas'    TestAthenaAutoTestAi001   #'${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    # Sleep   10
    # 关闭娜娜窗口
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    开窗规则校验
    Sleep   10
    点击新增行
    开窗必填项校验
    开窗最大/最小长度校验
    开窗最大/最小值规则校验
    开窗重复性校验
    开窗样式color校验
    开窗样式disabled校验
    关闭当前基础资料
    开窗默认值规则校验
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    开窗赋值规则校验作业测试
    Sleep   5
    开窗赋值、联动规则校验
    关闭当前基础资料


