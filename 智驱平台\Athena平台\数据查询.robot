*** Settings ***
Suite Setup    Run Keywords    环境设定
Test Teardown    Run Keywords    关闭浏览器    AND    错误处理
Resource          ../关键字/业务关键字/数据查询.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/2.0应用功能.robot

*** Variables ***
${SUITE_VAR1}    value1

*** Test Cases ***
进度查询
    [Documentation]    陈金明
    ...    目前只在生产执行
    Log    ${SUITE_VAR1}
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${owner}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    生产华为环境自动化测试KM    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试小AI平台
    ${intervalDay}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    08-12~08-12    '${ENV}'=='microsoft.prod'    10-09~10-09
    ${projectNameDay}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    计划时程异常2024072404[自动化测试项目名称]    '${ENV}'=='microsoft.prod'    计划时程异常2024100807[自动化测试项目卡1728363983]
    ${ownerDay}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    生产华为环境自动化测试KM    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试小AI平台
    ${intervalWeek}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    09-02~09-08    '${ENV}'=='microsoft.prod'    10-07~10-13
    ${projectNameWeek}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    计划时程异常2024072404[自动化测试项目名称]    '${ENV}'=='microsoft.prod'    计划时程异常2024100807[自动化测试项目卡1728363983]
    ${ownerWeek}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    生产华为环境自动化测试KM    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试小AI平台
    ${intervalQuarter}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    07-01~09-30    '${ENV}'=='microsoft.prod'    10-01~12-31
    ${projectNameQuarter}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    计划时程异常2024060400000000001[cjm测试001]    '${ENV}'=='microsoft.prod'    计划时程异常2024100807[自动化测试项目卡1728363983]
    ${ownerQuarter}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    生产华为环境自动化测试KM    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试小AI平台
    登录Athena平台    ${ownerUsername}    ${ownerPassword}
    点击顶部菜单    全部
    点击右侧菜单    数据查询
    进度查询    天    项目中控台计划时程异常追踪    ${owner}
    进度查询数据校验    项目中控台计划变更    ${intervalDay}    ${projectNameDay}    ${ownerDay}
    进度查询    周    项目中控台计划时程异常追踪    ${owner}
    进度查询数据校验    项目中控台计划变更    ${intervalWeek}    ${projectNameWeek}    ${ownerWeek}
    #按月查询存在bug,暂时注释
    #进度查询    月    项目中控台计划时程异常追踪    ${owner}
    #进度查询数据校验    项目中控台计划变更    09-01~09-30    计划时程异常2024060400000000001[cjm测试001]    生产华为环境自动化测试KM
    进度查询    季度    项目中控台计划时程异常追踪    ${owner}
    进度查询数据校验    项目中控台计划变更    ${intervalQuarter}    ${projectNameQuarter}    ${ownerQuarter}
    [Teardown]    Run Keywords    关闭浏览器

历史项目/任务
    [Documentation]    陈金明
    ...    目前只在生产执行
    ${username}    Set Variable    HL18271405997
    ${password}    Set Variable    HuangL0920
    #${info}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    2024/04/23 - 04/23 行事历    '${ENV}'=='microsoft.prod'    2024/05/07 - 05/07 行事历
    ${purchase_no}    生成毫秒时间戳
    登录Athena平台    ${username}    ${password}
    手动发起项目2.0    cjm${purchase_no}    10
    任务卡提交    cjm${purchase_no}
    ${purchase_no}    生成毫秒时间戳
    登录Athena平台    ${username}    ${password}
    手动发起项目2.0    cjm${purchase_no}    10
    任务卡提交    cjm${purchase_no}
    点击顶部菜单    全部
    点击右侧菜单    数据查询
    #重置排序
    ${current_date}    Get Current Date    result_format=%Y/%m/%d
    Wait Until Keyword Succeeds    2x    3s    按输入历史项目和任务查询条件查询    ${purchase_no}    project_name=2.0应用功能测试    create_time=${current_date}    end_time=${current_date}    card_type=全部    status=准时    project_source=系统产生    project_operation=提交
    历史项目/任务查询数据校验    项目    ${purchase_no}    采购单确认    2.0应用功能测试    准时
#    js点击    //button[contains(@class,'ath-search-single-input-icon-btn')]
#    点击    //span[contains(text(),'筛选')]
    Clear Element Text    //input[@placeholder='请输入关键字搜索']
    js点击    //button[contains(@class,'ath-search-single-input-icon-btn')]
    排序    卡片类型    完成时间    降序
    表格设定-隐藏列    卡片类型
    表格设定-显示列    卡片类型
    数据导出
    #项目数据跳转校验    前置任务
