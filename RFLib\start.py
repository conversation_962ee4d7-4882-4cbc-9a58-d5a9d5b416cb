import time
import subprocess
import datetime

# 设定开始时间和结束时间
start_time = datetime.datetime.now().replace(hour=15, minute=59, second=40, microsecond=0)
end_time = datetime.datetime.now().replace(hour=16, minute=0, second=30, microsecond=0)

# 如果开始时间已经过去，则设置为明天的同一时间
if start_time < datetime.datetime.now():
    start_time = start_time + datetime.timedelta(days=1)
    end_time = end_time + datetime.timedelta(days=1)

# 计算距离开始时间的时间差
time_difference = (start_time - datetime.datetime.now()).total_seconds()

# 等待到开始时间
if time_difference > 0:
    print(f"等待 {time_difference} 秒到开始时间...")
    time.sleep(time_difference)

print("开始执行脚本...")
try:
    while datetime.datetime.now() < end_time:
        # 这里替换为你要执行的 Python 脚本的文件名
        subprocess.run(['python', '/Users/<USER>/Desktop/case/ui-case/RFLib/order.py'])
        time.sleep(0.5)
    print("执行时间已到，结束执行。")
except KeyboardInterrupt:
    print("执行被手动中断。")