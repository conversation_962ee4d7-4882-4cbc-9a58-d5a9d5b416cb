*** Settings ***
Library    SeleniumLibrary
Library    DatabaseLibrary
Library    String
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/系统关键字/接口.robot
Resource          ../关键字/系统关键字/mysql.robot
Resource          ../配置/人员手机号码.robot
Resource          ../配置/DB.robot


*** Variables ***
${BASE_URL}        http://172.16.2.154:9989/view/
${WEBHOOK_URL}     qyapi.weixin.qq.com
${WEBHOOK_KEY}     8d14b45b-29a2-421a-b709-2b411151c18a
${BROWSER_OPTIONS}    add_argument('--lang=zh-CN');add_argument('--no-sandbox');add_argument('--disable-gpu');add_argument('--disable-dev-shm-usage')

*** Keywords ***
检查项目状态并通知
    [Arguments]    ${view_path}
    Open Browser    ${BASE_URL}${view_path}    headlesschrome    options=${BROWSER_OPTIONS}
    ${ele_counts}    获取元素数量    //table[@id='projectstatus']//tbody//tr
    ${sum}    Evaluate    ${ele_counts} + 1
    FOR    ${index}    IN RANGE    1    ${sum}
        ${job}    获取元素属性值    //table[@id='projectstatus']//tbody/tr[${index}]    id
        ${status}    获取元素属性值    //table[@id='projectstatus']//tbody/tr[${index}]    class
        ${user}    获取元素字符    //table[@id='projectstatus']//tbody/tr[${index}]/td[9]
        @{item}    Split String     ${user}    separator=/
        FOR    ${user}    IN    @{item}
            Log    ${user} # 依次输出 a, b, c, d
            Run Keyword If    '${status}' not in [' job-status-blue', ' job-status-blue-anime']    发送企业微信通知    ${user}    ${job}
        END
    END
    Close Browser

发送企业微信通知
    [Arguments]    ${user}    ${job}
    ${mobile_list}    Set Variable If
    ...    '${手机号码}[${user}]' != 'None'    ${手机号码}[${user}]
    ...    []  # 处理未找到用户的情况
    ${data}    Set Variable    {"msgtype":"text","text":{"content":"${user}，你好，你的UI自动化用例：【${job}】今日执行失败未处理，请及时处理，如请假请代理人代为处理！！！","mentioned_mobile_list":${mobile_list}}}
    POST-UI    ${WEBHOOK_URL}    cgi-bin/webhook/send?key=${WEBHOOK_KEY}    ${data}

*** Test Cases ***
主测试流程
    连接数据库    ${MSDB}
    检查项目状态并通知    01预生产和生产冒烟回归/
    检查项目状态并通知    02测试环境冒烟必测/
    检查项目状态并通知    05MeterSphere/
    Log    测试