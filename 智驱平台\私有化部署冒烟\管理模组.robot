*** Settings ***
Suite Setup       Run Keywords    环境设定
Suite Teardown    Run Keywords    关闭浏览器
Library           SeleniumLibrary
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/业务关键字/Athena平台控件.robot
Resource          ../关键字/业务关键字/智能入口.robot
Resource          ../关键字/业务关键字/Athena平台.robot

*** Variables ***

*** Test Cases ***
管理模组页面检查
    [Documentation]    顾冬冬
    ${username}    Set Variable If    '${ENV}'=='private.test'    default
    ${password}    Set Variable If    '${ENV}'=='private.test'    1qaz@WSX
    登录Athena平台    ${username}    ${password}
    点击顶部菜单    全部
    点击右侧菜单    管理模组
    sleep    3
    选择浏览器窗体    -1
    当前页面若文本不存在自动刷新页面    3    管理模组
    点击    //span[contains(text(),'事件记录簿')]
    当前页面包含元素    xpath=//*[contains(text(),'资讯')]
    当前页面包含元素    xpath=//*[contains(text(),'警告')]
    当前页面包含元素    xpath=//*[contains(text(),'错误')]
    点击    //span[contains(text(),'运营单元代码一览表')]
    当前页面包含元素    xpath=//*[contains(text(),'公司/工厂')]
    当前页面包含元素    xpath=//*[contains(text(),'场域')]
    点击    //span[contains(text(),'伙伴授权管理')]
    当前页面包含元素    xpath=//*[contains(text(),'同意伙伴为我创建任务')]
    当前页面包含元素    xpath=//*[contains(text(),'同意伙伴调用我的服务')]
    点击    //span[contains(text(),'需辅助名单管理')]
    当前页面包含元素    xpath=//*[contains(text(),'企业名称')]
    当前页面包含元素    xpath=//*[contains(text(),'企业ID')]
    点击    //span[contains(text(),'传送方式管理')]
    当前页面包含元素    xpath=//*[contains(text(),'邮件')]
    当前页面包含元素    xpath=//*[contains(text(),'IM')]
    点击    //span[contains(text(),'数据来源设定')]
    当前页面包含元素    xpath=//*[contains(text(),'调用服务名称')]
    点击    //span[contains(text(),'授权码管理')]
    当前页面包含元素    xpath=//button[contains(text(),'授权')]
    点击    //span[contains(text(),'用户登录卡管理')]
    当前页面包含元素    xpath=//*[contains(text(),'卡片ID')]
