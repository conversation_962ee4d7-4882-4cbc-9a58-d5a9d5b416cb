*** Settings ***
Library           SeleniumLibrary
Resource          ../系统关键字/web.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../系统关键字/接口.robot
Resource          Athena平台控件.robot
Resource          ../../元素/开发平台元素.robot
Resource          ../../配置/域名.robot
Resource          Athena平台.robot
Resource          ../业务关键字/PCC.robot


*** Keywords ***
发起项目
    [Arguments]    ${product_name}   ${price}   ${price_unit}   ${price_idea}
    点击    //*[contains(text(),'产品议价-pilotrun')]
    sleep  2
    输入    //*[@name="product_name"]          ${product_name}           #输入产品名称
    点击    (//div[@class='btn-group-other'])[1]                         #点击采购员编码弹窗
    通用搜索框搜索并勾选    自动化测试采购
#    sleep  2
#    输入    //div[@class='ath-input-content-box ng-star-inserted']//input[@type='text']      ${person_code}   #输入采购员编码
#    点击    (//span[contains(text(),'搜索')])[2]                          #点击搜索
#    sleep  1
#    点击    (//span[@class="ag-cell-value"])[1]                          #点击选中
#    点击    (//span[contains(text(),'提交')])[2]                          #点击提交
#    sleep  2
#    # 输入    //*[@placeholder="采购者信息"]       ${person_code}          #输入采购员编码
    点击    (//div[@class='btn-group'])[2]                               #点击供应商弹窗
    通用搜索框搜索并勾选    自动化测试供应商
#    sleep  1
#    点击    //div[@class="open-window-label ng-star-inserted"]           #点击选择第一个供应商
#    点击    //button[@class="ath-btn ant-btn ant-btn-primary ng-star-inserted active"]  #点击提交
    sleep  2
    输入    //input[@placeholder="产品单价"]     ${price}                 #输入产品单价
    输入    //input[@name="price_unit"]        ${price_unit}            #输入单价单位
    输入    //input[@name="price_idea"]        ${price_idea}            #输入单价意见
    点击    //span[contains(text(),'项目介绍')]                           #只是点击任意元素激活提交按钮
    sleep  1
    点击    //span[contains(text(),'提交')]                              #项目提交
    点击    //span[contains(text(),'确定')]                              #确认提交
    sleep  5

任务卡提交并终止
    [Arguments]    ${product_name}
#    判断后跳转到目标页面    /todo/task
    # 操作【录入产品价格】卡片
    点击    //div[contains(text(),'我的任务')]
#    点击    //span[contains(text(),'搜索')]                             #点击搜素按钮
#    输入    //input[@placeholder='可用空格分隔关键词']    录入产品价格       #输入任务卡名称
#    点击    //span[@class="ant-input-group-addon ng-star-inserted"]    #点击搜索按钮
    任务/项目名称搜索     ${product_name}
    点击    (//span[contains(text(),'单价意见')] )[1]          #如果卡片名称为【录入产品价格】
    点击    //span[contains(text(),'提交')]                            #提交录入产品价格卡片
    点击    //span[contains(text(),'确定')]                            #弹窗确认
    当前页面不可见字符    请稍等

    # 操作【采购方审核价格】卡片
    点击    //li[contains(text(),'待办')]                              #点击待办
#    sleep  1
#    Clear Element Text    xpath=//*[@id="main-content"]/app-todo/app-todo-nav/nav/div[2]/app-todo-search/div/ath-search/div/div/div/ath-input-group/div[2]/section/span/input
#    sleep  1
#    输入    //input[@placeholder='可用空格分隔关键词']    采购方审核        #搜索采购方审核卡片
#    sleep  1
#    点击    //span[@class="ant-input-group-addon ng-star-inserted"]
    任务/项目名称搜索    ${product_name}
    点击卡片    ${product_name}              #点击进入卡片
    点击    //*[@id="task-content-content-id"]//button[1]
    点击    //span[contains(text(),'不同意')]                           #点击不同意
    #点击    //div[@class="ath-input-content-box ng-star-inserted"]
    #点击    (//span[contains(text(),'意见')] )[2]
    输入    //textarea[@id='opinion']     123                                        #输入意见
    点击    //span[contains(text(),'确定')]                            #点击确定
    当前页面不可见字符    请稍等
   # 操作【录入价格意见】
    判断后跳转到目标页面    /todo/task
    任务/项目名称搜索     ${product_name}
    #输入    //input[@placeholder='可用空格分隔关键词']    录入价格意见       #输入任务卡名称
    #js点击    //span[@class='ant-input-group-addon ng-star-inserted']    #点击搜索按钮
    点击    //span[contains(text(),'产品编号')]                          #点击进入卡片
    点击    //span[contains(text(),'终止')]                             #点击终止按钮
    点击    //span[contains(text(),'确定')]                             #二次弹窗确认




















