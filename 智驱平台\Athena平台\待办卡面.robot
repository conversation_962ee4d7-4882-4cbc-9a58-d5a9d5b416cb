*** Settings ***
Documentation     郑苏振
Suite Setup       Run Keywords    环境设定
Test Teardown     Run Keywords    错误处理    关闭浏览器
Resource          ../关键字/业务关键字/智能入口.robot
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/业务关键字/待办.robot
Resource          ../关键字/业务关键字/交付设计器.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/顺序签核.robot

*** Test Cases ***
待办-自定义卡面展示
    ${username}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${password}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    Set Global Variable    ${username}
    Set Global Variable    ${password}
    ${group}    Set Variable If    '${ENV}'=='pressure'    自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    自动化测试环境    '${ENV}'=='huawei.prod'    自动化测试环境    '${ENV}'=='microsoft.prod'    自动化测试环境    '${ENV}'=='muihuawei.test'    自动化测试环境
    ${app}      Set Variable If    '${ENV}'=='pressure'    小AI-五要素基线用例专用-勿动    '${ENV}'=='paas'    小AI-五要素基线用例专用-勿动    '${ENV}'=='huawei.test'    小AI-五要素基线用例专用-勿动    '${ENV}'=='huawei.prod'    小AI-五要素基线用例专用-勿动    '${ENV}'=='microsoft.prod'    小AI-五要素基线用例专用-勿动    '${ENV}'=='muihuawei.test'    小AI-五要素基线用例专用-勿动
    登录Athena平台    ${Username}    ${Password}
    语言设定    简体
    点击顶部菜单    全部
    点击右侧菜单    交付设计器
    选择窗体    交付设计器
    应用设定    ${group}    ${app}
    项目/任务要素设定
    #切换项目要素设定页签  质量检验
    Sleep    3
    打开进阶设定    project     小AI-五要素基线用例专用-勿动_项目_0001
    [Teardown]    Run Keywords    关闭浏览器
