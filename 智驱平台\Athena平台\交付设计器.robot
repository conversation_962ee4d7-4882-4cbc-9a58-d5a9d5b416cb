*** Settings ***
Documentation     张欣雨
Suite Setup       Run Keywords    环境设定
Suite Teardown    关闭浏览器
Resource          ../关键字/业务关键字/交付设计器.robot
Resource          ../关键字/业务关键字/Athena平台.robot

*** Test Cases ***
首页--导入数据
    #账密
#    ${Username}    Set Variable If    '${ENV}'=='pressure'    <EMAIL>        '${ENV}'=='paas'    <EMAIL>    '${ENV}'=='huawei.test'    <EMAIL>    '${ENV}'=='huawei.prod'    <EMAIL>    '${ENV}'=='microsoft.prod'    <EMAIL>
#    ${Password}    Set Variable If    '${ENV}'=='pressure'    06092002zxyC@    '${ENV}'=='paas'    06092002zxyC@    '${ENV}'=='huawei.test'    @cjm820412000    '${ENV}'=='huawei.prod'    @cjm820412000    '${ENV}'=='microsoft.prod'    @cjm820412000
    ${username}    Set Variable If    '${ENV}'=='paas'    HL18271405997    '${ENV}'=='huawei.test'    HL18271405997    '${ENV}'=='huawei.prod'    HL18271405997    '${ENV}'=='microsoft.prod'    HL18271405997    '${ENV}'=='muihuawei.test'    HL18271405997
    ${password}    Set Variable If    '${ENV}'=='paas'    HuangL0920    '${ENV}'=='huawei.test'    HuangL0920    '${ENV}'=='huawei.prod'    HuangL0920    '${ENV}'=='microsoft.prod'    HuangL0920    '${ENV}'=='muihuawei.test'    HuangL0920
    #租户（区分环境）
    ${Athenatenant}    Set Variable If    '${ENV}'=='pressure'    自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    #导入租户/应用/参数/运营单元
    ${tenant}    Set Variable If    '${ENV}'=='pressure'    智驱中台工作台    '${ENV}'=='paas'    智驱中台工作台    '${ENV}'=='huawei.test'    智驱中台工作台    '${ENV}'=='huawei.prod'    智驱中台工作台    '${ENV}'=='microsoft.prod'    智驱中台工作台
    ${application}    Set Variable If    '${ENV}'=='pressure'   【质量组】DTD1.0    '${ENV}'=='paas'    KM-机制场景    '${ENV}'=='huawei.test'    项目中控台    '${ENV}'=='huawei.prod'    项目中控台    '${ENV}'=='microsoft.prod'    项目中控台
    ${parameter}    Set Variable If    '${ENV}'=='pressure'    全部    '${ENV}'=='paas'    全部    '${ENV}'=='huawei.test'    应用参数设定    '${ENV}'=='huawei.prod'    应用参数设定    '${ENV}'=='microsoft.prod'    应用参数设定
    ${unit}    Set Variable If    '${ENV}'=='pressure'    全部    '${ENV}'=='paas'    全部    '${ENV}'=='huawei.test'    全部    '${ENV}'=='huawei.prod'    全部    '${ENV}'=='microsoft.prod'    全部
    #进入对应的运营单元下
    ${group}    Set Variable If    '${ENV}'=='pressure'    自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    ${app}    Set Variable If    '${ENV}'=='pressure'    【质量组】DTD1.0    '${ENV}'=='paas'    KM-机制场景    '${ENV}'=='huawei.test'    应用参数设定测试    '${ENV}'=='huawei.prod'    应用参数设定测试    '${ENV}'=='microsoft.prod'    应用参数设定测试
    登录Athena平台    ${Username}    ${Password}    tenant=${Athenatenant}
    点击顶部菜单    全部
    点击右侧菜单    交付设计器
    选择浏览器窗体    -1
    导入数据    ${tenant}    ${application}    ${parameter}    ${unit}    ${group}
    [Teardown]    Run Keywords    关闭浏览器

首页--导入数据-经典门户
    #账密
#    ${Username}    Set Variable If    '${ENV}'=='pressure'    <EMAIL>        '${ENV}'=='paas'    <EMAIL>    '${ENV}'=='huawei.test'    <EMAIL>    '${ENV}'=='huawei.prod'    <EMAIL>    '${ENV}'=='microsoft.prod'    <EMAIL>
#    ${Password}    Set Variable If    '${ENV}'=='pressure'    06092002zxyC@    '${ENV}'=='paas'    06092002zxyC@    '${ENV}'=='huawei.test'    @cjm820412000    '${ENV}'=='huawei.prod'    @cjm820412000    '${ENV}'=='microsoft.prod'    @cjm820412000
    ${username}    Set Variable If    '${ENV}'=='paas'    HL18271405997    '${ENV}'=='huawei.test'    HL18271405997    '${ENV}'=='huawei.prod'    HL18271405997    '${ENV}'=='microsoft.prod'    HL18271405997    '${ENV}'=='muihuawei.test'    HL18271405997
    ${password}    Set Variable If    '${ENV}'=='paas'    HuangL0920    '${ENV}'=='huawei.test'    HuangL0920    '${ENV}'=='huawei.prod'    HuangL0920    '${ENV}'=='microsoft.prod'    HuangL0920    '${ENV}'=='muihuawei.test'    HuangL0920
    #租户（区分环境）
    ${Athenatenant}    Set Variable If    '${ENV}'=='pressure'    自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    #导入租户/应用/参数/运营单元
    ${tenant}    Set Variable If    '${ENV}'=='pressure'    智驱中台工作台    '${ENV}'=='paas'    智驱中台工作台    '${ENV}'=='huawei.test'    智驱中台工作台    '${ENV}'=='huawei.prod'    智驱中台工作台    '${ENV}'=='microsoft.prod'    智驱中台工作台
    ${application}    Set Variable If    '${ENV}'=='pressure'   【质量组】DTD1.0    '${ENV}'=='paas'    KM-机制场景    '${ENV}'=='huawei.test'    项目中控台    '${ENV}'=='huawei.prod'    项目中控台    '${ENV}'=='microsoft.prod'    项目中控台
    ${parameter}    Set Variable If    '${ENV}'=='pressure'    全部    '${ENV}'=='paas'    全部    '${ENV}'=='huawei.test'    应用参数设定    '${ENV}'=='huawei.prod'    应用参数设定    '${ENV}'=='microsoft.prod'    应用参数设定
    ${unit}    Set Variable If    '${ENV}'=='pressure'    全部    '${ENV}'=='paas'    全部    '${ENV}'=='huawei.test'    全部    '${ENV}'=='huawei.prod'    全部    '${ENV}'=='microsoft.prod'    全部
    #进入对应的运营单元下
    ${group}    Set Variable If    '${ENV}'=='pressure'    自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    ${app}    Set Variable If    '${ENV}'=='pressure'    【质量组】DTD1.0    '${ENV}'=='paas'    KM-机制场景    '${ENV}'=='huawei.test'    应用参数设定测试    '${ENV}'=='huawei.prod'    应用参数设定测试    '${ENV}'=='microsoft.prod'    应用参数设定测试
    ${type}    Set Variable    经典门户
    登录Athena平台    ${Username}    ${Password}    tenant=${Athenatenant}    type=${type}
    点击顶部菜单    全部
    点击右侧菜单    用户工具
    点击右侧菜单    交付设计器
    选择浏览器窗体    -1
    导入数据    ${tenant}    ${application}    ${parameter}    ${unit}    ${group}
    [Teardown]    Run Keywords    关闭浏览器


首页--通用参数设定--敏捷数据设定
    ${Username}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestKm001    '${ENV}'=='paas'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestKm01
    ${Password}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestKm001    '${ENV}'=='paas'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestKm01
    ${Athenatenant}    Set Variable If    '${ENV}'=='pressure'     自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'     自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    ${group}    Set Variable If    '${ENV}'=='pressure'     自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    ${app}    Set Variable If    '${ENV}'=='pressure'     【质量组】DTD1.0    '${ENV}'=='paas'    KM-机制场景    '${ENV}'=='huawei.test'    采购订单管理    '${ENV}'=='huawei.prod'    应用参数设定测试    '${ENV}'=='microsoft.prod'    应用参数设定测试
    登录Athena平台    ${Username}    ${Username}    tenant=${Athenatenant}
    点击顶部菜单    全部
    进入交付设计器    交付设计器
    选择浏览器窗体    -1
    点击通用设定    通用设定
    设定通用参数设定/待办工作台设定/敏捷数据设定    通用参数设定
    生效通用参数设定
    返回    首页
    #敏捷数据设定
    点击通用设定    通用设定
    设定通用参数设定/待办工作台设定/敏捷数据设定    敏捷数据设定
    返回    首页
    #应用设定
    #    应用设定    ${group}    ${app}
    [Teardown]    Run Keywords    关闭浏览器

首页--待办工作台设定
    ${Username}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestKm001    '${ENV}'=='paas'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestKm01
    ${Password}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestKm001    '${ENV}'=='paas'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestKm01
    ${Athenatenant}    Set Variable If    '${ENV}'=='pressure'    自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    登录Athena平台    ${Username}    ${Password}    tenant=${Athenatenant}
    点击顶部菜单    全部
    进入交付设计器    交付设计器
    选择浏览器窗体    -1
    点击通用设定    通用设定
    设定通用参数设定/待办工作台设定/敏捷数据设定    待办工作台设定
    选择任务/项目    任务
    选择分组设定/排序设定/筛选设定/卡面呈现    分组设定
    点击设定
    选择任务/项目    任务
    选择分组设定/排序设定/筛选设定/卡面呈现    排序设定
    点击设定
    选择任务/项目    任务
    选择分组设定/排序设定/筛选设定/卡面呈现    筛选设定
    点击设定
    选择任务/项目    任务
    选择分组设定/排序设定/筛选设定/卡面呈现    卡面呈现
    卡面呈现设定    展示模式二
    卡面呈现设定    展示模式一
    选择任务/项目    项目
    选择分组设定/排序设定/筛选设定/卡面呈现    分组设定
    点击设定
    选择任务/项目    项目
    选择分组设定/排序设定/筛选设定/卡面呈现    排序设定
    点击设定
    选择任务/项目    项目
    选择分组设定/排序设定/筛选设定/卡面呈现    筛选设定
    点击设定
    选择任务/项目    项目
    选择分组设定/排序设定/筛选设定/卡面呈现    卡面呈现
    卡面呈现设定    展示模式二
    卡面呈现设定    展示模式一
    [Teardown]    Run Keywords    关闭浏览器
