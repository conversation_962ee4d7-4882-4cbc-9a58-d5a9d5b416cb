*** Settings ***
Documentation     高伟
Suite Setup       Run Keywords    环境设定
Test Teardown     Run Keywords    错误处理    关闭浏览器
Resource          ../关键字/业务关键字/Athena平台控件.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/公共方法.robot
Resource          ../关键字/系统关键字/web.robot

*** Test Cases ***
常用-添加分组后删除
#    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001     '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
#    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001     '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${username}    Set Variable    HL18271405997
    ${password}    Set Variable    HuangL0920
    登录ATHENA平台    ${username}    ${password}
    点击常用    
    新增分组    常用业务数据录入    常用业务数据组
    新增分组后，添加常用    项目大事记    常用业务数据组

常用-添加报表后移除
#    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001     '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
#    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001     '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${username}    Set Variable    HL18271405997
    ${password}    Set Variable    HuangL0920
    登录ATHENA平台    ${username}    ${password}
    点击常用
    新增分组    常用报表    常用报表分组
    新增分组后，添加常用    异常明细检讨    常用报表分组
    #关闭常用
    关闭常用设置窗体
    点击顶部菜单    全部
    点击右侧菜单    报表
    常用标识校验
    点击常用
    首页常用撤销常用设置    常用报表分组
    关闭常用设置窗体
    取消常用后校验
#    删除分组