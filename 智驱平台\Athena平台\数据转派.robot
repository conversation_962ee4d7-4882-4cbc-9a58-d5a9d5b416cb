*** Settings ***
Documentation     高伟
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/公共方法.robot
Resource          ../关键字/业务关键字/数据转派.robot
Resource          ../配置/数据转派参数.robot
Test Setup        Run Keywords    环境设定
Test Teardown     Close Browser


*** Test Cases ***
数据转派
    ${tomorrow}=    获取明天日期
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    qcsupplier001    '${ENV}'=='huawei.prod'    qcsupplier001    '${ENV}'=='microsoft.prod'    qcsupplier001
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    supplier001    '${ENV}'=='huawei.prod'    supplier001    '${ENV}'=='microsoft.prod'    supplier001
    ${ownerUsername02}    Set Variable If    '${ENV}'=='huawei.test'    HL18271405997    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02
    ${ownerPassword02}    Set Variable If    '${ENV}'=='huawei.test'    HuangL0920    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02
    ${token}     Set Variable If    '${ENV}'=='huawei.test'    ${ass_token_test}    '${ENV}'=='huawei.prod'    ${ass_token_hwprod}    '${ENV}'=='microsoft.prod'    ${ass_token_wrprod}
    ${tenantId}     Set Variable If    '${ENV}'=='huawei.test'    ${ass_routerkey_test}    '${ENV}'=='huawei.prod'    ${ass_routerkey_hwprod}    '${ENV}'=='microsoft.prod'    ${ass_routerkey_wrprod}
    ${assigname}     Set Variable If    '${ENV}'=='huawei.test'    生产华为环境自动化测试智驱入口    '${ENV}'=='huawei.prod'    生产华为环境自动化测试智驱入口    '${ENV}'=='microsoft.prod'    生产华为环境自动化测试智驱入口
    Set Global Variable    ${token}
    Set Global Variable    ${tenantId}
    ${data}    Set Variable    {"ruleId":"DT_c4510b4210001952","ruleValue":"M1;20210801 12:00;true","ruleValues":["M1;20210801 12:00;true"],"operationUnit":null,"status":true,"submit":true}
    调用isv接口发起侦测项目    ${data}
    登录Athena平台     ${ownerUsername}    ${ownerPassword}
    数据转派/提交-任务名称搜索    续签合同确认
    进入任务卡（数据转派）详情
    选中所有待转派数据-转派    ${assigname}    没有意见
    退出当前登录,切换账号登录    ${ownerUsername02}    ${ownerPassword02}
    数据转派/提交-任务名称搜索    续签合同确认
    进入任务卡（数据转派）详情
    选中所有待办数据-提交



