*** Settings ***
Suite Setup       Run Keywords    环境设定
Suite Teardown    Run Keywords    关闭浏览器
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/业务关键字/开发平台.robot
Resource          ../关键字/业务关键字/公共方法.robot
Resource          ../配置/域名.robot
Resource          ../配置/全局参数.robot
Resource          ../元素/开发平台元素.robot
Resource          ../Athena平台/交付设计器.robot
Resource          ../关键字/业务关键字/交付设计器.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Library           Collections
Library           OperatingSystem

*** Test Cases ***
控制能力-按时（普通侦测）
    [Setup]    环境设定
    ${username}    Set Variable If    '${ENV}'=='paas'    ${username}    '${ENV}'=='huawei.test'    ${username}    '${ENV}'=='huawei.prod'    ${username}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${password}    Set Variable If    '${ENV}'=='paas'    ${password}    '${ENV}'=='huawei.test'    ${password}    '${ENV}'=='huawei.prod'    ${password}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${tenantId}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    自动化测试环境    '${ENV}'=='huawei.prod'    智驱中台工作台
    ${application}    Set Variable If    '${ENV}'=='paas'    采购管理88CN    '${ENV}'=='huawei.test'    采购管理88CN    '${ENV}'=='huawei.prod'    采购管理88CN
    ${mechanismName}    Set Variable If    '${ENV}'=='paas'    测试机制不可删除    '${ENV}'=='huawei.test'    测试机制不可删除    '${ENV}'=='huawei.prod'    采购管理88CN
    ${mechanism_parameters_username}    Set Variable If    '${ENV}'=='paas'    ${username}    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ${username}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${mechanism_parameters_password}    Set Variable If    '${ENV}'=='paas'    ${username}    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ${username}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002

#   运行态生效机制（二次编译）
    登录Athena平台    ${username}    ${password}    ${tenantId}
    Athena平台.点击顶部菜单    全部
    点击右侧菜单    交付设计器
    选择浏览器窗体    -1
    应用下的机制生效    ${application}    ${mechanismName}
    关闭浏览器
#   需要看下引擎流程是否正常,引擎正常在看消息
#   校验机制生效效果（im消息发送给机制参数）
    登录Athena平台    ${mechanism_parameters_username}    ${mechanism_parameters_password}    ${tenantId}
    打开消息页
#    校验消息的时间和消息体
    消息检查    按时--计划完成日期--消息发给机制参数



控制能力-按量（普通侦测）
    [Setup]    环境设定
    ${username}    Set Variable If    '${ENV}'=='paas'    ${username}    '${ENV}'=='huawei.test'    ${username}    '${ENV}'=='huawei.prod'    ${username}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${password}    Set Variable If    '${ENV}'=='paas'    ${password}    '${ENV}'=='huawei.test'    ${password}    '${ENV}'=='huawei.prod'    ${password}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${tenantId}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    自动化测试环境    '${ENV}'=='huawei.prod'    智驱中台工作台
    ${application}    Set Variable If    '${ENV}'=='paas'    采购管理88CN    '${ENV}'=='huawei.test'    采购管理88CN    '${ENV}'=='huawei.prod'    采购管理88CN
    ${mechanismName}    Set Variable If    '${ENV}'=='paas'    测试机制不可删除    '${ENV}'=='huawei.test'    测试机制不可删除    '${ENV}'=='huawei.prod'    采购管理88CN
    ${business_username}    Set Variable If    '${ENV}'=='paas'    ${username}    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ${username}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${business_password}    Set Variable If    '${ENV}'=='paas'    ${username}    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ${username}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002

#   运行态生效机制（二次编译）
    登录Athena平台    ${username}    ${password}    ${tenantId}
    Athena平台.点击顶部菜单    全部
    点击右侧菜单    交付设计器
    选择浏览器窗体    -1
    应用下的机制生效    ${application}    ${mechanismName}
    关闭浏览器
#   需要看下引擎流程是否正常,引擎正常在看消息
#   校验机制生效效果（im消息发送给机制参数）
    登录Athena平台    ${business_username}    ${business_password}    ${tenantId}
    打开消息页
#    校验消息的时间和消息体
    检查im消息    按量--基础资料录入--消息发送给业务字段







