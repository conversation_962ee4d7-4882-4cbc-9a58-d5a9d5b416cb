*** Settings ***
Library           SeleniumLibrary
#Library           ../../../RFLib/Base.py
Resource          ../../元素/开发平台元素.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/web.robot
Resource          公共方法.robot
Resource          ../系统关键字/公共方法.robot

*** Keywords ***
搜索PWD任务卡
    [Arguments]    ${work}
    点击    css=.ath-search-icon-with-text



单档多栏定位排序按钮
    [Arguments]    ${name}
    点击    //span[contains(text(),'${name}')]/following-sibling::span[@class='ag-header-icon ag-header-label-icon ag-sort-icon ng-star-inserted']

单档多栏定位筛选按钮
    [Arguments]    ${name}
    点击    //span[contains(text(),'${name}')]/following-sibling::span[@class='ag-header-icon ag-header-label-icon ag-filter-icon ng-star-inserted']

上传
    [Arguments]    ${arg}
    Log    ..${/}..${/}${CURDIR}


    # IM
点击即时对话
    点击    //*[@athtooltipplacement="bottom"]//*[contains(text(),'即时对话')]
    Sleep    3

点击通讯录
    点击   //div[contains(text(),'通讯录')]
    Sleep    3

点击通讯录中租户名称
    [Arguments]    ${name}
    点击    //p[contains(text(),'${name}')]
    Sleep    3

点击通讯录中租户下的部门名称
    [Arguments]    ${name}
    点击    //p[contains(text(),'${name}')]
    Sleep    3

点击租户下部门的人员名称
     [Arguments]    ${name}
     ${startTimeLoc}    按顺序获取元素    //span[contains(text(),"${name}") and @class='memName' ]    0
     点击    ${startTimeLoc}

点击发消息入口
    点击    //span[contains(text(),'发消息')]
    Sleep    3

点击输入框并输入私聊内容
    [Arguments]    ${content}
    点击    //textarea[@placeholder="请输入"]
    输入    //textarea[@placeholder="请输入"]    ${content}
    Press Keys    //textarea[@placeholder="请输入"]    ENTER
    Sleep    2

标记私聊内容为结论后取消标记并撤回
    [Arguments]    ${content}
    点击    //div[@class="chat-box"]
    Sleep    1
    Open Context Menu    //div[contains(text(),'${content}')]
    Sleep    2
    点击    //li[contains(text(),'标记为结论')]
    Sleep    2
    点击    //span[contains(text(),'确定')]
    Sleep    2
    Open Context Menu    //div[contains(text(),'${content}')]
    Sleep    3
    点击    //li[contains(text(),'取消结论')]
    Sleep    2
    Open Context Menu    //div[contains(text(),'${content}')]
    Sleep    2
    点击    //li[contains(text(),'撤回')]

点击添加成员入口后点击添加成员
    #    聊天窗口右上角三个点
    点击    //div[@class="head"]/div[@class="p2p-detail ng-star-inserted"]
    Sleep    2
    点击    //div[contains(text(),'添加成员')]
    Sleep    3
 
点击添加群聊时弹出框下的部门名称
    [Arguments]    ${deptname} 
#    ${startTimeLoc}    按顺序获取元素    //p[@class="deptName" and text()='${deptname}']    1
#    点击     ${startTimeLoc} 
    点击    //div[@class='area area-max-height']//p[@class="deptName" and text()='${deptname}']
    Sleep    3

点击添加群聊时弹出况下部门人员名称
    [Arguments]    ${name}
    点击    //div[@class='area area-max-height']//span[@class='memName'][contains(text(),'${name}')]
    Sleep    3

添加群聊成员后，点击确定
    点击     //span[contains(text(),'确定')]
    Sleep    3
    
解散群聊
    [Arguments]    ${name}
    点击     //p[contains(text(),'你将${name}加入群')]
    Sleep    3
    #    点击空白处，使弹框消失
    #    点击    //div[@class='team-toolBox ng-star-inserted']
    #    Sleep    3
    #    点击右上角三个点
    #    点击    //div[@class="head"]/div[@class="team-detail ng-star-inserted"]
    #    Sleep    3
    点击    //span[contains(text(),'解散群聊')]
    Sleep    3
    点击    //span[contains(text(),'确定')]
    Sleep    3

点击新建任务
    [Arguments]    ${text}
    点击    //div[@class='editor-tool-icon taskMsg ng-star-inserted']
    Sleep    8
    输入    //input[@placeholder="工作名称"]    ${text}
    Sleep    3
    点击    //span[contains(text(),'提交')]
    Sleep    3

点击常用
    点击    //div[@class='common-use-container']

新增分组
    [Arguments]    ${classification}    ${group_name}
    #收起常用业务数据录入
    ${visible}    判断元素是否可见    //span[contains(text(),'常用业务数据录入')]/ancestor::li[contains(@class,'ant-menu-submenu ant-menu-submenu-inline ng-star-inserted ant-menu-submenu-open')]    5
    Run Keyword If    ${visible}==True    js点击    //span[contains(text(),'常用业务数据录入')]
    #展开指定的常用设置
    ${visible}    判断元素是否可见    //span[contains(text(),'${classification}')]/ancestor::li[contains(@class,'ant-menu-submenu ant-menu-submenu-inline ng-star-inserted ant-menu-submenu-open')]    5
    Run Keyword If    ${visible}==False    js点击    //span[contains(text(),'${classification}')]
    #判断测试组分组是否存在，存在则删除
    ${visible}    判断字符是否可见    ${group_name}    3
    Run Keyword If    ${visible}    删除分组    ${group_name}
    #判断默认分组是否展开，没有则点击使其展开
#    ${visible}    判断元素是否可见     //span[contains(text(),'${classification}')]/following::span[contains(text(),'默认分组')]    5
#    Run Keyword If    ${visible}==False    点击    //span[contains(text(),'${classification}')]/following::span[contains(text(),'默认分组')]
    点击    //span[contains(text(),'${classification}')]/following::span[contains(text(),'默认分组')]
    #删除默认分组里面的项目大事记，否则后续添加自定义分组选择不到项目大事记
    元素存在则点击    //span[contains(text(),'项目大事记')]/following::*[@class='iconfont ng-star-inserted'][1]    3
    元素存在则点击    //span[contains(text(),'确定')]    2
    元素存在则点击    //span[contains(text(),'异常明细检讨')]/following::*[@class='iconfont ng-star-inserted'][1]    3
    元素存在则点击    //span[contains(text(),'确定')]    1
    #判断测试组分组是否存在，存在则删除
    ${visible}    判断字符是否可见    ${group_name}    5
    Run Keyword If    ${visible}    删除分组    ${group_name}
    #新增分组
    Sleep    3
    鼠标悬停    //span[contains(text(),'${classification}')]/following-sibling::i[1]
    js点击    //*[contains(text(),'增加分组')]
    输入    //input[@class="ant-input ath-input ng-untouched ng-pristine ng-invalid ng-star-inserted"]    ${group_name}
    点击    //span[contains(text(),'确定')]


输入分组名称后确定
    [Arguments]    ${text}
    输入    //input[@class="ant-input ath-input ng-untouched ng-pristine ng-invalid ng-star-inserted"]    ${text}
    点击    //span[contains(text(),'确定')]
    Sleep    2

新增分组后，添加常用
    [Arguments]    ${ofenusedjob}    ${group_name}
    鼠标悬停    //span[@title='${group_name}']/following::i[1]
    js点击    //span[contains(text(),'增加常用')]
    输入    //input[@class='ant-select-selection-search-input ng-untouched ng-pristine']    ${ofenusedjob}
    Sleep    1
    点击    (//span[@class='ant-checkbox'])[1]
    #   清除蒙层
    Execute Javascript    document.querySelector('.cdk-overlay-backdrop.nz-overlay-transparent-backdrop.cdk-overlay-backdrop-showing').remove()
    Sleep    1
    点击      //div[@class="ant-modal-title"]//div[contains(text(),'增加常用')]
    点击    //span[contains(text(),'确定')]
    Sleep    3

删除分组
    [Arguments]    ${group_name}
    鼠标悬停    //span[@title='${group_name}']/following-sibling::i
    js点击    //li[contains(text(),'删除分组')]
    点击    //span[contains(text(),'确定')]

从默认分组删除从新建分组中移入的作业
    [Arguments]    ${jobname}    ${tagjob}
    ${JobJuge}    Get Element Count    //span[contains(text(),"${tagjob}")]
    Run Keyword If    '${JobJuge}'=='0'   点击    //span[contains(text(),"默认分组")]    ELSE    Log    Nothing need to do.
    #   移除移入分组中的作业--轴定位取消标志
    点击    //span[contains(text(),'${jobname}')]/parent::div/following-sibling::*
    Sleep    3
    点击    //span[contains(text(),"确定")]
    Sleep    3

常用标识校验
#    刷新页面
    Sleep    5
    ${visible}    判断元素是否可见    //div[@class='ath-row ant-row common-use-list ng-star-inserted'][@id='PCC']
    IF    '${visible}'=='False'
        点击    //div[contains(text(),'项目中控台')]
    END
    
    ${attribute}    获取元素属性值    //span[contains(text(),'异常明细检讨')]/following::i[1]    class
    log    ${attribute}
    Should Be Equal As Strings    ${attribute}    anticon iconfont on ng-star-inserted
    #返回首页
    点击    //li[@class='ath-tab-menus-li ng-star-inserted']

首页常用撤销常用设置
    [Arguments]    ${group_name}
    ${visible}    判断元素是否可见    //span[contains(text(),'常用业务数据录入')]/ancestor::li[contains(@class,'ant-menu-submenu ant-menu-submenu-inline ng-star-inserted ant-menu-submenu-open')]    5
    Run Keyword If    ${visible}==True    js点击    //span[contains(text(),'常用业务数据录入')]
    ${visible}    判断元素是否可见    //span[contains(text(),'常用报表')]/ancestor::li[contains(@class,'ant-menu-submenu ant-menu-submenu-inline ng-star-inserted ant-menu-submenu-open')]    5
    Run Keyword If    ${visible}==False    js点击    //span[contains(text(),'常用报表')]
    点击    //span[@title='${group_name}']
    元素存在则点击    //span[contains(text(),'异常明细检讨')]/following::*[@class='iconfont ng-star-inserted'][1]    10
    元素存在则点击    //span[contains(text(),'确定')]    5

关闭常用设置窗体
     js点击    //div[@class='ant-drawer-content-wrapper common-drawer']//i[@class='anticon anticon-close ng-star-inserted']

页面未刷新重复取消常用
    点击    //li[contains(text(),'报表')]
    ${visible}    判断元素是否可见    //div[@class='ath-row ant-row common-use-list ng-star-inserted'][@id='PCC']
    IF    '${visible}'=='False'
        点击    //div[contains(text(),'项目中控台')]
    END
    Sleep    5
    点击     //span[contains(text(),'异常明细检讨')]/following::i[1]
    点击    //span[contains(text(),'确定')]
    当前页面可见字符    取消成功
    刷新页面
    Sleep    5

取消常用后校验
    点击    //li[contains(text(),'报表')]
    Sleep    3
    ${visible}    判断元素是否可见    //div[@class='ath-row ant-row common-use-list ng-star-inserted'][@id='PCC']
    IF    '${visible}'=='False'
        点击    //div[contains(text(),'项目中控台')]
    END
    当前页面不可见元素    //span[contains(text(),'异常明细检讨')]/following::i[1]    3