*** Settings ***
Suite Setup       Run Keywords    环境设定
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/管理模组.robot

*** Test Cases ***
#时间记录簿
#    [Documentation]    陈金明
#    ...    只在生产环境执行
#    登录Athena平台    pur003    pur003
#    租户切换    零部件制造云VSS
#    点击顶部菜单    全部
#    点击右侧菜单    管理模组
#    选择窗体    管理模组
#    管理模组左侧菜单    事件记录簿
#    事件记录簿查询    dj-default-登入系统，成功
#    事件记录簿查询结果校验    2023-05-11 16:09:02    技术主管1(tech003)dj-default-尝试于(MTAuMC4yMTQuMjM4)dj-default-登入系统，成功    \    dj-default-登陆    系统
#    [Teardown]    Run Keywords    关闭浏览器

#已经转入入口
#运营单元代码一览表
#    [Documentation]    陈金明
#    ...    只在生产环境执行
#    登录Athena平台    pur003    pur003
#    租户切换    零部件制造云VSS
#    点击顶部菜单    全部
#    点击右侧菜单    管理模组
#    选择窗体    管理模组
#    管理模组左侧菜单    运营单元代码一览表
##    运营单元代码一览表数据校验
#    [Teardown]    Run Keywords    关闭浏览器

伙伴授权管理
    [Documentation]    陈金明
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002

    登录Athena平台    ${username}    ${password}
#    租户切换    零部件制造云VSS
    点击顶部菜单    全部
    点击右侧菜单    管理模组
    选择窗体    管理模组
    管理模组左侧菜单    伙伴授权管理
    伙伴授权管理数据校验
    [Teardown]    Run Keywords    关闭浏览器

需辅助名单管理
    [Documentation]    陈金明
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${operator}    Set Variable If    '${ENV}'=='huawei.test'    此处需要适配    '${ENV}'=='huawei.prod'    生产华为环境自动化测试小AI入口    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试SD

    登录Athena平台    ${username}    ${password}
    点击顶部菜单    全部
    点击右侧菜单    管理模组
    选择窗体    管理模组
    管理模组左侧菜单    需辅助名单管理
    启用/停止启用
    管理模组.搜索    企业名称    宏远
    需辅助名单管理数据校验    供应商    宏远    01007    \    ${operator}
    新增    鼎捷    ${唯一标识}    59418
    [Teardown]    Run Keywords    关闭浏览器

传送方式管理
    [Documentation]    陈金明
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002

    登录Athena平台    ${username}    ${password}
    点击顶部菜单    全部
    点击右侧菜单    管理模组
    选择窗体    管理模组
    管理模组左侧菜单    传送方式管理
    传送方式管理数据校验
    [Teardown]    Run Keywords    关闭浏览器

数据来源设定
    [Documentation]    陈金明
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002

    登录Athena平台    ${username}    ${password}
    点击顶部菜单    全部
    点击右侧菜单    管理模组
    选择窗体    管理模组
    管理模组左侧菜单    数据来源设定
    数据来源设定数据校验
    [Teardown]    Run Keywords    关闭浏览器

#授权码管理
#    [Documentation]    陈金明
#    登录Athena平台    pur003    pur003
#    租户切换    零部件制造云VSS
#    点击顶部菜单    全部
#    点击右侧菜单    管理模组
#    选择窗体    管理模组
#    管理模组左侧菜单    授权码管理
#    授权码管理数据校验
#    [Teardown]    Run Keywords    关闭浏览器

#用户登录卡管理
#    [Documentation]    陈金明
#    登录Athena平台    pur003    pur003
#    租户切换    零部件制造云VSS
#    点击顶部菜单    全部
#    点击右侧菜单    管理模组
#    选择窗体    管理模组
#    管理模组左侧菜单    用户登录卡管理
#    用户登录卡管理数据校验
#    [Teardown]    Run Keywords    关闭浏览器
