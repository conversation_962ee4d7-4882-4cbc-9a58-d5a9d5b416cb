*** Settings ***
Suite Setup       Run Keywords    环境设定
Test Teardown     Run Keywords    关闭浏览器
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/交付设计器.robot
Resource          ../关键字/业务关键字/开发平台.robot
Resource          ../关键字/业务关键字/公共方法.robot

*** Test Cases ***
限制能力-按熟练度
    [Documentation]    陈金明
    #需求范式发版41220
    #新增共享范式-新增机制-机制原理-机制能力-发布机制-生效机制-发起项目-检查机制能力是否生效
    ${username}    Set Variable If    '${ENV}'=='paas'    ${username}    '${ENV}'=='huawei.test'    ${username}    '${ENV}'=='huawei.prod'    ${username}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${password}    Set Variable If    '${ENV}'=='paas'    ${password}    '${ENV}'=='huawei.test'    ${password}    '${ENV}'=='huawei.prod'    ${password}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${application}    Set Variable If    '${ENV}'=='paas'    质量测试专用应用    '${ENV}'=='huawei.test'    采购管理88CN    '${ENV}'=='huawei.prod'    采购管理88CN
    ${appCode}    Set Variable If    '${ENV}'=='paas'    qctest001    '${ENV}'=='huawei.test'    purchase88CN    '${ENV}'=='huawei.prod'    purchase88CN
    ${tenant}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    智驱中台工作台    '${ENV}'=='huawei.prod'    智驱中台工作台
    ${tenantId}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    IntelligentDriveCenterWorkbench    '${ENV}'=='huawei.prod'    IntelligentDriveCenterWorkbench
    ${person_value}    Set Variable If    '${ENV}'=='paas'    测试职能    '${ENV}'=='huawei.test'    测试职能    '${ENV}'=='huawei.prod'    测试职能
    ${deploy_to_env}    Set Variable If    '${ENV}'=='paas'    大陆正式区（阿里）    '${ENV}'=='huawei.test'    大陆测试区    '${ENV}'=='huawei.prod'    大陆正式区
    ${token}    Set Variable If    '${ENV}'=='paas'    fb6f63fb-e9a0-4968-95ea-76e9481c29c3    '${ENV}'=='huawei.test'    1eee320f-c9f3-4f26-b827-7baa72d72ba8    '${ENV}'=='huawei.prod'    661c4af4-2b02-4c7a-9d47-e3b9836236c1
    Set Global Variable    ${topic}    自动化测试议题
    Set Global Variable    ${token}
    Set Global Variable    ${tenantId}
    #注释：共享范式新建
    #注释：范式名称和机制名称定义，采用预置范式
    ${time}    生成秒时间戳
    Set Global Variable    ${msg}    ${time}
    Set Global Variable    ${paradigmName}    fanshi01
    Set Global Variable    ${paradigmCode}    PD_4b6090c210001fcc
    Set Global Variable    ${mechanismName}    ${time}
    登录开发平台    ${username}    ${password}    ${tenant}
    开发平台.点击顶部菜单    解决方案中心
    搜索应用    ${application}
    进入应用配置页    ${appCode}
    选择浏览器窗体    -1
    点击左侧菜单    机制设计
    新增机制    ${mechanismName}    ${paradigmName}
    进入机制设计页    ${mechanismName}
    新增机制原理    机制原理名称    机制原理描述
    新增限制能力-熟练度    限制能力-按熟练度    限制能力描述    熟练度    ${person_value}    #支持,人员,职能和部门成员
    应用发布    ${deploy_to_env}    ${tenant}
    关闭浏览器
    #购买范式（采用接口形式）定义范式购买的请求入参
    ${data}    Set Variable    {"appCode":"${appCode}","paradigm":"${paradigmCode}","mechanismCodes":["${mechanismCode}"]}
    购买范式    ${data}
    ##注释：Athena平台生效机制
    登录Athena平台    ${username}    ${password}
    租户切换    ${tenant}
    Athena平台.点击顶部菜单    全部
    进入交付设计器    交付设计器
    选择浏览器窗体    -1
    机制生效    ${application}    ${mechanismName}    ${topic}
    选择窗体    鼎捷雅典娜
    刷新页面
    Athena平台.点击顶部菜单    全部
    点击右侧菜单    业务数据录入
    打开应用的作业     ${appCode}   采购管理
    限制能力验证    ${msg}
    登录开发平台    ${username}    ${password}    ${tenant}
    开发平台.点击顶部菜单    解决方案中心
    搜索应用    ${application}
    进入应用配置页    ${appCode}
    选择浏览器窗体    -1
    点击左侧菜单    机制设计
    机制列表搜索    ${mechanismName}
    删除机制    ${mechanismName}