#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行登录任务卡测试用例的脚本
"""

import subprocess
import sys
import os

def run_robot_test():
    """运行Robot Framework测试用例"""
    
    # 测试用例文件路径
    test_file = "智驱平台/测试用例/登录任务卡测试.robot"
    
    # 检查测试文件是否存在
    if not os.path.exists(test_file):
        print(f"错误：测试文件 {test_file} 不存在")
        return False
    
    # Robot Framework命令
    cmd = [
        "robot",
        "--outputdir", "results",  # 输出目录
        "--loglevel", "INFO",      # 日志级别
        test_file
    ]
    
    try:
        print("开始运行登录任务卡测试用例...")
        print(f"命令: {' '.join(cmd)}")
        
        # 运行测试
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        # 输出结果
        print("\n=== 测试输出 ===")
        print(result.stdout)
        
        if result.stderr:
            print("\n=== 错误输出 ===")
            print(result.stderr)
        
        # 检查返回码
        if result.returncode == 0:
            print("\n✅ 测试执行成功！")
            print("📊 查看详细报告：results/report.html")
            print("📋 查看日志：results/log.html")
        else:
            print(f"\n❌ 测试执行失败，返回码：{result.returncode}")
        
        return result.returncode == 0
        
    except FileNotFoundError:
        print("❌ 错误：未找到 robot 命令")
        print("请确保已安装 Robot Framework：")
        print("pip install robotframework")
        print("pip install robotframework-seleniumlibrary")
        return False
    except Exception as e:
        print(f"❌ 运行测试时发生错误：{e}")
        return False

def check_dependencies():
    """检查依赖项"""
    print("检查依赖项...")
    
    try:
        import robot
        print("✅ Robot Framework 已安装")
    except ImportError:
        print("❌ Robot Framework 未安装")
        print("请运行：pip install robotframework")
        return False
    
    try:
        import SeleniumLibrary
        print("✅ SeleniumLibrary 已安装")
    except ImportError:
        print("❌ SeleniumLibrary 未安装")
        print("请运行：pip install robotframework-seleniumlibrary")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("🚀 登录任务卡测试用例运行器")
    print("=" * 50)
    
    # 检查依赖项
    if not check_dependencies():
        print("\n❌ 依赖项检查失败，请安装必要的库后重试")
        return 1
    
    # 运行测试
    success = run_robot_test()
    
    if success:
        print("\n🎉 测试完成！")
        return 0
    else:
        print("\n💥 测试失败！")
        return 1

if __name__ == "__main__":
    sys.exit(main())
