*** Settings ***
Library           SeleniumLibrary
Resource          ../元素/开发平台元素.robot
Resource          ../关键字/业务关键字/Athena平台控件.robot
Resource          ../配置/全局参数.robot
Resource          ../关键字/业务关键字/公共方法.robot
Resource          ../关键字/系统关键字/公共方法.robot
Resource          ../关键字/业务关键字/开发平台.robot
Resource          ../关键字/系统关键字/web.robot
Resource          ../配置/域名.robot
Library           Collections
Library           ../../RFLib/Input.py
Library           OperatingSystem
Library           E:\自动化\auto\ui-case\智驱平台\开发平台\../../RFLib/Shangchuan.py
Library           E:\自动化\auto\ui-case\智驱平台\控件\../../RFLib/Input.py
Resource          ../关键字/业务关键字/个人行事历.robot

*** Test Cases ***
行事历上传附件
    登录Athena平台    TestAthenaAutoTestAi001    TestAthenaAutoTestAi001
    AHENA平台控件.点击顶部菜单    全部
    点击右侧菜单行事历    行事历
    创建上传附件个人行事历    12333333333
    [Teardown]    Run Keywords    关闭浏览器
