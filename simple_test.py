#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的登录测试脚本
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import time

def test_login():
    """测试登录功能"""
    
    # 配置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    
    driver = None
    
    try:
        print("🚀 开始登录测试...")
        
        # 启动浏览器
        print("📱 启动Chrome浏览器...")
        driver = webdriver.Chrome(options=chrome_options)
        driver.maximize_window()
        
        # 打开登录页面
        url = "https://athena-test.digiwincloud.com.cn"
        print(f"🌐 打开登录页面: {url}")
        driver.get(url)
        
        # 等待登录页面加载
        print("⏳ 等待登录页面加载...")
        wait = WebDriverWait(driver, 30)
        username_input = wait.until(
            EC.presence_of_element_located((By.XPATH, "//input[@name='userId']"))
        )
        
        # 输入用户名
        print("👤 输入用户名: qc005")
        username_input.clear()
        username_input.send_keys("qc005")

        # 输入密码
        print("🔐 输入密码...")
        password_input = driver.find_element(By.XPATH, "//input[@name='password']")
        password_input.clear()
        password_input.send_keys("qc005")
        
        # 点击登录按钮
        print("🔘 点击登录按钮...")
        login_button = driver.find_element(By.XPATH, "//div[@class='action']/button/span")
        login_button.click()
        
        # 等待首页加载
        print("⏳ 等待首页加载...")
        try:
            # 等待首页标识元素出现
            wait.until(
                EC.presence_of_element_located((By.XPATH, "//i[@class='icon font-entrance icongongsi']"))
            )
            print("✅ 登录成功！首页已加载")
        except:
            print("⚠️ 首页加载超时，但可能已经登录成功")
        
        # 处理可能的弹窗
        try:
            know_button = driver.find_element(By.XPATH, "//span[contains(text(),'我知道了')]")
            if know_button.is_displayed():
                know_button.click()
                print("✅ 关闭了提示弹窗")
        except:
            print("ℹ️ 没有发现提示弹窗")
        
        time.sleep(2)
        
        # 跳转到任务卡页面
        print("📋 跳转到任务卡页面...")
        task_url = url + "/todo/task"
        driver.get(task_url)
        
        # 等待任务卡页面加载
        print("⏳ 等待任务卡页面加载...")
        try:
            wait.until(
                EC.presence_of_element_located((By.XPATH, "//div[contains(text(),'我的任务')]"))
            )
            print("✅ 任务卡页面加载成功")
        except:
            print("⚠️ 任务卡页面加载超时")
        
        # 点击我的任务
        try:
            my_tasks = driver.find_element(By.XPATH, "//div[contains(text(),'我的任务')]")
            my_tasks.click()
            print("✅ 成功点击'我的任务'")
            time.sleep(3)
        except:
            print("⚠️ 无法点击'我的任务'")
        
        # 验证任务卡页面
        try:
            todo_content = driver.find_element(By.XPATH, "//div[@class='todo-content']")
            if todo_content.is_displayed():
                print("✅ 任务卡内容区域已显示")
            else:
                print("⚠️ 任务卡内容区域未显示")
        except:
            print("⚠️ 未找到任务卡内容区域")
        
        print("🎉 测试完成！")
        
        # 保持浏览器打开一段时间以便查看结果
        print("⏳ 保持页面打开10秒以便查看...")
        time.sleep(10)
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        
    finally:
        if driver:
            print("🔚 关闭浏览器...")
            driver.quit()

if __name__ == "__main__":
    test_login()
