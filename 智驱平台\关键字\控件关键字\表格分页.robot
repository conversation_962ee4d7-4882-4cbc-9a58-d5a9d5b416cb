*** Settings ***
Library           SeleniumLibrary
Resource          ../系统关键字/web.robot
Resource          ../../元素/Athena平台元素.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../系统关键字/接口.robot

*** Keywords ***
分页页码选择
    [Arguments]    ${defaultpagesize}    ${checkpagesize}
    当前页面可见   //ath-select-item[@title='${defaultpagesize} 条/页']
    js点击    //ath-select-item[@title='${defaultpagesize} 条/页']
    当前页面可见    //*[contains(@class,'cdk-overlay-pane')]/ath-option-container/div
    鼠标悬停   //span[text()='${checkpagesize} 条/页']
    js点击  //span[text()='${checkpagesize} 条/页']
    当前页面可见   //ath-select-item[@title='${checkpagesize} 条/页']
    点击   //i[@nzpopoverplacement='bottomLeft']
    当前页面可见   //span[contains(text(),'(共${checkpagesize}条)')]
    点击   //i[@nzpopoverplacement='bottomLeft']


分页切换
    [Arguments]    ${pagenum}
    分页页码选择   50   10
    #向后5页
    鼠标悬停   //span[@class='ant-pagination-item-ellipsis']
    当前页面可见  //li[@title='向后 5 页']
    Sleep   3
    点击   //li[@title='向后 5 页']
    当前页面可见    //li[contains(@class,'ant-pagination-item-active')]/a
    #向前5页
    鼠标悬停   //span[@class='ant-pagination-item-ellipsis']
    当前页面可见  //li[@title='向前 5 页']
    点击  //li[@title='向前 5 页']
    当前页面可见    //li[contains(@class,'ant-pagination-item-active')]/a
    #点击第x页
    鼠标悬停   //a[text()='${pagenum}']
    点击   //a[text()='${pagenum}']
    当前页面可见   //li[contains(@class,'ant-pagination-item-active')]/a
    #点击上一页/下一页
    点击   //li[@title="下一页"][@class='ant-pagination-next ng-star-inserted']
    当前页面可见   //li[@title="上一页"][@class='ant-pagination-prev ng-star-inserted']
    点击   //li[@title="上一页"][@class='ant-pagination-prev ng-star-inserted']
    当前页面可见    //a[text()='1']
    点击   //a[text()='1']
    当前页面可见    //li[@title="上一页"][@class='ant-pagination-prev ng-star-inserted ant-pagination-disabled']


















   



