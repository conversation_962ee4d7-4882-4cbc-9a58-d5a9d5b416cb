*** Settings ***
Documentation     郑苏振
Library           SeleniumLibrary
Resource          ../系统关键字/web.robot
Resource          ../系统关键字/公共方法.robot
Resource          Athena平台.robot
Resource          公共方法.robot
Resource          PCC.robot
Resource          首页.robot

*** Keywords ***
任务详情添加收藏
    [Arguments]    ${taskname}
    跳转网页    /todo/task
    点击    //div[contains(text(),'我的任务')]
    Sleep    10
    任务/项目名称搜索    ${taskname}
    点击    //span[@type='addon']
    Sleep    15
    #切换视图模式
    视图模式设定    卡片
    #点击    //span[@class="card-item-name-left"]/span[contains(text(),'${taskname}')]
    Comment    当前页面可见    //span[@class="card-item-name-left"]/span[contains(text(),'${taskname}')]
    Comment    Mouse Over    //span[@class="card-item-name-left"]/span[contains(text(),'${taskname}')]
    点击卡片    ${taskname}
    Comment    当前页面可见    //span[contains(text(),'更多')]
    js点击    //div[@class='icon-box ng-star-inserted']/div
    点击    //span[text()="收藏"]
    Comment    当前页面可见    //div[text()="设定收藏名称"]
    点击    //input[@class="input-name ant-input ath-input ng-untouched ng-pristine ng-valid"]
    输入    //input[@class="input-name ant-input ath-input ng-untouched ng-pristine ng-valid"]    任务卡收藏-${taskname}
    点击    //span[contains(text(),'确认')]
    Sleep    5
    Comment    当前页面可见    //span[(text()='收藏成功')]
    #关闭项目卡详情
    鼠标悬停    //li[contains(text(),'项目/任务详情')]/i[@nztype='close']
    点击    //li[contains(text(),'项目/任务详情')]/i[@nztype='close']
    Sleep    3
    #关闭待办
    鼠标悬停    //li[contains(text(),'待办')]/i[@nztype='close']
    点击    //li[contains(text(),'待办')]/i[@nztype='close']

查询任务卡快照
    [Arguments]    ${taskname}
    跳转网页    /snapshot
    点击    //div[@class="right"]/div[contains(text(),'任务')]
    Comment    当前页面可见    //span[text()='任务卡收藏-${taskname}']

项目卡详情添加收藏
    [Arguments]    ${projectname}
    跳转网页    /todo/project
    Sleep    10
    点击    //div[contains(text(),'我的项目')]
    任务/项目名称搜索    ${projectname}
    点击    //span[@type='addon']
    Sleep    15
    #切换视图模式
    视图模式设定    卡片
    Comment    当前页面可见    //span[@class="card-item-name-left"]/span[contains(text(),'${projectname}')]
    Comment    鼠标悬停    //span[@class="card-item-name-left"]/span[contains(text(),'${projectname}')]
    点击卡片    ${projectname}
    Sleep    3
    鼠标悬停    //div[@class='project-operate-list ng-star-inserted']/div
    js点击    //div[@class='project-operate-list ng-star-inserted']/div
    Comment    当前页面可见    //span[text()="收藏"]
    点击    //span[text()="收藏"]
    Sleep    5
    Comment    当前页面可见    //div[text()="设定收藏名称"]
    鼠标悬停    //input[@class="input-name ant-input ath-input ng-untouched ng-pristine ng-valid"]
    点击    //input[@class="input-name ant-input ath-input ng-untouched ng-pristine ng-valid"]
    Sleep    3
    输入    //*[@id="main-content"]/app-card-detail/div/div[2]/app-task-or-project-detail/app-project-detail/div/app-snapshot-modal/div/div/input    项目卡收藏-${projectname}
    点击    //span[contains(text(),'确认')]
    Sleep    5
    Comment    当前页面可见    //span[(text()='收藏成功')]
    #关闭项目卡详情
    鼠标悬停    //li[contains(text(),'项目/任务详情')]/i[@nztype='close']
    点击    //li[contains(text(),'项目/任务详情')]/i[@nztype='close']
    Sleep    3
    #关闭待办
    鼠标悬停    //li[contains(text(),'待办')]/i[@nztype='close']
    点击    //li[contains(text(),'待办')]/i[@nztype='close']

查询项目卡快照
    [Arguments]    ${projectname}
    跳转网页    /snapshot
    点击    //div[@class="right"]/div[contains(text(),'项目')]
    Comment    当前页面可见    //span[text()='项目卡收藏-${projectname}']

根据关键字搜索快照
    [Arguments]    ${searchkey}
    跳转网页    /snapshot
    Sleep    30
    元素存在则点击    //input[@placeholder='请输入收藏名称或来源单号']
    Sleep    3
    输入    //input[@placeholder='请输入收藏名称或来源单号']    ${searchkey}
    Sleep    3
    comment    当前页面可见    //span[text()='项目卡收藏-${searchkey}']

清空搜索
    clear element Text //input[@placeholder='请输入收藏名称或来源单号']

根据时间筛选快照
    [Arguments]    ${starttime}    ${endtime}
    跳转网页    /snapshot
    Sleep    10
    鼠标悬停    //ath-range-picker[@nzformat='yyyy/MM/dd']
    点击    //ath-range-picker[@nzformat='yyyy/MM/dd']
    Sleep    10
    Comment    当前页面可见    //div[@class='ant-picker-panels']
    点击    //td[@role='gridcell']/div[contains(text(),'${starttime}')]
    Sleep    3
    点击    //td[@role='gridcell']/div[contains(text(),'${endtime}')]
    # \ executejavascript    window.document.getElementsByClassName('ng-star-inserted')[0].value='${starttime}'
    # \ Sleep \ 10
    # \ executejavascript    window.document.getElementsByClassName('ng-star-inserted')[1].value='${endtime}'
    Comment    当前页面可见    //div[text()='2024/10']

切换列表模式
    Comment    当前页面可见 //i[@class='icon list']
    点击    //i[@class='icon list']
    Sleep    3
    Comment    当前页面可见 //thead[@class='ant-table-thead ng-star-inserted']

删除快照
    鼠标悬停    //div[@class='snapshot-name']
    Comment    当前页面可见    //div[@class='snapshot-head-right ant-dropdown-trigger']
    Sleep    3
    鼠标悬停    //div[@class='snapshot-head-right ant-dropdown-trigger']
    Sleep    3
    鼠标悬停    //li[@class='ant-dropdown-menu-item']/span[text()='删除']
    点击    //li[@class='ant-dropdown-menu-item']/span[text()='删除']

进入快照详情
    [Arguments]    ${taskname}
    鼠标悬停    //div[@class='snapshot-name']
    点击    //div[@class='snapshot-name']
    Sleep    3
    Comment    当前页面可见    //div[@class='info']/span[contains(text(),'${taskname}')]
    #进入项目卡/任务卡详情
    鼠标悬停    //div[contains(text(),'详情')]
    js点击    //div[contains(text(),'详情')]
    Sleep    3
    Comment    当前页面可见    //li[contains(text(),'项目/任务详情')]
    #关闭详情
    点击    //li[@class='ath-tab-menus-li ng-star-inserted ath-tab-menus-active'][contains(text(),'项目/任务详情')]/i[@nztype='close']
    Sleep    3
    #获取最新数据
    鼠标悬停    //div[contains(text(),'获取最新数据')]
    js点击    //div[contains(text(),'获取最新数据')]
    Sleep    10
    Comment    当前页面可见    //div[@class='tab active ng-star-inserted']
    #关闭页签
    点击    css:app-snapshot-detail > div > div.tabs > div.tab.active.ng-star-inserted > svg > use
    Sleep    10
    #返回快照列表
    鼠标悬停    //div[@class='info']/span[contains(text(),'${taskname}')]
    点击    //div[@class='info']/span[contains(text(),'${taskname}')]

关闭项目/任务卡详情页签
    Mouse Over //li[contains(text(),'项目/任务详情')]/i[@nztype='close']
    点击 //li[contains(text(),'项目/任务详情')]/i[@nztype='close']

关闭待办页签
    Mouse Over //li[contains(text(),'待办')]/i[@nztype='close']
    点击    //li[contains(text(),'待办')]/i[@nztype='close']
