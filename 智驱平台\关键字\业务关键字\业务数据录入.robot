*** Settings ***
Library           SeleniumLibrary
Resource          ../系统关键字/web.robot
Resource          ../系统关键字/公共方法.robot
Resource          Athena平台控件.robot
Resource          Athena平台.robot

*** Keywords ***
关闭业务数据录入页签
    当前页面可见    //li[contains(text(),'业务数据录入')]
    鼠标悬停    //ul/li[contains(text(),'业务数据录入')]/i
    点击    //ul/li[contains(text(),'业务数据录入')]/i
    当前页面可见    //div[text()='是否确认关闭该页面']
    点击    //button/span[contains(text(),'确定')]
    当前页面不可见元素    //li[contains(text(),'业务数据录入')]

查询所需作业
    [Arguments]    ${work}    ${app}=null
    元素存在则点击    //app-base-data-toolbar-search//i
    sleep    3
    输入    //app-base-data-toolbar-search//input    ${work}
    当前页面可见    //div[@class='cdk-overlay-pane']//span[text()='${work}']
    #此处兼容重名的基础资料，带上应用名称一起判断
    IF    '${app}'=='null'
        点击    //div[@class='cdk-overlay-pane']//span[text()='${work}']
    ELSE
        点击    xpath=(//span[contains(text(),'${app}')]/following::*//*[contains(text(),'${work}')])[1]
    END
    Sleep    2

关闭作业页签
    元素存在则点击     //button[@class='ath-close-all-btn ng-star-inserted']/i

串项目跳转
    点击     (//*[@iconfont="iconchuanchaxiangmuicon1" and @title="查看项目详情"])[1]
    Sleep    5
    ${Keywords}    Get Element Count    //div[contains(text(),'无开启此项目的权限')]
    Run Keyword If    '${Keywords}'=='0'
    ...    有权限操作
    ...    ELSE    无权限操作    

无权限操作
    点击    //span[contains(text(),'我知道了')]
    Sleep    2

有权限操作
    点击    //*[@class='iconfont icon ant-dropdown-trigger ng-star-inserted']
    点击    //div[contains(text(),'报工查询')]
    Sleep    20
    点击    //span[@class='bi-menu-item-text'][contains(text(),'导出')]
    点击    //span[contains(text(),'确定')]
    当前页面可见字符    导出成功
    点击    //div[@class='fun-icon jump']//i[@class='anticon']    #跳转至报表
    Sleep    5

新增单条数据
    [Arguments]    ${no}    ${name}    ${email}
    点击    //span[contains(text(),'新增')]
    当前页面可见    //span[contains(text(),'新建')]
    输入    //input[@placeholder="请输入test员工编号"]    ${no}
    输入    //input[@placeholder="请输入test员工姓名"]    ${name}
    输入    //input[@placeholder="请输入test员工邮箱"]    ${email}
    Sleep    5
    点击    //span[contains(text(),'新建')]
    Sleep    5

删除数据入回收站
    [Arguments]    ${key}
    点击    //div[contains(text(),'表格_双档作业测试')]
    Sleep    5
    点击    //div[contains(text(),'${key}')]/preceding::span[@class='ant-checkbox'][1]
    点击    //span[contains(text(),'删除到回收站')]
    点击    //*[contains(text(),' 确定')]

批量删除数据入回收站
    点击    //div[contains(text(),'表格_双档作业测试')]
    Sleep    5
    点击    //span[contains(text(),'test员工编号')]/preceding::span[@class='ant-checkbox']
    点击    //*[contains(text(),'删除到回收站')]
    点击    //*[contains(text(),' 确定')]
    Sleep    10
    #当前页面可见字符    操作成功
    刷新页面
    Sleep    10

批量删除清空回收站
    批量删除数据入回收站
    Sleep    10
    #进入回收站
    js点击    //nz-badge[contains(text(),'回收站')]
    点击    //div[@class="athena-table-content ag-theme-athena has-status-bar"]/ag-grid-angular/div/div[2]/div[2]/div[1]/div[@class="ag-pinned-left-header"]/div/div
    #刷新作业列表
    点击    //div[@class="list-header-refresh"]
    #搜索作业名称
    输入    //input[@class='searchInput ant-input ath-input ng-untouched ng-pristine ng-valid ng-star-inserted']    表格
    当前页面可见    //span[@style="color: rgb(32, 18, 217);"]
    #彻底删除数据
    点击    //span[contains(text(),'清空回收站')]
    点击    //span[contains(text(),'确定')]
    当前页面可见字符    暂无数据

回收站数据批量恢复
    [Arguments]    ${key}    ${key1}
    Sleep    10
    js点击    //nz-badge[contains(text(),'回收站')]
    点击    //div[@class="athena-table-content ag-theme-athena has-status-bar"]/ag-grid-angular/div/div[2]/div[2]/div[1]/div[@class="ag-pinned-left-header"]/div/div
    点击    //*[contains(text(),'还原')]
    点击    //span[contains(text(),'确定')]
    Sleep    10
    点击    //*[contains(text(),'业务数据录入')]
    点击    //div[@class='action-icon']//i[@class='anticon']
    当前页面可见字符    ${key}
    当前页面可见字符    ${key1}

回收站数据删除恢复
    [Arguments]    ${key}
    刷新页面
    点击    //nz-badge[contains(text(),'回收站')]
    Sleep    5
    #回收站数据断言
    当前页面可见    //div[@class="list-header-refresh"]
    当前页面可见字符    总笔数
    当前页面可见字符    表格_双档作业测试
    #还原删除的数据
    ${row}    获取元素属性值    //div[contains(text(),'${key}')]/ancestor::div[@aria-rowindex !='']    aria-rowindex
    Log     ${row}
    点击    //div[@aria-rowindex='${row}']//span[@class='ant-checkbox']

#    点击    //div[contains(text(),'${key}')]/preceding::span[@class='ant-checkbox'][1]
    Sleep    5
    点击    //*[contains(text(),'还原')]
    点击    //span[contains(text(),'确定')]
    #当前页面可见字符    操作成功
    Sleep    10
    点击    //*[contains(text(),'业务数据录入')]
    点击    //div[@class='action-icon']//i[@class='anticon']
    当前页面可见字符    ${key}
    #批量删除当前数据

批量删除数据至回收站
    [Arguments]    ${no}    ${name}    ${email}    ${no1}    ${no2}
    点击    //*[contains(text(),'业务数据录入')]
    #新增第一条    \
    点击    //span[contains(text(),'新增')]
    当前页面可见    //span[contains(text(),'新建')]
    输入    //input[@placeholder="请输入test员工编号"]    ${no}
    输入    //input[@placeholder="请输入test员工姓名"]    ${name}
    输入    //input[@placeholder="请输入test员工邮箱"]    ${email}
    点击    //span[contains(text(),'新建')]
    #当前页面可见字符    保存成功
    Sleep    10
    #新增第二条
    点击    //div[@class='ant-tabs-tabpane ant-tabs-tabpane-active ng-star-inserted']//span[contains(text(),'复制')]
    Sleep    5
    输入    //input[@placeholder="请输入test员工编号"]    ${no1}
    点击    //span[contains(text(),'新建')]
    #当前页面可见字符    保存成功
    Sleep    10
    批量删除数据入回收站