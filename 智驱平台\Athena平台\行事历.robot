*** Settings ***
Documentation     黄蕾
Suite Setup       Run Keywords    环境设定
Suite Teardown    Run Keywords    关闭浏览器
Test Teardown     Run Keywords    错误处理    关闭浏览器
Resource          ../关键字/业务关键字/公共方法.robot
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/业务关键字/个人行事历.robot
Resource          ../关键字/业务关键字/团队行事历.robot
Resource          ../关键字/业务关键字/Athena平台.robot

*** Test Cases ***
个人行事历创建（未提交）
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    点击右侧菜单行事历    行事历
    新增个人行事历（未勾选已执行）
    [Teardown]    Run Keywords    关闭浏览器

个人行事历创建（已执行）
    ${username}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    点击右侧菜单行事历    行事历
    已完成个人行事历创建展示
    [Teardown]    Run Keywords    关闭浏览器

个人行事历创建（编辑）
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    点击右侧菜单行事历    行事历
    未完成个人行事历创建展示
    Sleep    20
    [Teardown]    Run Keywords    关闭浏览器

团队行事历创建失败
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    点击右侧菜单行事历    行事历
    创建团队行事历（不修改名称）
    创建团队行事历重复提示    我知道了
    [Teardown]    Run Keywords    关闭浏览器

团队行事历全链路
    #团队行事历创建账号
    ${username}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #团队行事历指定管理员账号
    ${Tusername}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestKm001    '${ENV}'=='paas'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02
    ${Tpassword}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestKm001    '${ENV}'=='paas'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02
    #管理员
    ${Tuser}    Set Variable If    '${ENV}'=='pressure'    测试环境自动化测试KM    '${ENV}'=='paas'    测试环境自动化测试KM    '${ENV}'=='huawei.test'    测试环境自动化测试KM    '${ENV}'=='huawei.prod'    生产华为环境自动化测试小AI入口    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试小AI入口
    #当责者
    ${user}    Set Variable If    '${ENV}'=='pressure'    测试环境自动化测试小AI平台    '${ENV}'=='paas'    测试环境自动化测试小AI平台    '${ENV}'=='huawei.test'    测试环境自动化测试小AI平台    '${ENV}'=='huawei.prod'    生产华为环境自动化测试小AI平台    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试小AI平台
    #创建、退出
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    点击右侧菜单行事历    行事历
    创建团队行事历（修改名称）
    退出团队行事历    ${Tuser}
    关闭浏览器
    #转移
    登录ATHENA平台    ${Tusername}    ${Tpassword}
    语言设定    简体
    点击顶部菜单    全部
    点击右侧菜单行事历    行事历
    转移团队行事历    ${user}
    关闭浏览器
    #解散
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    点击右侧菜单行事历    行事历
    解散团队行事历
    关闭浏览器
