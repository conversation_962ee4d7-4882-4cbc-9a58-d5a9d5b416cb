*** Settings ***
Library           SeleniumLibrary
Resource          ../系统关键字/web.robot
Resource          ../../元素/Athena平台元素.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../系统关键字/接口.robot

*** Keywords ***
发起合并单元格项目卡
    [Arguments]   ${titlebox1}  ${titlebox2}    ${titlebox3}
    点击顶部菜单    全部
    点击    //span[contains(text(),'发起项目')]
    当前页面可见    //div[@id='card_M9d63_b4e2_mainline_project_0005']
    点击    //div[@id='card_M9d63_b4e2_mainline_project_0005']
    Sleep   10
    元素存在则点击  //div/ath-input-text/ath-input-group
    输入  //input[@name='contract_terms_first_title']  ${titlebox1}
    Press Keys    //input[@name='contract_terms_first_title']   ENTER
    元素存在则点击  //div[@row-index="0"]/div[1]
    输入  //input[@name='contract_terms_second_title']  ${titlebox2}
    #新增表格数据
    元素存在则点击  //button/span[contains(text(),'新增行')]
    当前页面可见  //div[@row-index='1']/div[1]  3
    点击  //div[@row-index='1']/div[1]
    输入  //input[@name='contract_terms_second_title']  ${titlebox3}
    #提交
    元素存在则点击  //button/span[contains(text(),'提交')]
    当前页面可见  //div[contains(@class,'ant-modal-confirm-conten')]
    元素存在则点击  //button/span[contains(text(),'确定')]
    当前页面可见字符  发起成功
    
合并单元格任务卡
    [Arguments]   ${titlebox1}   ${titlebox2}  ${titlebox3}
    #进入任务卡详情
    判断后跳转到目标页面    /todo/task
    Sleep   10
    视图模式设定    卡片
    点击    //div[@class='toolbar']/app-todo-filter
    切换筛选类型  常规筛选
    点击  //button[@class='ath-btn ant-btn ant-btn-default']/span[text()='重置']
    #筛选合并单元格任务卡
    输入  //div/app-todo-comm-filter/div[1]/app-todo-filter-item/ath-tree-select/div/nz-select-search/input  条款送审
    元素存在则点击  //ath-tree-node-title/ath-tooltip-wrapper/div/span/span[text()='条款送审']
    点击  //div/app-todo-comm-filter/div[1]/app-todo-filter-item/ath-tree-select/div/nz-select-search/input 
    输入    //div[@class="filter-list-container ng-star-inserted"]/app-todo-comm-filter/div[2]/app-todo-filter-item/ath-tree-select/div/nz-select-search/input    未读
    鼠标悬停    //span[@class='font-highlight'][text()='未读']
    点击  //span[@class='font-highlight'][text()='未读']
    当前页面可见    //div[contains(@class,'athena-selected-panel')]
    点击    //button[@class='ath-btn ant-btn ant-btn-primary']
    #根据创建时间降序排序
    js点击    //span[contains(text(),'排序')]
    点击    //span[contains(text(),'重置')]
    Sleep    5
    #删除排序条件
    #js点击    //span[contains(text(),'排序')]
    点击    //div[@class='cdk-drop-list sort-item-list']/div[1]/*[name()='svg']
    点击    //div[@class='cdk-drop-list sort-item-list']/div[1]/*[name()='svg']
    点击    //div[@class='cdk-drop-list sort-item-list']/div[1]/*[name()='svg']
    #点击    //div[@class='cdk-drop-list sort-item-list']/div[1]/*[name()='svg']
    #降序
    #js点击    //span[contains(text(),'排序')]
    元素存在则点击     //div[@class='ant-tabs-tab-btn']/ath-tab-normal-title/span[contains(text(),'降序')]
    点击    //span[contains(text(),'确定')]
    Sleep    5
    #选择最新的任务卡点击进入详情
    当前页面可见  //div[@class='todo-card-item-container ng-star-inserted']  3
    点击  //div[@class='todo-card-item-container ng-star-inserted']
    Sleep   5
    元素存在则点击    //app-dynamic-input-display[@title='${titlebox3}']
    当前页面可见  //button[@class='ath-btn ant-btn tableToolbar ath-btn-toolbar ng-star-inserted']
    js点击    //button[@class='ath-btn ant-btn tableToolbar ath-btn-toolbar ng-star-inserted']
    当前页面可见  //div[@title='${titlebox1}']/div/span/cell-renderer[@class='dynamic-cell-render ath-table-cell-render cell-single-control canEditor ng-star-inserted']
    #在选中单元格下新增行
    元素存在则点击    //app-dynamic-input-display[@title='${titlebox2}']
    当前页面可见  //button[@class='ath-btn ant-btn tableToolbar ath-btn-toolbar ng-star-inserted']
    js点击    //button[@class='ath-btn ant-btn tableToolbar ath-btn-toolbar ng-star-inserted']
    当前页面可见  //div[@title='${titlebox1}']/div/span/cell-renderer[@class='dynamic-cell-render ath-table-cell-render cell-single-control canEditor ng-star-inserted']

单元格拖拽赋值
    元素存在则点击  //button/span[contains(text(),'新增行')]
    当前页面可见   //span[@class='error-text-tip sync-errors-tip ng-star-inserted']
    元素存在则点击   //*[@col-id='test_input']/div/span/cell-renderer
    鼠标悬停   //div[@class='ag-fill-handle']
    Sleep   3
    Drag And Drop   //div[@class='ag-fill-handle']   //div[@row-index='1']/div[@col-id='test_input']
    Sleep   3
    当前页面可见  //div[@row-index='1']/div[@col-id='test_input']/div/span/cell-renderer/div[1]/div/div/app-dynamic-input-display[@title='景枫中心1层']






