*** Settings ***
Library           SeleniumLibrary
Resource          ../系统关键字/web.robot
Resource          ../../元素/Athena平台元素.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../系统关键字/接口.robot

*** Keywords ***
点击新增行
    元素存在则点击  //button/span[contains(text(),'新增行')]    3
    当前页面可见  //div[@class='ag-center-cols-container']    3

点击新增打开子页签
    元素存在则点击  //button/span[contains(text(),'新增')]
    Sleep    3
    当前页面可见  //app-base-data-tab-title/div/div[contains(text(),'新增')]

点击单元格字段
    [Arguments]    ${schemakey}
    元素存在则点击  //div[@col-id='${schemakey}']/div[@class='ag-cell-wrapper']    3
    当前页面可见  //input[@name='${schemakey}']    3

输入单元格字段值
    [Arguments]    ${tablecontent}   ${schemakey}
    当前页面可见  //input[@name='${schemakey}']  3
    输入   //input[@name='${schemakey}']   ${tablecontent}

submit保存
    元素存在则点击  //button/span[contains(text(),'保存')]  3
    当前页面可见字符  操作成功

submit删除
    全选表格数据
    Sleep   3
    元素存在则点击  //button/span[contains(text(),'删除')]   3
    当前页面可见  //div[contains(@class,'ant-modal-body')]   3
    Sleep   3
    元素存在则点击  //button/span[contains(text(),'确定')]   3
    当前页面可见字符  操作成功

全选表格数据
    元素存在则点击    //ath-patch-header-renderer/label/span[@class='ant-checkbox']
    当前页面可见  //div[@class='status-bar']    3

基础表格分页页码选择
    [Arguments]    ${defaultpagesize}    ${checkpagesize}
    当前页面可见   //ath-open-window-table/div/div/ath-pagination/div/ath-select/ath-select-top-control/ath-select-item/span[text()='${defaultpagesize} 条/页']
    点击   //ath-open-window-table/div/div/ath-pagination/div/ath-select/ath-select-top-control/ath-select-item/span[text()='${defaultpagesize} 条/页']
    当前页面可见    //div[contains(@class,'cdk-overlay-pane')]/ath-option-container/div
    鼠标悬停  //span[text()='${checkpagesize} 条/页']
    点击  //span[text()='${checkpagesize} 条/页']
    当前页面可见  //div/ath-select/ath-select-top-control/ath-select-item[@title='${checkpagesize} 条/页']

基础表格分页切换
    #分页页码选择 \ \ 50 \ \ 10
    #向后5页
    鼠标悬停   //span[@class='ant-pagination-item-ellipsis']
    当前页面可见   //li[@title='向后 5 页']   3
    Sleep   3
    点击   //li[@title='向后 5 页']
    当前页面可见    //li[contains(@class,'ant-pagination-item-active')]/a
    #向前5页
    鼠标悬停  //span[@class='ant-pagination-item-ellipsis']
    当前页面可见  //li[@title='向前 5 页']   3
    点击  //li[@title='向前 5 页']
    当前页面可见    //li[contains(@class,'ant-pagination-item-active')]/a

基础表格分页切换到页码
    [Arguments]    ${pagenum}
    #点击第x页
    鼠标悬停    //a[text()='${pagenum}']
    点击    //a[text()='${pagenum}']
    当前页面可见    //li[contains(@class,'ant-pagination-item-active')]/a[text()='${pagenum}']

点击单元格
    [Arguments]    ${name}    ${no}
    点击    //div[@class="ag-center-cols-viewport"]/div/div[${no}]/div[@col-id="${name}"]

关闭当前基础资料
    元素存在则点击  //div[@class='ant-tabs-nav-list']/div/div[@aria-selected='true']/div/button[@aria-label='Close tab']/i    3

关闭所有基础资料
    元素存在则点击  //*[@id="base-data-container"]/ath-tabs/ath-tabs-nav/div/div/button[@class='ath-close-all-btn ng-star-inserted']/i   3
    当前页面不可见元素  //*[@id="base-data-container"]/ath-tabs/ath-tabs-nav/div/div/button[@class='ath-close-all-btn ng-star-inserted']/i   3
