*** Settings ***
Documentation     黄蕾
Suite Setup       Run Keywords    环境设定
Suite Teardown    Run Keywords    关闭浏览器
Test Teardown     Run Keywords    错误处理    关闭浏览器
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/公共方法.robot
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/业务关键字/TBB报表.robot
Resource          ../关键字/业务关键字/智能入口.robot
Resource          ../关键字/业务关键字/ABI报表.robot

*** Test Cases ***
打开报表（TBB）
    [Setup]    Run Keywords    环境设定
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    报表
    rescode与code一致    项目进度总览图
    rescode为空    项目进度分析
    #打开rescode与code不同的报表    #只有老个案才有
    [Teardown]    Run Keywords    关闭浏览器

TBB报表（项目进度总览图）
    [Setup]    Run Keywords    环境设定
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    报表
    查询需要的作业    项目进度总览图
    TBB报表内容展示
    [Teardown]    Run Keywords    关闭浏览器

打开报表（ABI）
    [Setup]    Run Keywords    环境设定
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    登录ATHENA平台    ${username}    ${password}
    语言设定    简体
    点击顶部菜单    全部
    进入基线标准作业    报表
    查询需要的作业    异常明细检讨
    ABI报表查询
    报表设计
    报表导出
    常用条件添加/进入
    常用条件删除
    [Teardown]    Run Keywords    关闭浏览器

#覆盖bug158977，只覆盖华为测试，和生产
任务报工明细表
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${tenant}    Set Variable If    '${ENV}'=='pressure'     自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'     自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    ${examinee}    Set Variable If    '${ENV}'=='pressure'     自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'     后置1746123081616    '${ENV}'=='huawei.prod'    子项目1-1746143188    '${ENV}'=='microsoft.prod'    1746519496383

    登录ATHENA平台    ${username}    ${password}    tenant=${tenant}
    点击顶部菜单    全部
    进入基线标准作业    报表
    查询需要的作业    任务报工明细表
    点击    //div[contains(text(),'常用条件')]
    点击    //div[@class='comm-condition-item-text'][contains(text(),'UI自动化查询条件')]
    点击    	//span[@class='ng-star-inserted'][contains(text(),'查询')]
    Iframe选择    //iframe[contains(@src,'ReportKey=PCC')]
    当前页面可见字符    ${examinee}
    [Teardown]    Run Keywords    关闭浏览器

#覆盖bug169026，只覆盖华为测试
#使用繁体语言
#by cjm
工单任务追踪
    [Tags]    工单任务追踪
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    pur001
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    pur001
    ${tenant}    Set Variable If    '${ENV}'=='pressure'     自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'     E10_6003版本测试租户    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    登录ATHENA平台    ${username}    ${password}    tenant=${tenant}    language=繁体
    点击顶部菜单    全部
    进入基线标准作业    報表
    查询需要的作业    工單任務追蹤
    工单任务追踪查询
    [Teardown]    Run Keywords    关闭浏览器



