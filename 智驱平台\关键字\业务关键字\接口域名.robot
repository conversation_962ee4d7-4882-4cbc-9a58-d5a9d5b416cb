*** Settings ***

*** Keywords ***
接口域名
    ${taskengine}    Set Variable If    '${ENV}'=='paas'    taskengine-paas.apps.digiwincloud.com.cn    '${ENV}'=='huawei.test'    taskengine-test.apps.digiwincloud.com.cn    '${ENV}'=='microsoft.test'    ${<PERSON>平台域名}[microsofttest]    '${ENV}'=='huawei.prod'    taskengine.apps.digiwincloud.com.cn    '${ENV}'=='microsoft.prod'    taskengine.apps.digiwincloud.com
    Set Global Variable    ${taskengine}
    ${knowledgemaps}    Set Variable If    '${ENV}'=='paas'    knowledgemaps-paas.apps.digiwincloud.com.cn    '${ENV}'=='huawei.test'    knowledgemaps-test.apps.digiwincloud.com.cn    '${ENV}'=='microsoft.test'    ${Athena平台域名}[microsofttest]    '${ENV}'=='huawei.prod'    knowledgemaps.apps.digiwincloud.com.cn    '${ENV}'=='microsoft.prod'    knowledgemaps.apps.digiwincloud.com
    Set Global Variable    ${knowledgemaps}
    ${iam_url}    Set Variable If    '${ENV}'=='huawei.test'    iam-test.digiwincloud.com.cn    '${ENV}'=='huawei.prod'    iam.digiwincloud.com.cn    '${ENV}'=='microsoft.prod'    iam.digiwincloud.com
    Set Global Variable    ${iam_url}
    ${isv_url}    Set Variable If    '${ENV}'=='huawei.test'    isv-gateway-test.apps.digiwincloud.com.cn    '${ENV}'=='huawei.prod'    isv-gateway.apps.digiwincloud.com.cn    '${ENV}'=='microsoft.prod'    isv-gateway.apps.digiwincloud.com
    Set Global Variable    ${isv_url}