*** Settings ***
Suite Setup       Run Keywords    环境设定
Test Teardown     Run Keywords    错误处理
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/SCS.robot

*** Test Cases ***
单体数据任务全流程
    #当责者账号
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    ScsQCTest001
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    ScsQCTest001
    ${key}    生成毫秒时间戳
    Set Global Variable    ${key}
    ${project}    生成毫秒时间戳
    Set Global Variable    ${project}
    ${money}    生成毫秒时间戳
    Set Global Variable    ${money}
    ${report}    生成毫秒时间戳
    Set Global Variable    ${report}
    ${term}    生成毫秒时间戳
    Set Global Variable    ${term}
    登录Athena平台    ${username}    ${password}
    语言设定    简体
    #维护资料录入数据
    维护单体基础资料    ${key}    ${project}    ${money}    ${report}    ${term}
