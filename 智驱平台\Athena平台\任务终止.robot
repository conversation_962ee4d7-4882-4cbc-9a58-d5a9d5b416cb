*** Settings ***
Documentation     陈金明
Suite Setup       Run Keywords    环境设定
Suite Teardown    Run Keywords    关闭浏览器
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/系统关键字/公共方法.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/任务终止.robot

*** Test Cases ***
#业务流程：发起项目，任务卡提交，审核不同意，发起人终止任务
任务终止
    [Documentation]
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01      '${ENV}'=='paas'    TestAthenaAutoTestAi001
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01      '${ENV}'=='paas'    TestAthenaAutoTestAi001
    ${product_name}    生成秒时间戳
    ${tenant}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'     自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    
    登录Athena平台    ${username}    ${password}    tenant=${tenant}
    点击顶部菜单  全部
    点击右侧菜单  发起项目
    发起项目    ${product_name}    1    1    ${product_name}
    任务卡提交并终止    ${product_name}
#    关闭标签