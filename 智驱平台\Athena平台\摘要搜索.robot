*** Settings ***
Documentation     罗丹
Suite Setup       Run Keywords    环境设定
Test Teardown     Run Keywords    错误处理    关闭浏览器
Resource          ../关键字/业务关键字/智能入口.robot
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/业务关键字/待办.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/顺序签核.robot

*** Test Cases ***
摘要搜索
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    qcuser001    '${ENV}'=='huawei.prod'    qcuser001    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    qcuser001    '${ENV}'=='huawei.prod'    qcuser001    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${tenant}    Set Variable If     '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'     AthenaQCTestW    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    ${key}    Set Variable If    '${ENV}'=='huawei.test'    AB003    '${ENV}'=='huawei.prod'    qcuser001    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${a}    Set Variable If    '${ENV}'=='huawei.test'    LDzdh任务    '${ENV}'=='huawei.prod'    qcuser001    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01

    Set Global Variable    ${username}
    Set Global Variable    ${password}
    登录ATHENA平台    ${username}    ${password}
    租户切换    ${tenant}
    跳转到待办页    /todo/task
    js点击    //div[contains(text(),'我的任务')]
    js点击    //span[contains(text(),'筛选')]
    点击    //div[@class="filter-list-container ng-star-inserted"]/app-todo-comm-filter/div[1]
    Js点击        //span[contains(text(),'LDzdh任务')]
    Js点击         //div[contains(@class, 'todo-common-tool-container')]
    Sleep    2
    js点击    //span[contains(text(),'确定')]
    Js点击        //span[@class='card-item-name-title' and contains(text(), 'LDzdh任务')]
    输入  //input[@name="user_no"]').send_keys(text)    输入什么？？？？？
    点击  //div[@class='weihu-hot ng-star-inserted']

