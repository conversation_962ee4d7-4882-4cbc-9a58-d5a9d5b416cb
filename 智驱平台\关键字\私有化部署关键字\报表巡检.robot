*** Settings ***
Documentation     黄蕾
Library           SeleniumLibrary
Resource          ../业务关键字/TBB报表.robot
Resource          ../../元素/Athena平台元素.robot
Resource          ../../配置/全局参数.robot
Resource          报表巡检.robot
Resource          ../系统关键字/web.robot
Resource          ../../配置/Athena平台.robot
Resource          ../业务关键字/公共方法.robot

*** Keywords ***
ABI报表
    [Arguments]    ${work}
    查询需要的作业    ${work}
    Sleep    10
    点击    //div[@class="filter-condition-btn-list"]/button[2]/span
    sleep    10
    当前页面可见    //div[@class="custom-content-container"]/div[1]/div/span[contains( text(),'条件1')]
    点击    //span[contains( text(),'全屏')]
    点击    //span[contains( text(),'退出全屏')]
    sleep    5

TBB报表
    [Arguments]    ${work}
    查询需要的作业    ${work}
    Sleep    10
    全屏取消全屏
    报表筛选
    sleep    5

定制报表
    [Arguments]    ${work}
    查询需要的作业    ${work}
    Sleep    2
    点击    //div[@class="filter-condition-btn-list"]/button[2]/span
    sleep    2
    当前页面可见    //div[@class="bi-content-container"]/div[1]/div/span[contains( text(),'条件1')]
    点击    //span[contains( text(),'报表设计')]
    sleep    2
    当前页面可见字符    使用中
    点击    //a[contains( text(),'查看教学视频')]
    点击    //i[@class="anticon ant-modal-close-icon anticon-close ng-star-inserted"]
    sleep    2

报表筛选
    iframe选择
    点击    //div[@title="项目状态"]
    点击    //div[@class="filter-wrap"]/div/div[1]/div/dl/dd/div/div/ul/li[3]
    Sleep    2
    点击    //i[@class="a-ico ico-glob-filter-refresh"]
    Sleep    2
    当前页面可见字符    重置成功
