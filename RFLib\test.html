<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Server-Sent Events Image Display</title>
</head>

<body>
    <img id="realtimeImage" src="" alt="实时图片显示">
    <script>
        const eventSource = new EventSource('http://localhost:3000');
        eventSource.onmessage = function (event) {
            const base64Image = event.data;
            const imgElement = document.getElementById('realtimeImage');
            imgElement.src = 'data:image/jpeg;base64,' + base64Image;
        };
    </script>
</body>

</html>