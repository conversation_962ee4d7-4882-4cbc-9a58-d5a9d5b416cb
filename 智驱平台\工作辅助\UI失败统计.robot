*** Settings ***
Library           SeleniumLibrary
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/系统关键字/接口.robot
Resource          ../关键字/系统关键字/mysql.robot
Resource          ../配置/人员手机号码.robot
Resource          ../配置/DB.robot

*** Test Cases ***
UI失败用例统计
    [Documentation]    陈金明
    连接数据库    ${MSDB}
    Open Browser    http://172.16.2.154:9989/view/01预生产和生产冒烟回归/    headlesschrome    options=add_argument('--lang=zh-CN');add_argument('--no-sandbox');add_argument('--disable-gpu');add_argument('--disable-dev-shm-usage')
    ${ele_counts}    获取元素数量    //table[@id='projectstatus']//tbody//tr
    ${index}    Set Variable    1
    ${ele_counts}    Evaluate    ${ele_counts}+1
    ${webhook_url}    Set Variable    qyapi.weixin.qq.com
    WHILE    ${index}<${ele_counts}
        ${job}    获取元素属性值    //table[@id='projectstatus']//tbody/tr[${index}]    id
        ${status}    获取元素属性值    //table[@id='projectstatus']//tbody/tr[${index}]    class
        ${user}    获取元素字符    //table[@id='projectstatus']//tbody/tr[${index}]/td[9]
        IF    '${status}' == ' job-status-red'
            Log    '发送消息 xxx，你好，你的UI自动化用例 xxx 今日执行失败未处理，扣1分，你还剩xx分！'
            Log    '结果记录到数据库，包含主表和明细表'
            ${result}    执行sql[查]    select remaining_score from remaining_scores where person_name='${user}'
            ${score}    Set Variable    ${result[0][0]}
            ${score}    Evaluate    ${score}-1
            执行sql[增/删/改]    update remaining_scores set remaining_score=${score} where person_name='${user}'
            ${data}    Set Variable    {"msgtype":"text","text":{"content":"${user}，你好，你的UI自动化用例：【${job}】今日执行失败未处理，扣1分，你还有${score}分！！！","mentioned_mobile_list":${手机号码}[${user}]}}
            POST-UI    ${webhook_url}    cgi-bin/webhook/send?key=8d14b45b-29a2-421a-b709-2b411151c18a    ${data}
        END
        ${index}    Evaluate    ${index}+1
    END