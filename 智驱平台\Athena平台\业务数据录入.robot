*** Settings ***
Documentation     高伟
...               AND关闭浏览器
Suite Setup       Run Keywords    环境设定
Test Teardown     Run Keywords    错误处理    关闭浏览器
Resource          ../关键字/业务关键字/业务数据录入.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/业务关键字/智能入口.robot

*** Test Cases ***
基础资料作业打开串项目卡（PCC）
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${username}    ${password}
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    项目大事记
    串项目跳转
    关闭浏览器

回收站
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${key}    生成毫秒时间戳
    Set Global Variable    ${key}
    ${key1}    生成毫秒时间戳
    Set Global Variable    ${key1}
    ${plan}    生成毫秒时间戳
    Set Global Variable    ${plan}
    ${len}    生成毫秒时间戳
    Set Global Variable    ${len}
    登录ATHENA平台    ${username}    ${password}
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    查询所需作业    表格_双档作业测试
    新增单条数据    ${key}    ${key}    ${key}
    删除数据入回收站    ${key}
    回收站数据删除恢复    ${key}
    批量删除清空回收站
    批量删除数据至回收站    ${plan}    ${plan}    ${plan}    ${len}    ${key1}
    回收站数据批量恢复    ${plan}    ${len}
    关闭浏览器
