*** Settings ***
Documentation     高伟
Resource          ../关键字/业务关键字/作业授权.robot
Resource          ../关键字/业务关键字/基础数据录入.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/公共方法.robot
Test Setup        Run Keywords    环境设定
Test Teardown     Run Keywords    错误处理    AND    关闭浏览器



*** Test Cases ***
导入记录(单档多栏)-导入正常数据
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称    单档多栏的作业    多类作业功能测试
    数据导入    单档多栏的作业.xlsx
    点击查看记录
    返回业务数据录入菜单
    点击删除作业    101


导入记录(单档)-导入正常数据
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称    单档的作业    多类作业功能测试
    数据导入    单档的作业.xlsx
    点击查看记录
    返回业务数据录入菜单


导入记录(双档)-导入正常数据
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称    双档的作业    多类作业功能测试
    数据导入    双档的作业.xlsx
    点击查看记录
    返回业务数据录入菜单

导入记录(多档)-导入正常数据
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称    多档的作业    多类作业功能测试
    数据导入    多档的作业.xlsx
    点击查看记录
    返回业务数据录入菜单

导入导出中心
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入导入导出中心
    导入导出中心展示断言
    导入记录
    导出记录
    关闭浏览器

单档多栏-导入不符合格式的文件
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称    单档多栏的作业    多类作业功能测试
    数据导入    异常格式.docx

    
    
单档多栏-导入空内容文件
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称    单档多栏的作业    多类作业功能测试
    数据导入    单档多栏的作业-空.xlsx
    导入空文件判断


单档多栏-导入必填项为空的文件
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    进入业务数据录入菜单
    点击作业名称    单档多栏的作业    多类作业功能测试
    数据导入    单档多栏的作业-必填为空.xlsx
    点击查看记录
    查看异常页面