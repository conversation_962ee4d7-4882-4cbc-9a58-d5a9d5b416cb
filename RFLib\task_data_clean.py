#!/usr/local/bin/python3
# -*- coding: utf8 -*-
# Author: cjm

import requests
import json
import time
from typing import Dict, Any, List

# 配置信息
class Config:
    BASE_URL = "https://atmc-test.apps.digiwincloud.com.cn/api/atmc/v1/backlog/list"
    FIX_URL = "https://troubleshoot-test.apps.digiwincloud.com.cn/restful/standard/troubleshoot/api/scene/execute/fix"
    CLIENT_ID = "91A5042B081718453829FA813114D0C7"
    BASE_TOKEN = "52a82bf5-3cf4-458c-84e0-01e14dc6d0f6"
    FIX_TOKEN = "0ff5646a-e2e3-4f34-98b6-0d5fab83f42a"
    TENANT_ID = "AthenaAutoTest"
    RETRY_DELAY = 0.2  # 请求间隔时间(秒)
    MAX_RETRIES = 3  # 最大重试次数


# 请求头配置
headers = {
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:132.0) Gecko/20100101 Firefox/132.0",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Accept": "application/json, text/javascript, */*; q=0.01",
    "routerKey": "AthenaAutoTest"
}


def get_backlog_items() -> List[Dict[str, Any]]:
    """获取待处理的项目列表"""
    url = f"{Config.BASE_URL}?clientId={Config.CLIENT_ID}"
    headers_get = {**headers, "token": Config.FIX_TOKEN}

    try:
        response = requests.get(url, headers=headers_get, timeout=10)
        response.raise_for_status()  # 检查HTTP状态码
        return response.json().get('response', [])
    except requests.exceptions.RequestException as e:
        print(f"获取项目列表失败: {e}")
        return []


def close_backlog_item(item_id: str) -> bool:
    """关闭单个项目"""
    headers_post = {**headers, "token": Config.BASE_TOKEN}

    # 构建请求数据
    request_data = {
        "id": 381,
        "tenantId": Config.TENANT_ID,
        "repairRemark": "测试",
        "reqParams": [{"tenantId": Config.TENANT_ID, "backLogId": item_id}]
    }

    # 正确处理JSON嵌套结构
    encoded_params = json.dumps(request_data["reqParams"])
    request_data["reqParams"] = encoded_params

    for attempt in range(Config.MAX_RETRIES):
        try:
            response = requests.post(
                Config.FIX_URL,
                json=request_data,  # 使用json参数自动处理序列化
                headers=headers_post,
                timeout=15
            )
            response.raise_for_status()

            print(f"项目 {item_id} 关闭成功")
            print(f"响应: {response.json()}")
            return True

        except requests.exceptions.RequestException as e:
            print(f"尝试 {attempt + 1}/{Config.MAX_RETRIES}: 关闭项目 {item_id} 失败 - {e}")
            time.sleep(1 + attempt)  # 指数退避重试

    print(f"项目 {item_id} 关闭失败，已达到最大重试次数")
    return False


def main():
    """主函数：获取项目列表并关闭所有项目"""
    print("开始获取项目列表...")
    items = get_backlog_items()

    if not items:
        print("未找到待处理的项目")
        return

    print(f"找到 {len(items)} 个项目，开始处理...")

    success_count = 0
    for index, item in enumerate(items, 1):
        item_id = item.get('backlogId')
        if not item_id:
            print(f"跳过无效项目（ID缺失）: {item}")
            continue

        print(f"处理项目 {index}/{len(items)}: ID={item_id}")
        if close_backlog_item(item_id):
            success_count += 1

        # 控制请求频率
        time.sleep(Config.RETRY_DELAY)

    print(f"处理完成！成功: {success_count}, 失败: {len(items) - success_count}")


if __name__ == "__main__":
    main()