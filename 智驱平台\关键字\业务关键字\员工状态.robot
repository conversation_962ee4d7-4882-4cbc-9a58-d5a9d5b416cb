*** Settings ***
Library           SeleniumLibrary
Resource          ../系统关键字/web.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../系统关键字/接口.robot
Resource          Athena平台控件.robot
Resource          ../../元素/开发平台元素.robot
Resource          ../../配置/域名.robot
Resource          Athena平台.robot
Resource          ../../元素/Athena平台元素.robot

*** Keywords ***
应用发起侦测并返回发起时间
    [Arguments]    ${tenantName}    ${appName}    ${projectName}
    点击顶部菜单    全部
    点击右侧菜单    交付设计器
#    点击    xpath=(//button[@class='ant-drawer-close ng-star-inserted'])[1]
    Js点击   (//button[@class='ant-drawer-close ng-star-inserted'])[1]
    sleep    3
    选择浏览器窗体    -1
    当前页面若文本不存在自动刷新页面    3    请选择需要设定的运营单元
    sleep    5
    点击    //div[contains(text(),'${tenantName}')]
    sleep    30
    Scroll Element Into View    xpath=//div[contains(text(),'${appName}')]/../../div[2]/nz-spin/div/div/div
    sleep    6
    点击（文本）    ${appName}
    sleep    3
#    点击    //div[@class='fth-box ng-star-inserted'][1]
    点击    //*[@*[local-name()='href' and .='#iconguoduqingdan2']]
    sleep    3
    元素存在则点击    //*[contains(text(),'取消')]
    sleep    3
    ${start_time}    获取指定格式时间
    点击    //div[contains(text(),'${projectName}')]/span/img[@src='/assets/img/designer/execute.svg']
    sleep    3
    当前页面可见字符    发起成功
    RETURN    ${start_time}

#待作废
进入消息中心
#    元素存在则JS点击    //div[@class='all-menu-drawer ant-drawer-content-wrapper']//button[@class='ant-drawer-close ng-star-inserted'][@aria-label='Close']
    ${消息中心图标}    按环境获取元素定位    ${ENV}    //*[@class='icon font-entrance iconxiaoxi2']    //*[@class='icon font-entrance iconxiaoxi2']    //*[@class='icon font-entrance iconxiaoxi2']
    js点击    ${消息中心图标}
    点击    //li[contains(text(),' 消息 ')]
    ${visible}    判断元素是否可见    //*[contains(text(),'未读')]    10
    Run Keyword If    '${visible}'=='False'    Run Keywords    Reload Page
    ...    AND    点击    //div[@class='bell ant-dropdown-trigger']//ath-badge
    sleep    3
    当前页面若文本不存在自动刷新页面    3    未读
    关闭娜娜窗口

#待作废
消息中心轮询指定任务卡标识
    [Arguments]    ${max_count}    ${loc}    ${start_time}
    # 初始化等待计数为 0
    ${wait_count}    Set Variable    0
    ${cardNameKey}    Set Variable    ''
    # 开始循环，只要等待计数小于最大计数就继续循环
    WHILE    ${wait_count} < ${max_count}
        # 获取指定定位器的元素数量
        ${count}    获取元素数量    ${loc}
        # 如果元素数量大于 0，执行一系列操作
        Run Keyword If    ${count} > 0    Run Keywords
            ${element}    按顺序获取元素    ${loc}    0
            ${card_text}    Get Text    ${element}
            Log    ${card_text}
            ${split_list}    字符串分割    ${card_text}    场景名称：
            ${cardNameKey}    Set Variable    ${split_list}[1]
            ${time_difference}    Subtract Date From Date    ${start_time}    ${cardNameKey}
            # 如果时间差小于等于 300 秒，执行点击操作并退出循环
            Run Keyword If    ${time_difference} <= 300.0    Exit For Loop
            Log    Text not found, wait ${wait_count}
            # 等待计数加 1
            ${wait_count}    Evaluate    ${wait_count} + 1
            sleep    3
    # 结束循环
    END
    RETURN    ${cardNameKey}

#待作废
任务卡列表打开指定标识任务卡并检查详情标签
    [Arguments]    ${cardNameKey}    ${lab_check_flag}    ${loc}
    sleep    6
    点击    //span[contains(text(),'${cardNameKey}')]
    Run Keyword If    '${lab_check_flag} ' == 'true'    Run Keywords    当前页面可见元素    ${loc}
    sleep    10
    点击    //span[@class='ng-star-inserted'][contains(text(),'提交')]
    sleep    6
    点击    //span[@class='ng-star-inserted'][contains(text(),'确定')]
    sleep    6
    当前页面可见字符    -提交-
#待作废
消息中心轮询指定任务卡进入详情
    [Arguments]    ${max_count}    ${loc}    ${start_time}
    ${cardNameKey}    消息中心轮询指定任务卡标识    ${max_count}    ${loc}    ${start_time}
    ${消息中心任务详情链接}    按环境获取元素定位    ${ENV}    //*[contains(text(),'${cardNameKey}')]/../../../../div/span[contains(text(),'查看详情')]    //span[contains(text(),'${cardNameKey}')]/../../../../div/span[contains(text(),'查看详情')]    //*[contains(text(),'${cardNameKey}')]/../../../../div/span[contains(text(),'查看详情')]
    js点击    ${消息中心任务详情链接}
    RETURN    ${cardNameKey}
#待作废
消息中心轮询指定任务卡并提交
    [Arguments]    ${max_count}    ${loc}    ${start_time}
    ${cardNameKey}    消息中心轮询指定任务卡进入详情    ${max_count}    ${loc}    ${start_time}
    sleep    10
    点击    //span[@class='ng-star-inserted'][contains(text(),'提交')]
    sleep    6
    点击    //span[@class='ng-star-inserted'][contains(text(),'确定')]
    sleep    6
    当前页面可见字符    -提交-
    RETURN    ${cardNameKey}

#待作废
首页按待办类型搜索指定任务卡
    [Arguments]    ${taskType}    ${keyWord}
#    点击（文本）    首页
#    sleep    3
#    点击    //div[contains(text(),'待办')]
    跳转网页    /todo/task
    sleep    3
    点击    //div[contains(text(),'${taskType}')]
    sleep    3
    清空筛选
    点击    //div[@class='ath-search']
    sleep    3
    输入    xpath=//input[@placeholder='可用空格分隔关键词']    ${keyWord}
    Press Key    xpath=//input[@placeholder='可用空格分隔关键词']    \\13
    sleep    3
#待作废
元素存在则JS点击
    [Arguments]    ${loc}    ${timeout}=${timeout}
    ${element_exists}    Run Keyword And Return Status    当前页面可见    xpath=${loc}    ${timeout}
    Run Keyword If    ${element_exists}    js点击    ${loc}
#待作废
消息中心轮询指定时间内通知
    [Arguments]    ${max_count}    ${loc}    ${start_time}
    ${wait_count}    Set Variable    0
    ${flag}    Set Variable    False
    WHILE    ${wait_count} < ${max_count}
        ${count}    获取元素数量    ${loc}
        Run Keyword If    ${count}>0    Run Keywords
            ${element}    按顺序获取元素    ${loc}    0
            ${notice_text}    Get Text    ${element}
            log    ${notice_text}
            ${split_list}    字符串分割    ${notice_text}    \ 未读
            ${notice_current_time}    字符串替换    ${split_list}[0]    .    -
            Log    start_time=${start_time},notice_current_time=${notice_current_time}
            ${time_difference}    Subtract Date From Date    ${start_time}    ${notice_current_time}
            Run Keyword If    ${time_difference} <= 300.0    Run Keywords    Log    Text found, wait time ${time_difference}s
            ...    AND    Set Global Variable    ${flag}    True
            ...    AND    Exit For Loop
            Log    Text not found, wait ${wait_count}
            sleep    5
            ${wait_count}    Evaluate    ${wait_count} + 1
    END
    Should Be Equal    ${flag}    True    没有在指定时间内找到符合条件的文本

#待作废
任务卡/项目卡列表标签检查
    [Arguments]    ${cardType}    ${searchKey}    ${labName}
    首页按待办类型搜索指定任务卡    ${cardType}    ${searchKey}
    Run Keyword If    '${cardType}' == '我的任务'    Run Keyword    鼠标悬停    xpath=//*[@class='card-task-summary-container']
    Run Keyword If    '${cardType}' == '我的项目'    Run Keyword    鼠标悬停    xpath=//*[@class='card-project-summary-container']
    当前页面可见    xpath=//*[contains(text(), '${labName}')]
#待作废
按环境获取元素定位
    [Arguments]    ${ENV}    ${paas_e}    ${test_e}    ${prod_e}
    ${element}    Set Variable If    '${ENV}'=='paas'    ${paas_e}    '${ENV}'=='huawei.test'    ${test_e}    'prod' in '${ENV}'    ${prod_e}
    RETURN    ${element}

#by cjm
消息中心检查消息并进入任务卡详情页提交任务卡
    [Arguments]    ${msg}=null    ${monitor_time}=null
    Sleep    30
    ${time}    获取元素字符    xpath=(//span[contains(text(),'离职交接场景；场景名称：')])[1]
    Log    ${time}
    ${result}    Evaluate    '${time}'.split('：')    modules=sys
    Log    ${result}[1]
    #消息中最新的符合的消息，取出时间，如果大于侦测时间，则认为是侦测触发后发出的消息
    ${timestamp1}    Convert Date    ${result}[1]    result_format=epoch
    ${timestamp2}    Convert Date    ${CURRENT_TIME}    result_format=epoch
    ${is_greater}    Evaluate    ${timestamp1} > ${timestamp2}
    IF    ${is_greater}
        点击    xpath=(//span[contains(text(),'${result}[1]')]/following::span[@class='remind-message-go-detail'])[1]
        点击    //span[@class='ng-star-inserted'][contains(text(),'提交')]
        点击    //span[@class='ng-star-inserted'][contains(text(),'确定')]
        当前页面可见字符    -提交-
    END