*** Settings ***
Documentation     陈金明
Suite Setup       Run Keywords    环境设定
Test Teardown     Run Keywords    错误处理    AND    关闭浏览器
Resource          ../关键字/业务关键字/PCC.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/数据查询.robot
Resource          ../关键字/系统关键字/公共方法.robot

*** Test Cases ***
pcc全链路
    Comment    #当责者账号
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    #项目发起签核者
    ${projectusername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestSd01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${projectpassword}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestSd01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    Comment    #当责者/执行者名称,新建一级计划时需要按名称选择人员
    ${ownerUsernameCN}    Set Variable If    '${ENV}'=='huawei.test'    测试环境自动化测试智驱平台    '${ENV}'=='huawei.prod'    生产华为环境自动化测试KM    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试智驱平台    '${ENV}'=='muihuawei.test'    测试环境自动化测试智驱平台
    ${exUsernameCN}    Set Variable If    '${ENV}'=='huawei.test'    测试环境自动化测试SD    '${ENV}'=='huawei.prod'    生产华为环境自动化测试智驱入口    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试智驱入口    '${ENV}'=='muihuawei.test'    测试环境自动化测试SD
    Comment    #分享者账号
    ${sharingUsernameCN}    Set Variable If    '${ENV}'=='huawei.test'    测试环境自动化测试小AI入口    '${ENV}'=='huawei.prod'    生产华为环境自动化测试小AI入口    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试小AI入口    '${ENV}'=='muihuawei.test'    测试环境自动化测试小AI入口
    Comment    #执行者账号
    ${exUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestSd001
    ${exPassword}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestSd001
    Comment    #主管签核者账号
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestSd01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestSd01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    Comment    #加签人
    ${additionalSignatory}    Set Variable If    '${ENV}'=='huawei.test'    15722677434测试环境自动化测试SD    '${ENV}'=='huawei.prod'    15722677434生产华为环境自动化测试智驱入口    '${ENV}'=='microsoft.prod'    1591310641生产微软环境自动化测试智驱入口    '${ENV}'=='muihuawei.test'    15722677434测试环境自动化测试SD
    Comment    #加签人账号
    ${additionaUserName}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestSd001
    ${additionaUserPwd}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestSd001
    Comment    #项目卡分享人账号
    ${sharingUserName}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${sharingserPwd}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    Comment    #任务卡分享人账号
    ${TasksharingUserName}    Set Variable If    '${ENV}'=='huawei.test'    HL18271405997    '${ENV}'=='huawei.prod'    HL18271405997    '${ENV}'=='microsoft.prod'    HL18271405997    '${ENV}'=='muihuawei.test'    HL18271405997
    ${TasksharingserPwd}    Set Variable If    '${ENV}'=='huawei.test'    HuangL0920    '${ENV}'=='huawei.prod'    HuangL0920    '${ENV}'=='microsoft.prod'    HuangL0920    '${ENV}'=='muihuawei.test'    HuangL0920
    ${tenant}    Set Variable If    '${ENV}'=='pressure'     自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'     自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    Set Global Variable    ${projectname}    ${长唯一标识}
    Set Global Variable    ${taskname}    ${长唯一标识}
    登录Athena平台    ${ownerUsername}    ${ownerPassword}    tenant=${tenant}
    #首页点待办
    手动发起项目    ${projectname}    项目需签核    BB    3    2024/09/15
    关闭浏览器
    #发起项目签核不同意
    登录Athena平台    ${projectusername}    ${projectpassword}    tenant=${tenant}
    签核不同意    ${projectname}
    关闭浏览器
    登录Athena平台    ${ownerUsername}    ${ownerPassword}    tanent=${tenant}
    手动发起项目    ${projectname}    项目无需签核    BB    3    2024/09/15
    新建一级计划    ${projectname}    ${taskname}    2024/09/15    2024/09/15    ${ownerUsernameCN}    ${exUsernameCN}    需要签核
    启动项目
    #Comment    复制项目的分享链接    ${projectname}
#    项目卡添加至他人Athena    ${projectname}    ${sharingUsernameCN}
#    #登录被分享人账号
#    关闭浏览器
#    登录Athena平台    ${sharingUserName}    ${sharingserPwd}    tenant=${tenant}
#    分享项目卡详情展示    ${projectname}
##    关闭浏览器
##    登录Athena平台    ${sharingUserName}    ${sharingserPwd}
#    被分享项目卡    ${projectname}
    关闭浏览器
    #任务执行人报工
    登录Athena平台    ${exUsername}    ${exPassword}    tenant=${tenant}
    报工    ${taskname}
    关闭浏览器
#    #签核时退回重办，在详情内转派的场景中存在此用例，此处注释
#    登录Athena平台    ${username}    ${password}
#    退回重办    ${taskname}
#    关闭浏览器
#    登录Athena平台    ${exUsername}    ${exPassword}
#    报工    ${taskname}
#    关闭浏览器
    #主管签核并加签

#    登录Athena平台    ${username}    ${password}
#    加签    ${taskname}    ${additionalSignatory}
#    关闭浏览器
#    #加签签核
#    登录Athena平台    ${additionaUserName}    ${additionaUserPwd}
#    签核同意    ${taskname}
#    关闭浏览器
    #把主管的签核任务卡分享给他人

    #主管签核
    登录Athena平台    ${username}    ${password}    tenant=${tenant}
    签核同意    ${taskname}
    关闭浏览器
    登录Athena平台    ${ownerUsername}    ${ownerPassword}    tenant=${tenant}
    任务卡添加至他人Athena    hl_test
    关闭浏览器
    #被分享任务卡详情
    登录Athena平台    ${TasksharingUserName}    ${TasksharingserPwd}    tenant=${tenant}
    被分享的任务卡详情展示    ${taskname}
#    关闭浏览器
#    #被分享任务卡操作
#    登录Athena平台    ${TasksharingUserName}    ${TasksharingserPwd}
    被分享任务卡    ${taskname}
    #向前加签
    关闭浏览器
    登录Athena平台    ${ownerUsername}    ${ownerPassword}    tenant=${tenant}
    向前加签    ${taskname}    ${additionalSignatory}
    关闭浏览器
    #加签签核
    登录Athena平台    ${additionaUserName}    ${additionaUserPwd}    tenant=${tenant}
    签核同意    ${taskname}
    关闭浏览器
    #项目经理签核
    登录Athena平台    ${ownerUsername}    ${ownerPassword}    tenant=${tenant}
    项目详情里程碑切换    ${taskname}
    签核同意    ${taskname}
#    关闭浏览器
#    登录Athena平台    ${ownerUsername}    ${ownerPassword}
    结案    ${projectname}
    结案后任务卡信息查询

pcc全链路指定结案
    #当责者账号
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    #当责者/执行者名称,新建一级计划时需要按名称选择人员
    ${ownerUsernameCN}    Set Variable If    '${ENV}'=='huawei.test'    测试环境自动化测试智驱平台    '${ENV}'=='huawei.prod'    生产华为环境自动化测试KM    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试智驱平台    '${ENV}'=='muihuawei.test'    测试环境自动化测试智驱平台
    ${exUsernameCN}    Set Variable If    '${ENV}'=='huawei.test'    测试环境自动化测试SD    '${ENV}'=='huawei.prod'    生产华为环境自动化测试智驱入口    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试智驱入口    '${ENV}'=='muihuawei.test'    测试环境自动化测试SD
    #执行者账号
    ${exUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestSd001
    ${exPassword}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestSd001
    #主管签核者账号
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestSd01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestSd01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    #加签人
    ${additionalSignatory}    Set Variable If    '${ENV}'=='huawei.test'    15722677434测试环境自动化测试SD    '${ENV}'=='huawei.prod'    15722677434生产华为环境自动化测试智驱入口    '${ENV}'=='microsoft.prod'    1591310641生产微软环境自动化测试智驱入口    '${ENV}'=='muihuawei.test'    15722677434测试环境自动化测试SD
    #加签人账号
    ${additionaUserName}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestSd001
    ${additionaUserPwd}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestSd001
    Set Global Variable    ${projectname}    ${长唯一标识}
    Set Global Variable    ${taskname}    ${长唯一标识}
    登录Athena平台    ${ownerUsername}    ${ownerPassword}
    #    手动发起项目    ${projectname}    项目无需签核    BB    3    2024/09/15
    新建一级计划    ${projectname}    ${taskname}    2024/09/15    2024/09/15    ${ownerUsernameCN}    ${exUsernameCN}    需要签核
    启动项目
    关闭浏览器
    登录Athena平台    ${exUsername}    ${exPassword}
    报工    ${taskname}
    关闭浏览器
    #签核时退回重办
    登录Athena平台    ${username}    ${password}
    退回重办    ${taskname}
    关闭浏览器
    登录Athena平台    ${exUsername}    ${exPassword}
    报工    ${taskname}
    关闭浏览器
    #主管签核并加签
    登录Athena平台    ${username}    ${password}
    加签    ${taskname}    ${additionalSignatory}
    关闭浏览器
    #加签签核
    登录Athena平台    ${additionaUserName}    ${additionaUserPwd}
    签核同意    ${taskname}
    关闭浏览器
    #项目经理签核
    登录Athena平台    ${ownerUsername}    ${ownerPassword}
    签核同意    ${taskname}
    关闭浏览器
    登录Athena平台    ${ownerUsername}    ${ownerPassword}
    暂停项目    ${projectname}
    继续项目    ${projectname}
    指定结案    ${projectname}

pcc项目和任务批量转派
    #场景
    #TestAthenaAutoTestAi001发起项目,新建任务卡(执行人TestAthenaAutoTestSd001),启动项目,创建任务卡(TestAthenaAutoTestAi001)
    #当责者账号/项目经理签核账号
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestSd01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestSd01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    #当责者,新建一级计划时需要按名称选择人员
    ${ownerUsernameCN}    Set Variable If    '${ENV}'=='huawei.test'    测试环境自动化测试智驱平台    '${ENV}'=='huawei.prod'    生产华为环境自动化测试KM    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试智驱平台    '${ENV}'=='muihuawei.test'    测试环境自动化测试智驱平台
    #执行者名称,新建一级计划时需要按名称选择人员
    ${exUsernameCN}    Set Variable If    '${ENV}'=='huawei.test'    测试环境自动化测试SD    '${ENV}'=='huawei.prod'    生产华为环境自动化测试智驱入口    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试智驱入口    '${ENV}'=='muihuawei.test'    测试环境自动化测试SD
    #执行者账号
    ${exUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestSd001
    ${exPassword}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestSd001
    #转派后主管签核者账号
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    #加签人
    ${additionalSignatory}    Set Variable If    '${ENV}'=='huawei.test'    15722677434测试环境自动化测试SD    '${ENV}'=='huawei.prod'    18120123135生产华为环境自动化测试智驱平台    '${ENV}'=='microsoft.prod'    1591310641生产微软环境自动化测试智驱入口    '${ENV}'=='muihuawei.test'    15722677434测试环境自动化测试SD
    #加签人账号
    ${additionaUserName}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestSd001
    ${additionaUserPwd}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestSd001
    #转派人
    ${reassignUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestKm01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestKm001
    ${reassignUsernamepwd}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestKm01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestKm001
    Set Global Variable    ${projectname}    ${长唯一标识}
    Set Global Variable    ${taskname}    ${长唯一标识}
    登录Athena平台    ${ownerUsername}    ${ownerPassword}
    手动发起项目    ${projectname}    项目无需签核    BB    3    2024/09/15
    新建一级计划    ${projectname}    ${taskname}    2024/09/15    2024/09/15    ${ownerUsernameCN}    ${exUsernameCN}    需要签核
    启动项目
    批量项目转派    ${projectname}    ${reassignUsername}
    关闭浏览器
    #任务执行者转派任务卡
    登录Athena平台    ${exUsername}    ${exPassword}
    批量任务转派    ${taskname}    ${reassignUsername}
    Comment    复制项目的分享链接    ${projectname}
    关闭浏览器
    #被转派人报工
    登录Athena平台    ${reassignUsername}    ${reassignUsernamepwd}
    报工    ${taskname}
    关闭浏览器
    #主管签核时退回重办(还是转派前人员的主管)
    登录Athena平台    ${username}    ${password}
    退回重办    ${taskname}
    关闭浏览器
    登录Athena平台    ${reassignUsername}    ${reassignUsernamepwd}
    报工    ${taskname}
    关闭浏览器
    #主管签核并加签
    登录Athena平台    ${username}    ${username}
    加签    ${taskname}    ${additionalSignatory}
    关闭浏览器
    #加签签核
    登录Athena平台    ${additionaUserName}    ${additionaUserPwd}
    签核同意    ${taskname}
    关闭浏览器
    #如果任务卡被转派,为被转派的人签核
    #项目经理签核
    登录Athena平台    ${reassignUsername}    ${reassignUsernamepwd}
    签核同意    ${taskname}
    #    关闭浏览器
    #    登录Athena平台    ${reassignUsername}    ${reassignUsernamepwd}
    暂停项目    ${projectname}
    继续项目    ${projectname}
    指定结案    ${projectname}

pcc项目和任务转派
   #场景
    #角色：当责者，任务执行者，主管、项目经理、被转派者、知会者
    #TestAthenaAutoTestAi001发起项目,新建任务卡(执行人TestAthenaAutoTestSd001),启动项目,创建任务卡(TestAthenaAutoTestAi001)
    #当责者账号/项目经理签核账号
    ${ownerUsername}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestSd01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${ownerPassword}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestSd01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    #当责者,新建一级计划时需要按名称选择人员
    ${ownerUsernameCN}    Set Variable If    '${ENV}'=='pressure'    测试环境自动化测试SD    '${ENV}'=='huawei.test'    测试环境自动化测试SD    '${ENV}'=='huawei.prod'    生产华为环境自动化测试KM    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试KM    '${ENV}'=='muihuawei.test'    测试环境自动化测试智驱平台
    #执行者名称,新建一级计 划时需要按名称选择人员
    ${exUsernameCN}    Set Variable If    '${ENV}'=='pressure'    测试环境自动化测试智驱平台    '${ENV}'=='huawei.test'    测试环境自动化测试智驱平台    '${ENV}'=='huawei.prod'    生产华为环境自动化测试智驱入口    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试智驱入口    '${ENV}'=='muihuawei.test'    测试环境自动化测试SD
    #执行者账号
    ${exUsername}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestSd001
    ${exPassword}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestSd001
    #转派后主管签核者账号
    ${username}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${password}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    #加签人
    ${additionalSignatory}    Set Variable If    '${ENV}'=='pressure'    15722677434测试环境自动化测试SD    '${ENV}'=='huawei.test'    15722677434测试环境自动化测试SD    '${ENV}'=='huawei.prod'    18120123135生产华为环境自动化测试智驱平台    '${ENV}'=='microsoft.prod'    1591310641生产微软环境自动化测试智驱入口    '${ENV}'=='muihuawei.test'    15722677434测试环境自动化测试SD
    #加签人账号
    ${additionaUserName}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestSd001
    ${additionaUserPwd}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestSd001
    #转派人
    ${reassignUsername}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestKm01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestKm001
    ${reassignUsernamepwd}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestKm01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestKm001
    #知会人(个人)
    ${notifier}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestKm01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestKm001
    ${notifierPwd}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestKm01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestKm001
    #项目/任务来源
    ${return}    Set Variable If    '${ENV}'=='pressure'    由测试环境自动化测试小AI平台转派    '${ENV}'=='huawei.test'    由测试环境自动化测试小AI平台转派    '${ENV}'=='huawei.prod'    由生产华为环境自动化测试小AI入口转派    '${ENV}'=='microsoft.prod'    由生产微软环境自动化测试小AI入口转派    '${ENV}'=='muihuawei.test'    由测试环境自动化测试SD转派
    #    #手动任务签核知会人(个人)
    #    ${taskNotifier}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestKm01
    #    ${taskNotifierPwd}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestKm01
    ${tenant}    Set Variable If    '${ENV}'=='pressure'     自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'     自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境    
       
    Set Global Variable    ${projectname}    ${长唯一标识}
    #前置任务
    Set Global Variable    ${pretaskname}    前置${长唯一标识}
    #后置任务
    Set Global Variable    ${posttaskname}    后置${长唯一标识}
    登录Athena平台    ${ownerUsername}    ${ownerPassword}    当责者    tenant=${tenant}
    手动发起项目    ${projectname}    项目无需签核    BB    3    2024/09/15
    新建一级计划    ${projectname}    ${pretaskname}    2024/09/15    2024/09/15    ${ownerUsernameCN}    ${exUsernameCN}    无需签核
#    新建一级计划    ${projectname}    ${posttaskname}    2024/09/15    2024/09/15    ${ownerUsernameCN}    ${exUsernameCN}    需要签核
#    #此处加上任务的修改，删除和查看的场景
#    修改任务    ${posttaskname}
#    查看任务    ${posttaskname}修改
#    删除任务    ${posttaskname}修改
    新建一级计划    ${projectname}    ${posttaskname}    2024/09/15    2024/09/15    ${ownerUsernameCN}    ${exUsernameCN}    需要签核
    #启动项目
    启动项目
#    关闭浏览器
    #项目启动知会
    登录Athena平台    ${notifier}    ${notifierPwd}    知会人    tenant=${tenant}
    新项目知会
#    登录Athena平台    ${ownerUsername}    ${ownerPassword}
    切换浏览器    当责者
    项目转派    ${projectname}    ${reassignUsername}
#    关闭浏览器
    #任务执行者转派任务卡
    登录Athena平台    ${exUsername}    ${exPassword}
    #前置任务报工
    报工    ${pretaskname}    New
    #后置任务转派
    任务转派    ${posttaskname}    ${reassignUsername}
    Comment    复制项目的分享链接    ${projectname}
#    关闭浏览器
    #被转派人报工
    登录Athena平台    ${reassignUsername}    ${reassignUsernamepwd}    转派后owner和任务执行人    tenant=${tenant}
    项目转派后断言    ${projectname}    转派
    报工后撤回    ${posttaskname}
    报工    ${posttaskname}
#    关闭浏览器
    #签核前知会
#    登录Athena平台    ${notifier}    ${notifierPwd}
    切换浏览器    知会人
    签核前知会
#    关闭浏览器
    #主管签核时退回重办
    登录Athena平台    ${username}    ${password}    主管    tenant=${tenant}
    退回重办    ${posttaskname}
#    关闭浏览器
    #新建任务卡，责任人a，执行人b，转派执行人给c，c报工，x签核时退回重办，卡退给a和b，不会退给c
#    登录Athena平台    ${reassignUsername}    ${reassignUsernamepwd}
    切换浏览器    转派后owner和任务执行人
    报工    ${posttaskname}    退回
#    关闭浏览器
    #主管签核并加签
#    登录Athena平台    ${username}    ${username}
    切换浏览器    主管
    加签    ${posttaskname}    ${additionalSignatory}
#    关闭浏览器
    #加签签核时退回重签
    登录Athena平台    ${additionaUserName}    ${additionaUserPwd}    加签人    tenant=${tenant}
    退回重签    ${posttaskname}
#    关闭浏览器
    #退回重签后主管签核并加签
#    登录Athena平台    ${username}    ${username}
    切换浏览器    主管
    加签    ${posttaskname}    ${additionalSignatory}    退回重签
#    关闭浏览器
    #加签签核
#    登录Athena平台    ${additionaUserName}    ${additionaUserPwd}
    切换浏览器    加签人
    签核同意    ${posttaskname}    加签
#    关闭浏览器
    #签核完成后知会
#    登录Athena平台    ${notifier}    ${notifierPwd}
    切换浏览器    知会人
    签核后知会
#    关闭浏览器
    #如果任务卡被转派,为被转派的人签核
    #项目经理签核
#    登录Athena平台    ${reassignUsername}    ${reassignUsernamepwd}
    切换浏览器    转派后owner和任务执行人
#    ${role}    Create List    发起人    知会 直属主管签核    直属主管签核    发起人    知会 直属主管签核    直属主管签核    项目经理签核
#    ${user}    Create List
#    签核进度
    签核同意    ${posttaskname}
    签核进度
    暂停项目    ${projectname}
    继续项目    ${projectname}
    指定结案    ${projectname}
    点击顶部菜单    全部
    点击右侧菜单    数据查询
    Wait Until Keyword Succeeds    2x    3s    按输入历史项目和任务查询条件查询    ${projectname}    project_name=项目中控台    card_type=任务
    历史项目/任务查询数据校验    任务    ${projectname}    手动任务    项目中控台    逾时
    历史项目/任务查询转派记录    ${return}

pcc项目删除
    #当责者账号/项目经理签核账号
    ${username}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestSd01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${password}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestSd01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    Comment    #当责者/执行者名称,新建一级计划时需要按名称选择人员
    ${ownerUsernameCN}    Set Variable If    '${ENV}'=='huawei.test'    测试环境自动化测试智驱平台    '${ENV}'=='huawei.prod'    生产华为环境自动化测试KM    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试智驱平台    '${ENV}'=='muihuawei.test'    测试环境自动化测试智驱平台
    ${exUsernameCN}    Set Variable If    '${ENV}'=='huawei.test'    测试环境自动化测试SD    '${ENV}'=='huawei.prod'    生产华为环境自动化测试智驱入口    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试智驱入口    '${ENV}'=='muihuawei.test'    测试环境自动化测试SD
    Comment    #分享者账号
    ${sharingUsernameCN}    Set Variable If    '${ENV}'=='huawei.test'    测试环境自动化测试小AI入口    '${ENV}'=='huawei.prod'    生产华为环境自动化测试小AI入口    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试小AI入口    '${ENV}'=='muihuawei.test'    测试环境自动化测试小AI入口
    Comment    #项目卡分享人账号
    ${sharingUserName}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${sharingserPwd}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002


    ${tenant}    Set Variable If    '${ENV}'=='pressure'     自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'     自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    Set Global Variable    ${projectname}    ${长唯一标识}
    #前置任务
    Set Global Variable    ${pretaskname}    前置${长唯一标识}
    #后置任务
    Set Global Variable    ${posttaskname}    后置${长唯一标识}
    登录Athena平台    ${username}    ${password}    tenant=${tenant}
    手动发起项目    ${projectname}    项目无需签核    BB    3    2024/09/15
    新建一级计划    ${projectname}    ${pretaskname}    2024/09/15    2024/09/15    ${ownerUsernameCN}    ${exUsernameCN}    无需签核
    #此处加上任务的修改，删除和查看的场景
    修改任务    ${pretaskname}
    查看任务    ${pretaskname}修改
    删除任务    ${pretaskname}修改
    项目卡添加至他人Athena    ${projectname}    ${sharingUsernameCN}
    #登录被分享人账号
    关闭浏览器
    登录Athena平台    ${sharingUserName}    ${sharingserPwd}    tenant=${tenant}
    #被分享的卡只能看不能操作
    分享项目卡详情展示    ${projectname}
#    关闭浏览器
#    登录Athena平台    ${sharingUserName}    ${sharingserPwd}
    被分享项目卡    ${projectname}
    关闭浏览器
    登录Athena平台    ${username}    ${password}    tenant=${tenant}
    项目删除    ${projectname}

项目中控台子母项目
    #场景
    #TestAthenaAutoTestAi001发起项目,新建任务卡(执行人TestAthenaAutoTestSd001),启动项目,创建任务卡(TestAthenaAutoTestAi001)
    #当责者账号/项目经理签核账号
    ${ownerUsername}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestSd01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${ownerPassword}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestSd01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    #当责者,新建一级计划时需要按名称选择人员
    ${ownerUsernameCN}    Set Variable If    '${ENV}'=='pressure'    测试环境自动化测试智驱平台    '${ENV}'=='huawei.test'    测试环境自动化测试智驱平台    '${ENV}'=='huawei.prod'    生产华为环境自动化测试SD    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试智驱平台    '${ENV}'=='muihuawei.test'    测试环境自动化测试智驱平台
    #执行者名称,新建一级计划时需要按名称选择人员
    ${exUsernameCN}    Set Variable If    '${ENV}'=='pressure'    测试环境自动化测试SD    '${ENV}'=='huawei.test'    测试环境自动化测试SD    '${ENV}'=='huawei.prod'    生产华为环境自动化测试智驱入口    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试智驱入口    '${ENV}'=='muihuawei.test'    测试环境自动化测试SD
    #执行者账号
    ${exUsername}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestSd001
    ${exPassword}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestSd001
    #转派后主管签核者账号
    ${username}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${password}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi002    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    #加签人
    ${additionalSignatory}    Set Variable If    '${ENV}'=='huawei.test'    15722677434测试环境自动化测试SD    '${ENV}'=='huawei.prod'    18120123135生产华为环境自动化测试智驱平台    '${ENV}'=='microsoft.prod'    1591310641生产微软环境自动化测试智驱入口    '${ENV}'=='muihuawei.test'    15722677434测试环境自动化测试SD
    #加签人账号
    ${additionaUserName}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestSd001
    ${additionaUserPwd}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestSd001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestSd001
    #转派人
    ${reassignUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestKm01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestKm001
    ${reassignUsernamepwd}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestKm01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestKm001
    #知会人(个人)
    ${notifier}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestKm01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestKm001
    ${notifierPwd}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestKm01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestKm001
    #项目/任务来源
    ${return}    Set Variable If    '${ENV}'=='huawei.test'    由测试环境自动化测试小AI平台转派    '${ENV}'=='huawei.prod'    由生产华为环境自动化测试小AI入口转派    '${ENV}'=='microsoft.prod'    由生产微软环境自动化测试小AI入口转派    '${ENV}'=='muihuawei.test'    由测试环境自动化测试SD转派
    #    #手动任务签核知会人(个人)
    #    ${taskNotifier}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestKm01
    #    ${taskNotifierPwd}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestKm01
    ${tenant}    Set Variable If    '${ENV}'=='pressure'     自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'     自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境        
    ${time}    生成秒时间戳
    Set Global Variable    ${program}    ${time}
    Set Global Variable    ${taskname}    ${time}
    登录Athena平台    ${ownerUsername}    ${ownerPassword}    tenant=${tenant}
    子母项目发起项目    ${program}    项目无需签核    AA    2024/09/15    2024/09/17
    #    常规条件筛选    项目中控台字母项目
    新建一级计划    子项目1-${program}    1-${taskname}    2024/09/15    2024/09/15    ${exUsernameCN}    ${ownerUsernameCN}    无需签核
    Sleep    4
    启动项目
    新建一级计划    子项目2-${program}    2-${taskname}    2024/09/15    2024/09/15    ${exUsernameCN}    ${ownerUsernameCN}    无需签核
    启动项目
    报工    1-${taskname}    New
    报工    2-${taskname}    New
    #业务逻辑，需要2分钟下发项目
    Sleep    120
    指定结案    子项目1-${program}
    指定结案    子项目2-${program}
    子母项目结案    ${program}
    点击顶部菜单    全部
    点击右侧菜单    数据查询
    Wait Until Keyword Succeeds    2x    3s    按输入历史项目和任务查询条件查询    ${program}    project_name=项目中控台    card_type=任务    scheduled_start_time=2024/09/15    scheduled_end_time=2024/09/17
    当前页面可见字符    2024/09/15 - 2024/09/17

pcc异常任务卡
    #场景
    #角色：当责者，任务执行者，主管、项目经理、被转派者、知会者
    #TestAthenaAutoTestAi001发起项目,新建任务卡(执行人TestAthenaAutoTestSd001),启动项目,创建任务卡(TestAthenaAutoTestAi001)
    #当责者账号/项目经理签核账号
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    HL18271405997    '${ENV}'=='huawei.prod'    HL18271405997    '${ENV}'=='microsoft.prod'    HL18271405997    '${ENV}'=='muihuawei.test'    HL18271405997
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    HuangL0920    '${ENV}'=='huawei.prod'    HuangL0920    '${ENV}'=='microsoft.prod'    HuangL0920    '${ENV}'=='muihuawei.test'    HuangL0920
    #当责者,新建一级计划时需要按名称选择人员
    ${ownerUsernameCN}    Set Variable If    '${ENV}'=='pressure'    测试环境自动化测试SD    '${ENV}'=='huawei.test'    豆渣    '${ENV}'=='huawei.prod'    豆渣    '${ENV}'=='microsoft.prod'    hl_test    '${ENV}'=='muihuawei.test'    hl_test
    #执行者名称,新建一级计划时需要按名称选择人员
    ${exUsernameCN}    Set Variable If    '${ENV}'=='pressure'    测试环境自动化测试智驱平台    '${ENV}'=='huawei.test'    陈金明    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    黄蕾    '${ENV}'=='muihuawei.test'    黄蕾
    ${tenant}    Set Variable If    '${ENV}'=='pressure'     自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'     智驱中台工作台    '${ENV}'=='huawei.prod'    智驱中台工作台    '${ENV}'=='microsoft.prod'    智驱中台工作台
    #    #手动任务签核知会人(个人)
    #    ${taskNotifier}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestKm01
    #    ${taskNotifierPwd}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestKm01
    Set Global Variable    ${projectname}    ${长唯一标识}
    #前置任务
    Set Global Variable    ${pretaskname}    ${长唯一标识}
    #后置任务
    Set Global Variable    ${posttaskname}    ${长唯一标识}
    登录Athena平台    ${ownerUsername}    ${ownerPassword}    当责者    tenant=${tenant}
    手动发起项目    ${projectname}    项目无需签核    BB    3    2024/09/15
    新建一级计划    ${projectname}    ${pretaskname}    2024/09/15    2024/09/15    ${ownerUsernameCN}    ${exUsernameCN}    无需签核
    启动项目
    协同计划排定
    #新建一级计划    ${projectname}    ${posttaskname}    2024/09/15    2024/09/15    ${ownerUsernameCN}    ${exUsernameCN}    无需签核
    #关闭浏览器
    ${title}    Get Title
    点击顶部菜单    全部
    点击右侧菜单    交付设计器
    ${title}    Get Title
    选择浏览器窗体    -1
    交付设计器发起侦测
    #切回Ahtena
    选择窗体    ${title}
    计划时程异常项目卡检查    ${projectname}
    协同计划排定任务卡提交    ${pretaskname}
    协同计划时程异常任务卡提交    协同计划时程异常${pretaskname}
    协同计划时程异常任务卡提交    计划时程异常${pretaskname}
    报工    ${pretaskname}
    结案    ${projectname}

pcc自定义签核
    #场景
    #角色：当责者，任务执行者，主管、项目经理、被转派者、知会者
    #TestAthenaAutoTestAi001发起项目,新建任务卡(执行人TestAthenaAutoTestSd001),启动项目,创建任务卡(TestAthenaAutoTestAi001)
    #当责者账号/项目经理签核账号
    ${username}    Set Variable    HL18271405997
    ${password}    Set Variable    HuangL0920
    #签核者1-职能
    ${approver_role}    Set Variable    qcsupplier001
    ${approver_role_pwd}    Set Variable    supplier001
    #签核者2-指定人员
    ${approver_specified}    Set Variable    qcsupplier001
    ${approver_Specified_pwd}    Set Variable    supplier001
    #签核者2-直属主管
    ${approver_manager}    Set Variable    qcsupplier001
    ${approver_manager_pwd}    Set Variable    supplier001
    #签核者2-部门主管
    ${approver_dept_manager}    Set Variable    qcsupplier001
    ${approver_dept_manager_pwd}    Set Variable    supplier001
    #签核者2-发起人部门主管
    ${approver_owner_manager}    Set Variable    qcsupplier001
    ${approver_owner_manager_pwd}    Set Variable    supplier001
    #当责者,新建一级计划时需要按名称选择人员
    ${ownerUsernameCN}    Set Variable If    '${ENV}'=='pressure'    测试环境自动化测试SD    '${ENV}'=='huawei.test'    测试环境自动化测试SD    '${ENV}'=='huawei.prod'    生产华为环境自动化测试KM    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试KM    '${ENV}'=='muihuawei.test'    测试环境自动化测试智驱平台
    #执行者名称,新建一级计 划时需要按名称选择人员
    ${exUsernameCN}    Set Variable If    '${ENV}'=='pressure'    测试环境自动化测试智驱平台    '${ENV}'=='huawei.test'    测试环境自动化测试智驱平台    '${ENV}'=='huawei.prod'    生产华为环境自动化测试智驱入口    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试智驱入口    '${ENV}'=='muihuawei.test'    测试环境自动化测试SD
    #执行者账号
    ${exUsername}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestSd001
    ${exPassword}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi02    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi02    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestSd001
    #租户信息
    ${tenant}    Set Variable    智驱中台工作台
    Set Global Variable    ${projectname}    ${长唯一标识}
    #前置任务
    Set Global Variable    ${pretaskname}    前置${长唯一标识}
    #后置任务
    Set Global Variable    ${posttaskname}    后置${长唯一标识}
    登录Athena平台    ${username}    ${password}    tenant=${tenant}
    手动发起项目    ${projectname}    项目需签核    AA    3    2024/09/15
    关闭浏览器
    登录Athena平台    ${approver_role}    ${approver_role_pwd}    tenant=${tenant}
    #职能签核
    签核同意    ${projectname}
    #以下签核人为同一个人，省略登录的步骤
    #指定人员签核
    签核同意    ${projectname}
    #直属主管签核
    签核同意    ${projectname}
    #部门主管签核
    签核同意    ${projectname}
    关闭浏览器
    #项目当责者签核
    登录Athena平台    ${username}    ${password}    tenant=${tenant}
    签核同意    ${projectname}
    关闭浏览器
    #启动项目
    登录Athena平台    ${username}    ${password}    tenant=${tenant}
    跳转网页    /todo/project
    任务/项目名称搜索    ${projectname}
    点击卡片    ${projectname}
    启动项目
    关闭浏览器
    登录Athena平台    ${approver_owner_manager}    ${approver_owner_manager_pwd}    tenant=${tenant}
    签核同意    ${projectname}
    关闭浏览器
    #等待任务下发，需要 5分钟
    Sleep    230
    #结案
    登录Athena平台    ${username}    ${password}    tenant=${tenant}
    结案    ${projectname}    进行中
    关闭浏览器
    登录Athena平台    ${approver_manager}    ${approver_manager_pwd}    tenant=${tenant}
    启动后变更签核同意    ${projectname}    text=启动后项目状态变更签核

任务清理
    #场景
    #角色：当责者，任务执行者，主管、项目经理、被转派者、知会者
    #TestAthenaAutoTestAi001发起项目,新建任务卡(执行人TestAthenaAutoTestSd001),启动项目,创建任务卡(TestAthenaAutoTestAi001)
    #当责者账号/项目经理签核账号
    ${ownerUsername}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    HL18271405997    '${ENV}'=='huawei.prod'    HL18271405997    '${ENV}'=='microsoft.prod'    HL18271405997    '${ENV}'=='muihuawei.test'    HL18271405997
    ${ownerPassword}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    HuangL0920    '${ENV}'=='huawei.prod'    HuangL0920    '${ENV}'=='microsoft.prod'    HuangL0920    '${ENV}'=='muihuawei.test'    HuangL0920
    ${tenant}    Set Variable If    '${ENV}'=='pressure'     自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'     智驱中台工作台    '${ENV}'=='huawei.prod'    智驱中台工作台    '${ENV}'=='microsoft.prod'    智驱中台工作台

    #    #手动任务签核知会人(个人)
    #    ${taskNotifier}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestKm01
    #    ${taskNotifierPwd}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestKm001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestKm01
    登录Athena平台    ${ownerUsername}    ${ownerPassword}    tenant=${tenant}
    跳转网页    /todo/task
    ${condition}    Set Variable    ${True}
    WHILE    ${condition}
        点击    (//div[@class='todo-card-item-top-container'])[${index}]
        提交任务卡
        关闭指定标签页    项目/任务详情
        Sleep    6
    END