*** Settings ***
Documentation     陈金明
Resource          ../关键字/业务关键字/项目授权.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/鼎捷云.robot
Resource          ../关键字/业务关键字/公共方法.robot
Test Setup        Run Keywords    环境设定
Test Teardown     Close Browser

*** Variables ***
${username}    HL18271405997
${password}    HuangL0920
${username_athena}    qcsupplier001
${passwor_athena}    supplier001


*** Test Cases ***
项目权限配置
    ${tenant}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'     自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    Set Selenium Implicit Wait    0 seconds
    登陆鼎捷云    ${username}    ${password}
    点击左侧菜单    权限管理
    权限配置    ${username_athena}
    登录Athena平台    ${username_athena}    ${passwor_athena}    tenant=${tenant}
    点击顶部菜单    全部
    点击左侧菜单    发起项目
    当前页面不存在元素    //span[@class='card-title'][text()='项目中控台']