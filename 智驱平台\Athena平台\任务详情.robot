*** Settings ***
Documentation     质量组
Suite Setup       Run Keywords    环境设定
Test Teardown     Run Keywords    错误处理    关闭浏览器
Resource          ../关键字/业务关键字/智能入口.robot
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/业务关键字/待办.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/顺序签核.robot

*** Test Cases ***
待办-任务项目
    ${username}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${password}    Set Variable If    '${ENV}'=='pressure'    TestAthenaAutoTestAi001    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi001
    ${task}    Set Variable If    '${ENV}'=='pressure'    15062205158    '${ENV}'=='paas'    15062205158    '${ENV}'=='huawei.test'    1735802957    '${ENV}'=='huawei.prod'    1735264018    '${ENV}'=='microsoft.prod'    1735196439
    ${tasktype}    Set Variable If    '${ENV}'=='pressure'    职能签核    '${ENV}'=='paas'    采购签核    '${ENV}'=='huawei.test'    职能签核    '${ENV}'=='huawei.prod'    手动任务签核    '${ENV}'=='microsoft.prod'    手动任务签核
    ${tenant}    Set Variable If    '${ENV}'=='pressure'     自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'     自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境    
   
    Set Global Variable    ${username}
    Set Global Variable    ${password}
    登录ATHENA平台    ${username}    ${password}    tenant=${tenant}
    跳转网页    /todo/task
    Sleep    2
    打开任务详情列表
    切换任务与任务详情
    [Teardown]    Run Keywords    关闭浏览器
