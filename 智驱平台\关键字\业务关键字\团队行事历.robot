*** Settings ***
Library           SeleniumLibrary
Library           Collections
Library           OperatingSystem
Resource          ../系统关键字/web.robot
Resource          ../系统关键字/公共方法.robot

*** Keywords ***
创建团队行事历（不修改名称）
    点击    //h3[contains(text(),'行事历')]/following-sibling::i
    Sleep    10
    # ${a}    生成毫秒时间戳
    # 自定义行事历关键内容    ${a}
    Execute Javascript    document.querySelector('.ant-checkbox-input').click()
    点击    //button[@class='ath-btn ant-btn ant-btn-primary']
    Sleep    20

自定义行事历关键内容
    [Arguments]    ${Key}
    Sleep    30
    输入    //section[@class='ant-input-group ath-input-group-wrap-inner ath-inner-label-ing']//input[@class='ant-input ath-input ng-untouched ng-pristine ng-valid ng-star-inserted']    ${Key}

创建团队行事历重复提示
    [Arguments]    ${Sure}
    点击    //span[contains(text(),'${Sure}')]

创建团队行事历（修改名称）
    点击    //h3[contains(text(),'行事历')]/following-sibling::i
    Sleep    10
    Comment    Clear Element Text    //div[@class='ath-input-group-cocoon ath-input-inner-label']
    ${HLName}    生成毫秒时间戳
    Set Global Variable    ${HLName}
    自定义行事历关键内容    ${HLName}
    Execute Javascript    document.querySelector('.ant-checkbox-input').click()
    点击    //button[@class='ath-btn ant-btn ant-btn-primary']
    Sleep    20

团队行事历设为默认行事历
    Comment    鼠标滑动到底部    ${HLName}\n
    Mouse Over    //span[contains(text(),'${HLName}')]
    点击    //span[contains(text(),'${HLName}')]
    Sleep    10
    Mouse Over    //span[contains(text(),'${HLName}')]
    点击    //span[contains(text(),'${HLName}')]
    Sleep    3
    点击    //span[contains(text(),'${HLName}')]/span[2]
    Sleep    3
    Comment    Mouse Over    //*[contains(text(),'退出')]
    点击    //*[contains(text(),'退出')]
    Sleep    3
    点击    //span[contains(text(),'确定')]
    Sleep    3

团队行事历转移
    Mouse Over    //span[contains(text(),'${HLName}')]
    Sleep    5
    点击    //span[contains(text(),'${HLName}')]/span[2]
    Sleep    5
    点击    //*[contains(text(),'转移')]
    Sleep    3
    点击    //*[contains(text(),'高伟')]
    Sleep    3
    点击    //span[contains(text(),'确定')]
    Sleep    3

团队行事历解散
    Mouse Over    //span[contains(text(),'${HLName}')]
    Sleep    3
    点击    //span[contains(text(),'${HLName}')]/span[2]
    Sleep    3
    点击    //*[contains(text(),'解散')]
    Sleep    3
    点击    //span[contains(text(),'确定')]
    Sleep    3
    点击    //div[@class="header ath-grid-header"]/angular-mf-adapter[2]/app-main-toolbar/div[4]/div
    Sleep    3
    Should Contain    //span[contains(text(),'${HLName}团队已解散')]    ${HLName}团队已解散
    Sleep    3

按部门分组
    #升序
    点击    //span[contains(text(),'分组')]
    点击    //span[contains(text(),'按部门分组')]
    点击    //span[contains(text(),'确定')]
    Sleep    5
    should contain    //h5[contains(text(),'分组目录')]    分组目录
    Sleep    3
    #降序
    点击    //span[contains(text(),'分组')]
    点击    //div[@class="group-item-list ng-star-inserted"]/div[6]/ath-radio-group/label[2]/span[2]
    点击    //span[contains(text(),'确定')]
    Sleep    3
    should contain    //h5[contains(text(),'分组目录')]    分组目录
    Sleep    3

设为和取消默认团队行事历
    Mouse Over    //div[@class="content ath-grid-outer-wrapper"]/app-calendar/div[1]/div[1]/div[2]/div[2]/div/span[1]
    点击    //div[@class="content ath-grid-outer-wrapper"]/app-calendar/div[1]/div[1]/div[2]/div[2]/div/span[2]/span[1]/i
    Comment    点击    //div[@class="content ath-grid-outer-wrapper"]/app-calendar/div[1]/div[1]/div[2]/div[2]/div/span[2]/span[1]/i/*[name()='svg']
    当前页面可见字符    已设为默认
    Sleep    2
    Comment    点击    //div[@class="content ath-grid-outer-wrapper"]/app-calendar/div[1]/div[1]/div[2]/div[2]/div/span[2]/span[1]/i

退出团队行事历
    [Arguments]    ${user}
    Mouse Over    //span[contains(text(),'${HLName}')]
    点击    //span[contains(text(),'${HLName}')]
    Sleep    5
    Mouse Over    //span[contains(text(),'${HLName}')]
    点击    //span[contains(text(),'${HLName}')]
    Sleep    5
    点击    //span[contains(text(),'${HLName}')]/span[2]
    Sleep    5
    Comment    Mouse Over    //*[contains(text(),'退出')]
    点击    //*[contains(text(),'退出')]
    Sleep    3
    输入    //ath-input-group[@class='ath-input-group-wrapper ant-input-group-wrapper']//div[@class='ath-input-group-cocoon']//section[@class='ant-input-affix-wrapper ath-input-affix-wrapper']//input[@type='text']\n    ${user}
    点击    //span[@class='ath-content']
    Sleep    3
    点击    //span[contains(text(),'确定')]
    当前页面不可见字符    您已退出团队
    Sleep    3

转移团队行事历
    [Arguments]    ${Tuser}
    #编辑
    Mouse Over    //span[contains(text(),'${HLName}')]
    js点击    //span[contains(text(),'${HLName}')]/span[2]
    Sleep    3
    Scroll Element Into View    //*[contains(text(),'编辑')]
    Sleep    5
    js点击    //*[contains(text(),'编辑')]
    Sleep    3
    Execute Javascript    document.querySelector('.ant-checkbox-input').click()
    点击    //*[contains(text(),'确定')]
    当前页面可见字符    操作成功
    Sleep    3
    #转移
    Mouse Over    //span[contains(text(),'${HLName}')]
    Sleep    5
    js点击    //span[contains(text(),'${HLName}')]/span[2]
    Comment    Mouse Over    //*[contains(text(),'转移')]
    Sleep    5
    js点击    //*[contains(text(),'转移')]
    Sleep    3
    输入    //ath-input-group[@class='ath-input-group-wrapper ant-input-group-wrapper']//div[@class='ath-input-group-cocoon']//section[@class='ant-input-affix-wrapper ath-input-affix-wrapper']//input[@type='text']    ${Tuser}
    点击    //span[@class='ath-content']
    Sleep    3
    点击    //span[contains(text(),'确定')]
    Sleep    3

解散团队行事历
    Mouse Over    //span[contains(text(),'${HLName}')]
    js点击    //span[contains(text(),'${HLName}')]
    Sleep    3
    Mouse Over    //span[contains(text(),'${HLName}')]
    js点击    //span[contains(text(),'${HLName}')]
    Sleep    3
    Mouse Over    //span[contains(text(),'${HLName}')]/span[2]
    js点击    //span[contains(text(),'${HLName}')]/span[2]
    Sleep    3
    js点击    //*[contains(text(),'解散')]
    Sleep    3
    点击    //span[contains(text(),'确定')]
    Sleep    3
    Comment    点击    //div[@class="header ath-grid-header"]/angular-mf-adapter[2]/app-main-toolbar/div[4]/div
    Comment    Sleep    3
    Comment    Should Contain    //span[contains(text(),'${HLName}团队已解散')]    ${HLName}团队已解散
    Comment    Sleep    3
    当前页面可见字符    团队已解散
