*** Settings ***
Resource          ../系统关键字/web.robot

*** Keywords ***
搜索
    点击    //span[contains(text(),'搜索')]
    当前页面不可见字符    数据查询中，请稍候~
    #    Sleep    5

按输入项目信息查询
    [Arguments]    ${application}    ${projectName}    ${status}    ${startDate}    ${endDate}
    Comment    Select Frame    iframe
    点击    //ath-select-search[@class="ant-select-selection-search ng-star-inserted"]
    #下拉框打开立即点击可能会点错位置,加上等待时间
    #    Sleep    1
    点击    //span[@class='option-item-label' and text()='${application}']
    #    Sleep    1
    ${loc}    按顺序获取元素    //ath-select-search[@class="ant-select-selection-search ng-star-inserted"]    1
    点击    ${loc}
    点击    //span[@class='option-item-label' and text()='${projectName}']
    Run Keyword If    '${status}'!='全部'    状态选择    ${status}
    Log    ${startDate}
    输入    //input[@placeholder="开始日期"]    ${startDate}
#    Press Key    //input[@placeholder="开始日期"]    \\13
#    Log    ${endDate}
    输入    //input[@placeholder="结束日期"]    ${endDate}
    Press Key    //input[@placeholder="结束日期"]    \\13
    #    Sleep    1
    搜索

状态选择
    [Arguments]    ${status}
    点击    //*[@title='全部']
    点击    //span[@class='option-item-label' and text()='${status}']

项目查询结果校验
    [Arguments]    ${startTime}    ${status}    ${application}    ${projectName}    ${originalData}    ${owner}    ${current}    ${executor}
    当前页面可见字符    ${startTime}
    当前页面可见字符    ${status}
    当前页面可见字符    ${application}
    当前页面可见字符    ${projectName}
    当前页面可见字符    ${originalData}
    当前页面可见字符    ${owner}
    当前页面可见字符    ${current}
    当前页面可见字符    ${executor}

按输入任务信息查询
    [Arguments]    ${no}    ${status}    ${startDate}    ${endDate}    ${queryType}=单号
    点击    //li[contains(text(),'任务查询')]
    Run Keyword If    '${queryType}'!='单号'    Log    敬请期待
    输入    //input[@placeholder="请输入单号搜索"]    ${no}
    Sleep    5
    Run Keyword If    '${status}'!='全部'    状态选择    ${status}
    输入    //input[@placeholder="开始日期"]    ${startDate}
    Sleep    1
    输入    //input[@placeholder="结束日期"]    ${endDate}
    Sleep    1
    Press Key    //input[@placeholder="结束日期"]    \\13
    Sleep    2
    搜索
    #    Sleep    8

按输入服务信息查询
    [Arguments]    ${pid}    ${content}    ${status}    ${startDate}    ${endDate}
    点击    //li[contains(text(),'服务查询')]
    点击    //nz-form-item[@class='prefix-item ant-form-item ant-row ant-form-item-has-success']
    点击    //span[@class='option-item-label'][contains(text(),'${pid}')]
    输入    //input[@type='text']    ${content}
    点击    //ath-select[@formcontrolname='status']
    点击    //span[contains(text(),'${status}')]
    输入    //input[@placeholder="开始日期"]    ${startDate}
    输入    //input[@placeholder="结束日期"]    ${endDate}
    Sleep    2
    搜索
    #    Sleep    8

服务查询结果校验
    当前页面可见字符     xxx

任务跳转
    [Arguments]    ${projectName}
    Unselect Frame
    Iframe选择
    #    Sleep    5
    点击    //span[contains(text(),'协同计划排定')]
    #    Sleep    10
    Unselect Frame
    #    Click Element    //li[@class='ath-tab-menus-li ng-star-inserted ath-tab-menus-active']/i[@nztype='close']
    点击    //li[@class='ath-tab-menus-li ng-star-inserted ath-tab-menus-active']/i[@nztype='close']
    #    Sleep    10

项目跳转
    [Arguments]    ${projectName}
    Unselect Frame
    Iframe选择
    #    Sleep    5
    点击    //span[contains(text(),'${projectName}')]
    #    Sleep    20
    Unselect Frame
    #    Click Element    //li[@class='ath-tab-menus-li ng-star-inserted ath-tab-menus-active']/i[@nztype='close']
    点击    //li[@class='ath-tab-menus-li ng-star-inserted ath-tab-menus-active']/i[@nztype='close']
    #    Sleep    10

任务查询结果校验
    [Arguments]    ${startTime}    ${status}    ${application}    ${projectName}    ${currentTask}    ${taskType}    ${originalData}    ${operator}    ${operate}    ${operateTime}
    当前页面可见字符    ${startTime}
    当前页面可见字符    ${status}
    当前页面可见字符    ${application}
    当前页面可见字符    ${projectName}
    当前页面可见字符    ${currentTask}
    当前页面可见字符    ${taskType}
    当前页面可见字符    ${originalData}
    当前页面可见字符    ${operator}
    当前页面可见字符    ${operate}
    当前页面可见字符    ${operateTime}

打开关闭异常信件发送
    Sleep    40
    当前页面不可见字符    数据查询中，请稍候~
    ${visual}    判断元素是否可见    //button[@class="ath-switch ant-switch ant-switch-small"]    20
    Run Keyword If    ${visual}    Run Keywords    点击    //button[@class="ath-switch ant-switch ant-switch-small"]
    ...    AND    当前页面可见    //button[contains(@class,'ant-switch-checked')]    5
    Run Keyword If    '${visual}'=='False'    Run Keywords    点击    //button[contains(@class,'ant-switch-checked')]
    ...    AND    当前页面可见    //button[contains(@class,"ath-switch ant-switch ant-switch-small")]    5

关闭异常信件发送
    点击    //button[@class="ath-switch ant-switch ant-switch-small cdk-focused cdk-mouse-focused ant-switch-checked"]
