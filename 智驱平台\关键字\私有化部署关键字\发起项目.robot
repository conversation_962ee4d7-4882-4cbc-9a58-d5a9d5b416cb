*** Settings ***
Library           SeleniumLibrary
Resource          ../../元素/开发平台元素.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/web.robot
Resource          公共方法.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../系统关键字/web.robot
Resource          Athena平台控件.robot
Resource          Athena平台.robot
Resource          首页.robot
Resource          待办.robot




*** Keywords ***
发起项目页-发起项目
    点击顶部菜单    全部
    点击    //span[contains(text(),'发起项目')]
    Sleep    1
    点击    //div[contains(text(),'_DTDAPP')]
    Sleep    3
    ${startTimeLoc}    按顺序获取元素    //input[@class='ant-checkbox-input ng-untouched ng-pristine ng-valid']    1
    点击    ${startTimeLoc}
    Sleep    1
    点击    //span[contains(text(),"提交")]

进入待办页-我的任务-筛选任务名称
    [Arguments]    ${task_name}
    Sleep    3
    点击    //li[contains(text(),"首页")]
    Sleep    3
    点击    //div[@class='common-text zh']
    Sleep    1
    点击    //div[contains(text(),"我的任务")]
    Sleep    1
    点击    //app-todo-filter[@class='ng-star-inserted']//span[@class='todo-common-tool-option todo-common-tool-hover-active']
    Sleep    1
    点击    //span[contains(text(),"重置")]
    Sleep    2
    点击    //input[@class='ant-select-selection-search-input ng-pristine ng-valid ng-touched']
    Sleep    1
    点击    //div[@class='ath-tooltip-wrapper-inner']//span[contains(text(), '${task_name}')]
    Sleep    1
    点击    //div[@class='cdk-overlay-backdrop todo-filter-overlay-backdrop cdk-overlay-backdrop-showing']
    Sleep    1
    点击    //span[contains(text(),"确定")]


进入待办详情-提交
    Sleep    3
    点击    //div[contains(text(),"New")]
    当前页面包含元素    //div[contains(text()," 私有化环境需求智驱平台全流程测试 ")]
    Sleep    1
    点击    //span[contains(text(),"提交")]

