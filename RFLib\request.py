from browsermobproxy import Server

server = Server('path_to_browsermob-proxy')
server.start()
proxy = server.create_proxy()

# 获取代理端口
port = proxy.port

# 将代理设置为Chrome或Firefox浏览器的代理
chrome_options = webdriver.ChromeOptions()
chrome_options.add_argument('--proxy-server=localhost:%d' % port)

driver = webdriver.Chrome(executable_path='path_to_chromedriver', chrome_options=chrome_options)

# 使用代理访问网页
proxy.new_har("http://www.baidu.com")
driver.get("http://www.baidu.com")

# 停止代理
proxy.stop()

# 停止服务器
server.stop()

