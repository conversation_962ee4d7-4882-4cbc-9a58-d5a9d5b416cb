*** Settings ***
Library           SeleniumLibrary
Resource          ../../元素/开发平台元素.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/web.robot
Resource          公共方法.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../系统关键字/web.robot
Resource          Athena平台控件.robot
Resource          Athena平台.robot
Resource          首页.robot
Resource          待办.robot
Resource          ../业务关键字/PCC.robot

*** Keywords ***
进入-小AI-五要素基线用例专用作业--发起提交
    [Arguments]    ${reqname_random}
    点击    //*[contains(text(),"小AI-五要素基线用例专用-勿动_项目_0001")]
    Sleep    2
    点击    //app-dynamic-input-display[@placeholder='需求编号']
    Sleep    1
    Input Text    //input[@placeholder="需求编号"]    ${reqname_random}
    Sleep    1
    点击    //app-dynamic-input-display[@placeholder='需求名称']
    Sleep    1
    Input Text    //input[@placeholder="需求名称"]    个案开发
    Sleep    1
    点击    //span[contains(text(),"提交")]
    Sleep    1
    点击    //span[contains(text(),"确定")]
    Sleep    3

点击任务-交接
    [Arguments]    ${tagname}    ${handover_name}
    Sleep    5
    #这里识别不到待办,先跳转网页
    跳转到待办页    /todo/task
    交接任务/项目名称搜索    ${tagname}
    点击将要交接的任务卡    ${tagname}
    Sleep    3
#    点击    //span[contains(text(),"交接")]
#    Sleep    3
    输入    //app-task-handover//input    ${handover_name}
    Sleep    3
    点击    //span[@class='search-Highlight']
    Sleep    3
    点击    //span[contains(text(),"确定")]
    元素存在则点击    //span[contains(text(),"继续")]
    Sleep    1
    当前页面不可见元素    //span[contains(text(),"继续")]
    当前页面不可见元素    //span[contains(text(),"确定")]


点击将要交接的任务卡
    [Arguments]    ${tagname}
    Sleep    3
    点击    //span[contains(text(),"${tagname}")]
    Sleep    2
    元素存在则点击    //span[contains(text(),"交接")]

被交接人-提交任务
    [Arguments]    ${tagname}
    Sleep    5
    跳转到待办页    /todo/task
    交接任务/项目名称搜索    ${tagname}
#    判断交接来源是否正常展示
    鼠标悬停    //span[contains(text(),"${tagname}")]
    判断元素是否可见    //span[contains(text(),"供应商1")]
    点击将要交接的任务卡    ${tagname}
    Sleep    3
    点击    //span[contains(text(),"提交")]
    Sleep    3
    点击    //span[contains(text(),"确定")]

交接任务/项目名称搜索
    [Arguments]    ${name}
    #此处后面看怎么优化
    视图模式设定    卡片
    清空筛选
    #点击    //span[contains(text(),'搜索')]
    ${element_exists}    判断元素是否可见    (//span[contains(text(),'搜索')])[2]    3
    Run Keyword If    ${element_exists}    点击     (//span[contains(text(),'搜索')])[2]
    ...    ELSE    Log    Element does not exist.
    输入    //input[@placeholder='可用空格分隔关键词']    ${name}
    交接任务-待办搜索轮询    ${name}

交接任务-待办搜索轮询
    [Arguments]    ${tagname}
    点击    //span[@type='addon']
    FOR    ${index}    IN RANGE    6
        ${element_exists}    Run Keyword And Return Status    当前页面可见    //span[contains(text(),"${tagname}")]    10
        Run Keyword If    ${element_exists}    Exit For Loop
        ...    ELSE    刷新页面
    END
    #项目交接

点击项目-交接
    [Arguments]    ${tagname}    ${handover_name}
    Sleep    5
    跳转到待办页    /todo/project
    交接任务/项目名称搜索    ${tagname}
    点击将要交接的项目卡    ${tagname}
    Sleep    3
    输入    //app-task-handover//input    ${handover_name}
    Sleep    3
    点击    //span[@class='search-Highlight']
    Sleep    3
    点击    //span[contains(text(),"确定")]
    当前页面不可见元素    //span[contains(text(),"确定")]

点击将要交接的项目卡
    [Arguments]    ${tagname}
    Sleep    3
    点击    //span[contains(text(),"${tagname}")]
    Sleep    3
    点击    //span[contains(text(),"交接")]

被交接人-查询项目是否创建
    [Arguments]    ${tagname}
    Sleep    5
    跳转到待办页    /todo/project
    交接任务/项目名称搜索    ${tagname}

调用iam接口为用户授权/取消授权应用
    [Arguments]    ${data}
    DIGIWIN-POST    ${iam_url}    /api/iam/v2/user/simple/update/tenant    ${data}
    # 任务批量交接

调用iam登陆接口获取token
    [Arguments]    ${data}
    DIGIWIN-GET-TOKEN    ${iam_url}    /api/iam/v2/identity/login    ${data}


点击批量交接-任务
    [Arguments]    ${tagname}
    Sleep    5
    跳转到待办页    /todo/task
    交接任务/项目名称搜索    ${tagname}
    Sleep    2
    Js点击    //span[@class='option hover-active' and contains(text(),'更多')]
    Sleep    3
    点击    //div[contains(text(),"批量交接")]

任务交接-确定
    [Arguments]    ${tagname}    ${handover_name}
    Sleep    2
    点击    //span[contains(text(),'${tagname}')]
    Sleep    1
    点击    //span[contains(text(),'确定')]
    Sleep    2
    输入    //app-task-handover//input    ${handover_name}
    Sleep    2
    点击    //span[@class='search-Highlight']
    Sleep    2
#    点击    //span[contains(text(),"确定")]
    Sleep    1
    点击    //button[@class='ath-btn ant-btn btn ant-btn-primary']
    Sleep    1
    元素存在则点击    //span[contains(text(),"继续")]
    Sleep    2


项目交接-确定
    [Arguments]    ${handover_name}
    Sleep    2
    点击    //label[@id='all']//span[@class='ant-checkbox']
    Sleep    1
    点击    //span[contains(text(),'确定')]
    Sleep    2
    输入    //app-task-handover//input    ${handover_name}
    Sleep    3
    点击    //span[@class='search-Highlight']
    Sleep    3
    #    点击    //span[contains(text(),"确定")]
    点击    //button[@class='ath-btn ant-btn btn ant-btn-primary']
    Sleep    3
    #项目批量交接

点击批量交接-项目
    [Arguments]    ${tagname}
    Sleep    5
    跳转到待办页    /todo/project
    交接任务/项目名称搜索    ${tagname}
    Sleep    2
    Js点击    //span[@class='option hover-active' and contains(text(),'更多')]
    Sleep    3
    点击    //div[contains(text(),"批量交接")]



进入导入导出中心
    点击顶部菜单    全部
    Sleep    3
    点击    //span[contains(text(),'导入导出中心')]
    Sleep    3


判断交接时自己/未授权当前应用的用户在交接时是否可以在下拉选择中展示
    [Arguments]    ${name}    ${tagname}    ${selfname}
    #这里识别不到待办,先跳转网页
    跳转到待办页    /todo/task
    交接任务/项目名称搜索    ${tagname}
    点击将要交接的任务卡    ${tagname}
    Sleep    3
    输入    //app-task-handover//input    ${name}
    Sleep    3
    当前页面不可见元素    //*[contains(text(),"${name}")]
    Sleep    3
    输入    //app-task-handover//input    ${selfname}
    Sleep    3
    当前页面不可见元素    //*[contains(text(),"${selfname}")]


鼎捷云登陆接口获取token
    ${auth-app}     Set Variable If    '${ENV}'=='huawei.test'    ${auth-app-value-test}    '${ENV}'=='huawei.prod'    ${auth-app-value-hwprod}    '${ENV}'=='microsoft.prod'    ${auth-app-value-wrprod}
    ${auth-app-secret}     Set Variable If    '${ENV}'=='huawei.test'    ${auth-app-secret-value-test}    '${ENV}'=='huawei.prod'    ${auth-app-secret-value-hwprod}    '${ENV}'=='microsoft.prod'    ${auth-app-secret-value-wrprod}
    ${device-id}    Set Variable If    '${ENV}'=='huawei.test'    ${digi-middleware-device-id-test}    '${ENV}'=='huawei.prod'    ${digi-middleware-device-id-hwprod}    '${ENV}'=='microsoft.prod'    ${digi-middleware-device-id-wrprod}
    ${passwordHash}    Set Variable If    '${ENV}'=='huawei.test'    ${passwordHash_test}    '${ENV}'=='huawei.prod'    ${passwordHash_hwprod}    '${ENV}'=='microsoft.prod'    ${passwordHash_wrprod}
    ${clientEncryptPublicKey}    Set Variable If    '${ENV}'=='huawei.test'    ${clientEncryptPublicKey_test}    '${ENV}'=='huawei.prod'    ${clientEncryptPublicKey_hwprod}    '${ENV}'=='microsoft.prod'    ${clientEncryptPublicKey_wrprod}
    Set Global Variable    ${auth-app}
    Set Global Variable    ${auth-app-secret}
    Set Global Variable    ${device-id}
    Set Global Variable    ${passwordHash}
    Set Global Variable    ${clientEncryptPublicKey}     
    ${data}    Set Variable    {"userId":"<EMAIL>","passwordHash":"${passwordHash}","clientEncryptPublicKey":"${clientEncryptPublicKey}"}
    调用iam登陆接口获取token    ${data}


为用户应用授权小AI五要素应用权限
    鼎捷云登陆接口获取token
    ${data}    Set Variable    {"user":{"id":"qcsupplier001","name":"供应商1","email":"<EMAIL>","hash":"${hash_value}","remark":"變更用戶資料"},"metadata":[{"catalogId":"contact","key":"wechat","value":""}],"roleIds":[],"userInOrgs":[],"defaultOrg":[],"userInTag":[],"app":{"deleteAppIds":[],"addAppIds":["aifiveCase"]},"userType":false}
    调用iam接口为用户授权/取消授权应用    ${data}
