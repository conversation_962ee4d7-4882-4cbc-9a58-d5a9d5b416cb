*** Settings ***
Documentation     陈金明
Suite Setup       Run Keywords    环境设定
Suite Teardown    Run Keywords    关闭浏览器
Resource          ../关键字/业务关键字/催办.robot
Resource          ../关键字/业务关键字/Athena平台.robot
#Resource          ../关键字/业务关键字/PCC.robot

*** Test Cases ***
催办
    [Documentation]    陈金明
    #需要在交付设计器中配置催办
    #此用例包含活动的分支场景
    ${username}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${password}    Set Variable If    '${ENV}'=='paas'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${approver}    Set Variable If    '${ENV}'=='paas'    HL18271405997    '${ENV}'=='huawei.test'    HL18271405997    '${ENV}'=='huawei.prod'    HL18271405997    '${ENV}'=='microsoft.prod'    HL18271405997    '${ENV}'=='muihuawei.test'    HL18271405997
    ${approver_passwork}    Set Variable If    '${ENV}'=='paas'    HuangL0920    '${ENV}'=='huawei.test'    HuangL0920    '${ENV}'=='huawei.prod'    HuangL0920    '${ENV}'=='microsoft.prod'    HuangL0920    '${ENV}'=='muihuawei.test'    HuangL0920
    ${tenant}    Set Variable If    '${ENV}'=='pressure'    自动化测试环境    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    自动化测试环境    '${ENV}'=='huawei.prod'    自动化华为生产环境    '${ENV}'=='microsoft.prod'    自动化微软生产环境
    Set Global Variable    ${id}    ${长唯一标识}
    登录Athena平台    ${username}    ${password}    tenant=${tenant}
    点击顶部菜单    全部
    发起项目录入合同条款
    催办消息检验    ${id}
    完成条款送审
    关闭浏览器
#    登录Athena平台    ${approver}    ${approver_passwork}    tenant=${tenant}
#    签核同意    ${id}