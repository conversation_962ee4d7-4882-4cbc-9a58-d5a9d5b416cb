*** Settings ***
Documentation     陈金明
Suite Setup       Run Keywords    环境设定
Suite Teardown    Run Keywords    关闭浏览器
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/业务关键字/Athena平台控件.robot
Resource          ../关键字/业务关键字/IT运维模组.robot
Resource          ../关键字/业务关键字/Athena平台.robot

*** Test Cases ***
IT运维模组
    [Documentation]    陈金明
    ...    目前只在生产执行
    ${ownerUsername}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    ${ownerPassword}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #查询时间
    ${startDate}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    2024/09/25    '${ENV}'=='microsoft.prod'    2024/10/01
    ${endDate}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    2024/09/25    '${ENV}'=='microsoft.prod'    2024/10/08
    ${no}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    2024092501    '${ENV}'=='microsoft.prod'    2024100814
    #项目查询结果断言变量赋值,根据需要添加替换
    ${startTime}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    2024-09-25 09:06:57    '${ENV}'=='microsoft.prod'    2024-10-08 18:36:56
    ${projectName}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    2024092501[cj测试项    '${ENV}'=='microsoft.prod'    2024100814[自动化测
    ${originalData}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    2024092501    '${ENV}'=='microsoft.prod'    2024100814
    ${owner}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    生产华为环境自动化测试KM    '${ENV}'=='microsoft.prod'    生产微软环境自动化测试小AI平台
    #任务查询结果断言变量赋值,根据需要添加替换
    ${taskStartTime}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    2024-09-25 09:08:23    '${ENV}'=='microsoft.prod'    2024-10-08 18:36:57
    ${taskProjectName}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    协同排定2024092501    '${ENV}'=='microsoft.prod'    2024100814[自动化测
    ${taskCurrentTask}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    协同计划排定    '${ENV}'=='microsoft.prod'    判断项目发起是否签核
    ${taskType}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    人工    '${ENV}'=='microsoft.prod'    —
    ${taskOriginalData}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    2024092501-1-1    '${ENV}'=='microsoft.prod'    2024100814
    ${taskOperator}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    生产华为环境自动化测试KM    '${ENV}'=='microsoft.prod'    —
    #${operate}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    2024-09-25 09:06:57    '${ENV}'=='microsoft.prod'    2024-10-08 18:36:56
    #${operateTime}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    2024-09-25 09:06:57    '${ENV}'=='microsoft.prod'    2024-10-08 18:36:56
    登录Athena平台    ${ownerUsername}    ${ownerPassword}
    点击顶部菜单    全部

    [Teardown]    Run Keywords    关闭浏览器

test
    # 登录Athena平台    ProdHwAthenaAutoTestKm01    ProdHwAthenaAutoTestKm01
    # sleep    20
    # 输入    //input[@placeholder="可用空格分隔关键词"]    1234444
    # sleep    20
    ${123}    Set Variable    123
    ${123456}    Set Variable    123456
    ${a}    Evaluate    '${123}' in '${123456}'
    Run Keyword If    '123' not in '12345'    Log    1111
