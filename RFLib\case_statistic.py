import os
def count_keyword_titles():
    """
    用于统计指定目录下所有文件中处于"*** Keywords ***"部分的非空行（标题）的数量
    """
    count_all = 0  # 用于累计所有文件中符合要求的标题数量
    for root, dirs, files in os.walk(r"../智驱平台/关键字/控件关键字"):
        for file in files:
            file_path = os.path.join(root, file)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.splitlines()
                    in_keywords_section = False
                    for line in lines:
                        if line == "*** Keywords ***":
                            in_keywords_section = True
                            continue
                        # 如果处于Keywords部分且该行非空，就认为是一个标题，计数加1
                        if in_keywords_section and line.strip()==line and line!='' and  line[0]!='#':
                            print(line)
                            count_all += 1
            except UnicodeDecodeError:
                print(f"文件 {file_path} 编码格式可能不是utf-8，无法正确读取")
            except FileNotFoundError:
                print(f"文件 {file_path} 不存在，无法读取")
    return count_all


result = count_keyword_titles()
print(f"类似标题的个数为: {result}")