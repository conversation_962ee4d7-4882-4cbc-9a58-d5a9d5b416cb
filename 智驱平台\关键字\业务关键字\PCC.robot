*** Settings ***
Library           SeleniumLibrary
#Library           ../../../RFLib/Base.py
Resource          ../系统关键字/web.robot
Resource          ../业务关键字/Athena平台.robot
Resource          Athena平台.robot
Resource          首页.robot
#Resource          待办.robot
#Resource          数据查询.robot
Resource          收藏.robot

*** Keywords ***
手动发起项目
    [Arguments]    ${projectname}    ${projecttype}    ${projectrule}    ${projectcycle}    ${projectstartdate}
    #${projectname}    Set Variable    自动化测试项目卡${唯一标识}
    点击顶部菜单    全部
    点击    //span[contains(text(),'发起项目')]
    点击    //div[@id='card_projectCenterConsole_mainProject']
    当前页面可见    //span[contains (text(),'提交')]
    #归属公司选择
#    归属公司选择    TestAthenaAutoTestAi001
    #填写必填项
    输入    //input[@name= 'project_name']    ${projectname}
    项目信息录入    项目名称    ${projectname}
    Sleep    2
    #选择项目类型开窗，搜索一个项目类型后点击确定
    点击    //p[contains (text(),'项目类型')]/following::div[@class='btn-group'][1]
    点击    //div[@class='ath-input-content-box ng-star-inserted']
    当前页面不可见字符    数据加载中
    输入    //div[@class='ath-input-content-box ng-star-inserted']//input[@type='text']    ${projecttype}
    点击    //button[@class='ath-btn ant-btn ant-btn-primary ant-btn-sm ant-btn-icon']
#    Execute Javascript    document.querySelector('.ant-radio-input').click()
    Sleep    3
    点击    //span[@title='${projecttype}']/preceding::input[1]/parent::span[1]
    点击    //div[@class='ant-modal-footer']/descendant::span[contains(text(),'提交')]
    #选择编码规则开窗，搜索一个编码规则后选择并提交
    #输入    //input[@class='ant-input-number-input ng-untouched ng-pristine ng-valid']    ${projectcycle}
    #提交
    项目信息录入    计划开始日期    20240915
    点击    //span[contains (text(),'提交')]
    #提交项目
    点击    //span[@class='ng-star-inserted']/following::span[contains(text(),'确定')]
    判断后跳转到目标页面    /todo/project
    任务/项目名称搜索    ${projectname}    New

手动发起项目(繁体)
    [Arguments]    ${projectname}    ${projecttype}    ${projectrule}    ${projectcycle}    ${projectstartdate}
    #${projectname}    Set Variable    自动化测试项目卡${唯一标识}
    点击顶部菜单    全部
    点击    //span[contains(text(),'發起項目')]
    点击    //div[@id='card_projectCenterConsole_mainProject']
    当前页面可见    //span[contains (text(),'提交')]
    #归属公司选择
#    归属公司选择    TestAthenaAutoTestAi001
    #填写必填项
    输入    //input[@name= 'project_name']    ${projectname}
    项目信息录入    專案名稱    ${projectname}
    Sleep    2
    #选择项目类型开窗，搜索一个项目类型后点击确定
    点击    //p[contains (text(),'專案類型')]/following::div[@class='btn-group'][1]
    点击    //div[@class='ath-input-content-box ng-star-inserted']
    输入    //div[@class='ath-input-content-box ng-star-inserted']//input[@type='text']    ${projecttype}
    点击    //button[@class='ath-btn ant-btn ant-btn-primary ant-btn-sm ant-btn-icon']
#    Execute Javascript    document.querySelector('.ant-radio-input').click()
    Sleep    3
    点击    //span[@title='${projecttype}']/preceding::input[1]/parent::span[1]
    点击    //div[@class='ant-modal-footer']/descendant::span[contains(text(),'提交')]
    #选择编码规则开窗，搜索一个编码规则后选择并提交
    #输入    //input[@class='ant-input-number-input ng-untouched ng-pristine ng-valid']    ${projectcycle}
    #提交
    项目信息录入    計劃開始日期    20240915
    点击    //span[contains (text(),'提交')]
    #提交项目
    点击    //span[contains(text(),'確定')]
    Sleep    10
    跳转网页    /todo/project
    任务/项目名称搜索(繁体)    ${projectname}    New（()

子母项目发起项目
    [Arguments]    ${program}    ${project_type}    ${rule}    ${plan_start_date}    ${plan_finish_date}
    点击顶部菜单    全部
    点击    //span[contains(text(),'发起项目')]
    点击    //span[contains(text(),'项目中控台子母项目')]
    当前页面可见    //span[contains (text(),'提交')]
    编码规则    ${rule}
    项目信息录入    项目集名称    ${program}
    项目信息录入    计划开始日期    ${plan_start_date}
    项目信息录入    计划结束日期    ${plan_finish_date}
    Press Key    //p[contains(text(),'计划结束日期')]/following::input[1]    \\13
    #添加2个子项目
    点击    //span[contains(text(),'新增')]
    子项目录入    0    子项目1-${program}    ${project_type}    ${plan_start_date}    ${plan_finish_date}
    点击    //span[contains(text(),'新增')]
    子项目录入    1    子项目2-${program}    ${project_type}    ${plan_start_date}    ${plan_finish_date}
    点击    //span[contains(text(),'提交')]
    点击    //span[contains(text(),'确定')]
    判断后跳转到目标页面    /todo/project
    任务/项目名称搜索    ${program}    New
    #    任务/项目名称搜索    子项目1-${program}    New
    #    任务/项目名称搜索    子项目2-${program}    New

子项目录入
    [Arguments]    ${index}    ${project_name}    ${project_type}    ${plan_start_date}    ${plan_finish_date}
    鼠标悬停    //div[@class='ag-center-cols-clipper']//div[@row-index=${index}]//div[@col-id='project_type_name']
    子项目类型    ${index}    ${project_type}
    点击    //div[@class='ag-center-cols-clipper']//div[@row-index=${index}]//div[@col-id='project_name']
    输入    //div[@class='ag-center-cols-clipper']//div[@row-index=${index}]//input[@type='text'][@name='project_name']    ${project_name}
    点击    //div[@class='ag-center-cols-clipper']//div[@row-index=${index}]//div[@col-id='plan_start_date']
    输入    //div[@class='ag-center-cols-clipper']//div[@row-index=${index}]//div[@col-id='plan_start_date']//input    ${plan_start_date}
    Press Key    //div[@class='ag-center-cols-clipper']//div[@row-index=${index}]//div[@col-id='plan_start_date']//input    \\13
    点击    //div[@class='ag-center-cols-clipper']//div[@row-index=${index}]//div[@col-id='plan_finish_date']
    输入    //div[@class='ag-center-cols-clipper']//div[@row-index=${index}]//div[@col-id='plan_finish_date']//input    ${plan_finish_date}
    Press Key    //div[@class='ag-center-cols-clipper']//div[@row-index=${index}]//div[@col-id='plan_finish_date']//input    \\13

子项目类型
    [Arguments]    ${index}    ${project_type}
    点击    //div[@class='ag-center-cols-clipper']//div[@row-index=${index}]//div[@col-id='project_type_name']//i
    点击    //div[@class='ath-input-content-box ng-star-inserted']
    当前页面不可见字符    数据加载中
    输入    //div[@class='ath-input-content-box ng-star-inserted']//input[@type='text']    ${project_type}
    点击    //button[@class='ath-btn ant-btn ant-btn-primary ant-btn-sm ant-btn-icon']
    Execute Javascript    document.querySelector('.ant-radio-input').click()
    点击    //div[@class='ant-modal-footer']/descendant::span[contains(text(),'提交')]
    当前页面不可见字符    操作成功

项目类型
    [Arguments]    ${project_type}
    #选择项目类型开窗，搜索一个项目类型后点击确定
    点击    //p[contains (text(),'项目类型')]/following::div[@class='btn-group'][1]
    点击    //div[@class='ath-input-content-box ng-star-inserted']
    当前页面不可见字符    数据加载中
    输入    //div[@class='ath-input-content-box ng-star-inserted']//input[@type='text']    ${project_type}
    点击    //button[@class='ath-btn ant-btn ant-btn-primary ant-btn-sm ant-btn-icon']
    Execute Javascript    document.querySelector('.ant-radio-input').click()
    点击    //div[@class='ant-modal-footer']/descendant::span[contains(text(),'提交')]

编码规则
    [Arguments]    ${rule}
    点击    //p[contains (text(),'编码规则名称')]/following::div[@class='btn-group'][1]
    点击    //div[@class='ath-input-content-box ng-star-inserted']
    当前页面不可见字符    数据加载中
    输入    //div[@class='ath-input-content-box ng-star-inserted']//input[@type='text']    ${rule}
    点击    //button[@class='ath-btn ant-btn ant-btn-primary ant-btn-sm ant-btn-icon']
    Execute Javascript    document.querySelector('.ant-radio-input').click()
    点击    //div[@class='ant-modal-footer']/descendant::span[contains(text(),'提交')]
    当前页面不可见字符    操作成功

归属公司选择
    [Arguments]    ${cpmpany}
    点击    //p[contains (text(),'归属公司')]/following::div[@class='btn-group'][1]
    sleep    3
    点击    //div[@class='ath-input-content-box ng-star-inserted']
    sleep    1
    输入    //div[@class='ath-input-content-box ng-star-inserted']//input[@type='text']    ${cpmpany}
    sleep    5
    点击    //span[@class='ng-star-inserted'][contains(text(),'搜索')]
    sleep    3
    Execute Javascript    document.querySelector('.ant-radio-input').click()
    点击    //div[@class='ant-modal-footer']/descendant::span[contains(text(),'提交')]

新建一级计划
    [Arguments]    ${projectname}    ${taskname}    ${taskstartdate}    ${taskEndDate}    ${ownerUsernameCN}    ${exUsernameCN}    ${approvalType}
    #发起项目后页面有时候不能跳转到待办页面,此处加上判断进行跳转
    判断后跳转到目标页面    /todo/project
    任务/项目名称搜索    ${projectname}
    #点击    //div[@class='task-inner ng-star-inserted']
    #点击    //span[contains(text(),'${projectname}')]
    点击卡片    ${projectname}
    #页面加载的loading提示消失
    点击    	//div[contains(text(),'项目计划维护')]
    当前页面包含元素    //ath-spin[@class='pcc-wbs-render-loading ant-spin-nested-loading ath-spin' and @hidden=""]
    当元素可用    //div[@class='cdk-drag add-first-plan new-version no-dragging pointer'] /*[@class='anticon']
    #此sleep不可省略
    #获取pcc的项目project_no给项目知会和签核知会做检查
    ${str}    获取元素字符    //div[@class='title-info item']
    ${result}    Evaluate    '${str}'.split('[')    modules=sys
    Log    ${result}[0]
    #设定为全局变量给后面用
    Set Global Variable    ${projectNo}    ${result}[0]
    #创建任务卡
    点击    //div[@class='cdk-drag add-first-plan new-version no-dragging pointer'] /*[@class='anticon']
    #等待loading消失，否则计划名称输入可能失败
    当前页面不可见元素    //div[@class='ant-spin ant-spin-spinning']//span[@class='ant-spin-dot ant-spin-dot-spin ng-star-inserted']
    #此sleep不可省略
    Sleep    8
    #加上判断页面是否已经加载完成处于可编辑状态,判断提交按钮是否可用
    输入    //section[@class='ant-input-group ath-input-group-wrap-inner']//input[@type='text']    ${taskname}
    #选择负责人
    Execute Javascript    document.querySelector('.ant-select').click()
    点击    //span[@class='file-name']/following::span[contains(text(),'${ownerUsernameCN}')]
    #选择执行人
    输入    //input[@class='ant-select-selection-search-input ng-untouched ng-pristine']    ${exUsernameCN}
    点击    //span[@class='file-name']/following::span[contains(text(),'${exUsernameCN}')]
    点击    //label[contains(text(),'自动重推日期及工期')]
    #选择开始时间
    输入    //input[@placeholder='开始时间' ]    ${taskstartdate}
    #选择结束时间    \    点击
    输入    //input[@placeholder='结束时间' ]    ${taskEndDate}
    #点击下标题，使时间控件能够计算出工时
    #    点击    //div[@class='title']
    Press Key    xpath=(//input[@placeholder='结束时间' ])[1]    \\13
    #点击    //span[contains(text(),'进阶选项')]
    #Scroll Element Into View    //span[contains(text(),'更多')]
    #前置任务选择
    ${taskname}    Convert To String    ${taskname}
    Run Keyword If    '${taskname[0:2]}'=='后置'    前置任务选择    ${pretaskname}
    滑动元素到可见区域    //span[contains(text(),'需要签核')]
    #是否签核判断
    Run Keyword If    '${approvalType}'=='需要签核'    点击    //span[contains(text(),'需要签核')]
    当前页面不可见元素    //div[@class='ant-spin-container ng-star-inserted ant-spin-blur']
    点击    //button[@type='submit']
    当前页面不可见元素    //button[@type='submit']

新建一级计划(繁体)
    [Arguments]    ${projectname}    ${taskname}    ${taskstartdate}    ${taskEndDate}    ${ownerUsernameCN}    ${exUsernameCN}    ${approvalType}
    #发起项目后页面有时候不能跳转到待办页面,此处加上判断进行跳转
    跳转网页   /todo/project
    任务/项目名称搜索(繁体)    ${projectname}
    #点击    //div[@class='task-inner ng-star-inserted']
    #点击    //span[contains(text(),'${projectname}')]
    点击卡片    ${projectname}
    #页面加载的loading提示消失
    点击    	//div[contains(text(),'專案計劃維護')]
    当前页面包含元素    //ath-spin[@class='pcc-wbs-render-loading ant-spin-nested-loading ath-spin' and @hidden=""]
    当元素可用    //div[@class='cdk-drag add-first-plan new-version no-dragging pointer'] /*[@class='anticon']
    #此sleep不可省略
    #获取pcc的项目project_no给项目知会和签核知会做检查
    ${str}    获取元素字符    //div[@class='title-info item']
    ${result}    Evaluate    '${str}'.split('[')    modules=sys
    Log    ${result}[0]
    #设定为全局变量给后面用
    Set Global Variable    ${projectNo}    ${result}[0]
    #创建任务卡
    点击    //div[@class='cdk-drag add-first-plan new-version no-dragging pointer'] /*[@class='anticon']
    #此sleep不可省略
    Sleep    8
    #加上判断页面是否已经加载完成处于可编辑状态,判断提交按钮是否可用
    输入    //section[@class='ant-input-group ath-input-group-wrap-inner']//input[@type='text']    ${taskname}
    #选择负责人
    Execute Javascript    document.querySelector('.ant-select').click()
    点击    //span[@class='file-name']/following::span[contains(text(),'${ownerUsernameCN}')]
    #选择执行人
    输入    //input[@class='ant-select-selection-search-input ng-untouched ng-pristine']    ${exUsernameCN}
    点击    //span[@class='file-name']/following::span[contains(text(),'${exUsernameCN}')]
    点击    //label[contains(text(),'自動重推日期及工期')]
    #选择开始时间
    输入    //input[@placeholder='開始時間' ]    ${taskstartdate}
    #选择结束时间    \    点击
    输入    //input[@placeholder='結束時間' ]    ${taskEndDate}
    #点击下标题，使时间控件能够计算出工时
    #    点击    //div[@class='title']
    Press Key    xpath=(//input[@placeholder='結束時間' ])[1]    \\13
    #点击    //span[contains(text(),'进阶选项')]
    #Scroll Element Into View    //span[contains(text(),'更多')]
    #前置任务选择
    ${taskname}    Convert To String    ${taskname}
    Run Keyword If    '${taskname[0:2]}'=='後置'    前置任务选择    ${pretaskname}
    滑动元素到可见区域    //span[contains(text(),'需要簽核')]
    #是否签核判断
    Run Keyword If    '${approvalType}'=='需要签核'    点击    //span[contains(text(),'需要簽核')]
    当前页面不可见元素    //div[@class='ant-spin-container ng-star-inserted ant-spin-blur']
    点击    //button[@type='submit']
    当前页面不可见元素    //button[@type='submit']

协同计划排定
    元素存在则点击    //div[contains(text(),'更多')]    10
    点击    //div[contains(text(),'协同计划排定')]
    Sleep    5
    点击    //span[contains(text(),'确定')]
    点击    //span[contains(text(),'全选')]
    点击    //button[@class='btn active']

计划时程异常项目卡检查
    [Arguments]    ${project}    ${state}=null    ${progressAssertion}=null
    跳转网页    /todo/project
    任务/项目名称搜索    ${project}    ${state}
    ${str}    Catenate    SEPARATOR=    ${projectNo}    [${projectname}]
    当前页面可见    //span[contains(text(),'计划时程异常${str}')]

交付设计器发起侦测
    [Arguments]
        #以下为老样式进入交付设计器应用参数设定页执行侦测
#    点击    //div[@class='unit-card-container jituan']
    点击    //app-unit-card[@class='ng-star-inserted']//div[@class='unit-card-container']
    #进入对应应用
    滑动元素到可见区域    //div[contains(text(),'项目中控台')]/ancestor::div[@class='app-card-container']//nz-spin
    当前页面不可见元素     //span[@class='ant-spin-dot ant-spin-dot-spin ng-star-inserted']
    #    再次点击
    点击    //div[@class='title-box ant-col'][contains(text(),'项目中控台')]
    点击    //div[contains(@class,'fth-menu-box expend')]/div[1]/i[1]
    元素存在则点击    //span[contains(text(),'确定')]
    点击    //div[@class='period-name'][contains(text(),'计划变更周期')]/span/img
    点击    //span[contains(text(),'我知道了')]
    Sleep    60

协同计划时程异常任务卡提交
    [Arguments]    ${taskname}    ${state}=null    ${progressAssertion}=null
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'我的任务')]
    刷新页面
    Sleep    10
    任务/项目名称搜索    ${taskname}    ${state}
    Sleep    5
    #点击    //div[@class='activity-item']
    点击卡片    ${taskname}
    Sleep    3
    当前页面可见    //span[@class='ng-star-inserted'][contains(text(),'提交')]
    Wait Until Element Is Enabled    //span[@class='ng-star-inserted'][contains(text(),'提交')]
    Sleep    5
    点击    //span[@class='ng-star-inserted'][contains(text(),'提交')]
    Sleep    5
    点击    //span[contains(text(),'确定')]
    Sleep    10

协同计划排定任务卡提交
    [Arguments]    ${taskname}    ${state}=null    ${progressAssertion}=null
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'我的任务')]
    刷新页面
    Sleep    10
    任务/项目名称搜索    ${taskname}    ${state}
    Sleep    5
    #点击    //div[@class='activity-item']
    点击卡片    协同计划排定
    Sleep    240
    点击    //span[@class='ng-star-inserted'][contains(text(),'提交')]
    当前页面可见    //p[contains(text(),'提交')]


修改任务
    [Arguments]    ${taskname}
    Sleep    10
    鼠标悬停    //div[@class='task-name' and contains(text(),'${taskname}')]
    js点击    //span[contains(text(),'修改')]
    #    双击    //input[@type='text' and @placeholder='请输入标题名称']
    #    Press Key    //input[@type='text' and @placeholder='请输入标题名称']    DELETE
    输入    //input[@type='text' and @placeholder='请输入标题名称']    ${taskname}修改
    点击    //button[@type='submit']
    当前页面不存在元素    //button[@type='submit']

查看任务
    [Arguments]    ${taskname}
    鼠标悬停    //div[@class='task-name' and contains(text(),'${taskname}')]
    js点击    //span[contains(text(),'查看')]
    点击    //button[contains(text(),'取消')]

删除任务
    [Arguments]    ${taskname}
    鼠标悬停    //div[@class='task-name' and contains(text(),'${taskname}')]
    js点击    //span[contains(text(),'删除')]
    点击    //span[contains(text(),'确定')]
    当前页面不可见字符    ${taskname}

收藏项目卡
    [Arguments]    ${projectname}
    Sleep    5
    点击    //div[@class='ant-dropdown-trigger']
    点击    //span[contains(text(),'收藏')]
    点击    //span[contains(text(),'确认')]
    #断言
    should contain    //span[contains[text(),$'收藏成功)]    收藏成功

复制项目的分享链接
    [Arguments]    ${projectname}
    Comment    点击    //li[contains(text(),'待办')]
    Comment    点击    //span[contains(text(),'搜索')]
    Comment    Ente Text    ${projectname}
    Comment    点击    //span[@type='addon']
    Comment    点击    //div[@class='activity-display']
    点击    //i[@class='project-operate ng-star-inserted']//*[@class='iconfont']
    #断言
    should contain    //span[contains(text(),'复制成功')]]    复制成功

签核进度
#    ${elements}    获取元素集合    //div[@class='checkpoint-name']
#    ${length}=    Get Length    ${elements}
#    FOR    ${index}    IN RANGE    ${length}
#        ${ele}    Get From List    ${elements}    ${index}
#        ${title}    Get Text    ${ele}
#        Run Keyword If    '${title}'!='测试机制不可删除'    Append To List    ${ele_list}    ${title}
#    END
#    #按名称循环删除机制
#    ${length}=    Get Length    ${ele_list}
#    FOR    ${index_1}    IN RANGE     ${length}
#        ${ele}    Get From List    ${ele_list}    ${index_1}
#        删除机制    ${ele}
#    END
    点击    //div[contains(text(),'查看签核进度')]
#    ${text}    获取元素字符    //div[@class='progress']/ul/li[1]//sign-progress
    判断元素的text为    xpath=(//div[@class='checkpoint-name'])[1]    发起人
    判断元素的text为    xpath=(//div[@class='checkpoint-name'])[2]    知会 直属主管签核
    判断元素的text为    xpath=(//div[@class='checkpoint-name'])[3]    直属主管签核
    判断元素的text为    xpath=(//div[@class='checkpoint-name'])[4]    发起人
    判断元素的text为    xpath=(//div[@class='checkpoint-name'])[5]    知会 直属主管签核
    判断元素的text为    xpath=(//div[@class='checkpoint-name'])[6]    直属主管签核
#    判断元素的text为    xpath=(//div[@class='checkpoint-name'])[7]    测试环境自动化测试小AI平台 加签
    判断元素的text为    xpath=(//div[@class='checkpoint-name'])[8]    知会 直属主管签核
    判断元素的text为    xpath=(//div[@class='checkpoint-name'])[9]    直属主管签核
#    判断元素的text为    xpath=(//div[@class='checkpoint-name'])[10]   测试环境自动化测试小AI平台 加签
    判断元素的text为    xpath=(//div[@class='checkpoint-name'])[11]    知会 项目经理签核
    判断元素的text为    xpath=(//div[@class='checkpoint-name'])[12]    项目经理签核

判断元素的text为
    [Arguments]    ${loc}    ${text}
    # 获取元素的文本内容
    ${element_text}=    获取元素字符    ${loc}
    # 判断元素的文本内容是否为指定值
    Should Be Equal As Strings    ${element_text}    ${text}

签核同意
    [Arguments]    ${taskname}    ${state}=null    ${progressAssertion}=null
    跳转网页    /todo/task
    点击    //div[contains(text(),'我的任务')]
    任务/项目名称搜索    ${taskname}    ${state}
    点击卡片    ${taskname}
    Sleep    3
    当前页面可见    //span[text()=' 同意 ']
    点击    //span[text()=' 同意 ']
    点击    //span[contains(text(),'确定')]
    #判断页面出新签核同意的签章
    当前页面可见    //p[contains(text(),'同意')]
    Run Keyword If    '${progressAssertion}' != 'null'    Run Keywords    点击    //div[contains(text(),'查看流程进度')]    当前页面包含元素    //div[contains(text(),'${progressAssertion}')]    5

启动后变更签核同意
    [Arguments]    ${taskname}    ${state}=null    ${progressAssertion}=null    ${text}=null
    Comment    点击    //div[contains(text(),'待办')]
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'我的任务')]
    #任务/项目名称搜索    ${taskname}    ${state}
    视图模式设定    卡片
    清空筛选
    #等待页面查询完成的loading消失
    当前页面不可见元素    //span[@class='ant-spin-dot ant-spin-dot-spin ng-star-inserted']
    元素存在则点击    //div[contains(@class,'todo-search-container')]//span[contains(text(),'搜索')]    20
    输入    //input[@placeholder='可用空格分隔关键词']    ${taskname}
    点击    //span[@type='addon']
    #点击    //div[@class='activity-item']
    点击卡片    ${text}
    当前页面可见    //span[text()=' 同意 ']
    点击    //span[text()=' 同意 ']
    点击    //span[contains(text(),'确定')]
    #判断页面出新签核同意的签章
    当前页面可见    //p[contains(text(),'同意')]
    Run Keyword If    '${progressAssertion}' != 'null'    Run Keywords    点击    //div[contains(text(),'查看流程进度')]    当前页面包含元素

签核不同意
    [Arguments]    ${taskname}
    跳转网页    /todo/task
    点击    //div[contains(text(),'我的任务')]
    任务/项目名称搜索    ${taskname}
    #点击    //div[@class='activity-item']
    点击卡片    ${taskname}
    点击    //span[contains(text(),'不同意')]
    输入    //textarea    这是不同意原因输入文本
    点击    //span[contains(text(),'确定')]
    #后续此处加上wait until 替换sleep
    当前页面可见    //p[contains(text(),'不同意')]


切换待办桌面页签
    [Arguments]    ${tabname}
    点击    //div[contains(text(),$'{tabname}')]

批量任务转派
    [Arguments]    ${taskname}    ${reassignUsername}
    #搜索发出的pcc任务卡
    #点击代办
    Sleep    5
    #这里识别不到待办,先跳转网页
    跳转网页    /todo/task
    点击    //div[contains(text(),'我的任务')]
    任务/项目名称搜索    ${taskname}
    转派    ${taskname}    ${reassignUsername}

批量项目转派
    [Arguments]    ${projectname}    ${reassignUsername}
    #搜索发出的pcc任务卡
    #点击代办
    Sleep    5
    #这里识别不到待办,先跳转网页
    跳转网页    /todo/project
    任务/项目名称搜索    ${projectname}
    转派    ${projectname}    ${reassignUsername}

启动项目
    点击    //p[contains(text(),'启动项目')]
    点击    //span[contains(text(),'确定')]
    #如果没有前置任务此处会弹出继续按钮，否则就不弹，需要处理一下，超时时间给 30s
    元素存在则点击    //span[contains(text(),'继续')]    30
    Sleep    10
    #此处加上启动成功的判断，超时时间60s
    刷新页面
    当前页面不可见字符    启动项目    60
    当前页面若文本存在自动刷新页面  10  存在任务正在下发或更新中

启动项目(繁体)
    点击    //p[contains(text(),'啟動專案')]
    点击    //span[contains(text(),'確定')]
    #如果没有前置任务此处会弹出继续按钮，否则就不弹，需要处理一下，
    元素存在则点击    //span[contains(text(),'繼續')]    8
    Sleep    10
    当前页面若文本存在自动刷新页面  10  存在任务正在下发或更新中


新项目知会
    ${str}    Catenate    SEPARATOR=    ${projectNo}    [${projectname}]
    ${text}    Set Variable    新增【09/15-09/17 ${str}】
    #${text}    Set Variable    【${projectNo}】${projectname}已启动！
    打开消息页
    当前页面可见字符    ${text}    300

签核前知会
    ${str}    Catenate    SEPARATOR=    【${projectNo}    [${projectname}]】
    ${text1}    Set Variable    【09/15需完成${posttaskname}签核】
    ${text2}    Set Variable    知会通知，属于${str}项目
    打开消息页
    当前页面可见字符    ${text1}    300
    当前页面可见字符    ${text2}    300

签核后知会
    ${str} =    Catenate    SEPARATOR=    【${projectNo}    [${projectname}]】
    ${text1}    Set Variable    【09/15已完成${posttaskname}签核】
    ${text2}    Set Variable    知会通知，属于${str}项目
    打开消息页
    #偶现通知发出的时间近5分钟，此处加上超时5分钟
    当前页面可见字符    ${text1}    300
    当前页面可见字符    ${text2}    300

项目转派
    [Arguments]    ${projectname}    ${reassignUsername}
    #项目详情内的转派
    #搜索发出的pcc任务卡
    #点击代办
    Sleep    5
    #这里识别不到待办,先跳转网页
    #    跳转网页    /todo/project
    跳转到待办页    /todo/project
    任务/项目名称搜索    ${projectname}
    点击卡片    ${projectname}
    详情内转派    ${projectname}    ${reassignUsername}

任务转派
    [Arguments]    ${task}    ${reassignUsername}
    #项目详情内的转派
    #搜索发出的pcc任务卡
    #点击代办
    Sleep    5
    #这里识别不到待办,先跳转网页
    跳转到待办页    /todo/task
    任务/项目名称搜索    ${task}
    点击卡片    ${task}
    详情内转派    ${task}    ${reassignUsername}

打开详情页更多
    js点击    //div[@class='ant-dropdown-trigger'][@nztrigger='click']

详情内转派
    [Arguments]    ${projectname}    ${reassignUsername}
    #项目详情和任务详情页面的转派操作,2者的操作一致,所以共用方法
    #此处的按钮可能被消息弹窗遮挡所以用js点击的方法
    打开详情页更多
    Sleep    2
    js点击    //li[contains(text(),'转派')]
    #转派人选择
    Sleep    3
    输入    //input[@class="ant-select-selection-search-input ng-untouched ng-pristine ng-valid"][not(@readonly)]    ${reassignUsername}
    #转派人
    点击    //span[contains(text(),'${reassignUsername}')]
    输入    //textarea[@class='ant-input ath-input ng-untouched ng-pristine ng-valid ng-star-inserted']    转派UI自动化测试
    点击    //button[@class='ath-btn ant-btn button ant-btn-primary']//span[@class='ng-star-inserted'][contains(text(),'确定')]
    元素存在则点击    //span[contains(text(),'继续')]    20
    Sleep    5

转派
    [Arguments]    ${projectname}    ${reassignUsername}
    #待办页面的批量转派操作
    点击    //span[contains(text(),'更多')]
    点击    //div[contains(text(),'批量转派')]
    Sleep    2
    #勾选被转派的项目卡
    点击    //label[@class='batch-checkbox ant-checkbox-wrapper ath-checkbox-wrapper ng-untouched ng-pristine ng-valid ng-star-inserted']
    点击    //span[contains(text(),'确定')]
    #转派人选择
    点击    //input[@class="ant-select-selection-search-input ng-untouched ng-pristine ng-valid"]
    #转派人
    点击    //span[contains(text(),'${reassignUsername}')]
    输入    //textarea[@class='ant-input ath-input ng-untouched ng-pristine ng-valid ng-star-inserted']    转派UI自动化测试
    点击    //button[@class='ath-btn ant-btn button ant-btn-primary']//span[@class='ng-star-inserted'][contains(text(),'确定')]
    Sleep    5

项目转派后断言
    [Arguments]    ${projectname}    ${state}=null
    跳转到待办页    /todo/project
    任务/项目名称搜索    ${projectname}
    鼠标悬停    //span[@class='card-item-name-title'][contains(text(),'${projectname}')]
#    当前页面可见    //span[@class='todo-card-supplement-item-value ng-star-inserted'][contains(text(),'${state}')]
    当前页面可见    //span[@class='todo-card-supplement-item-value'][contains(text(),'${state}')]

任务转派后断言
    [Arguments]    ${taskname}    ${state}=null
    跳转到待办页    /todo/task
    任务/项目名称搜索    ${taskname}
    鼠标悬停    //div[@class='todo-card-item-container ng-star-inserted']//*[contains(text(),'${taskname}')]
    当前页面可见    //div[@class='todo-card-supplement-info'][contains(text(),'${state}')]

报工
    [Arguments]    ${taskname}    ${state}=null
    #搜索发出的pcc任务卡
    #点击代办
    #这里识别不到待办,先跳转网页
    跳转网页    /todo/task
    点击    //div[contains(text(),'我的任务')]
    任务/项目名称搜索    ${taskname}    ${state}
    #点击    //div[@class='activity-info-box']
    #点击    //span[contains(text(),'${taskname}')]
    点击卡片    ${taskname}
    当前页面可见    //span[contains(text(),'一键报工')]
    sleep    10
    点击    //span[contains(text(),'一键报工')]
    Sleep    3
    点击    //span[contains(text(),'提交')]
    #    Sleep    3
    点击    //span[contains(text(),'确定')]
    当前页面可见字符    撤回

报工(繁体)
    [Arguments]    ${taskname}    ${state}=null
    #搜索发出的pcc任务卡
    #点击代办
    #这里识别不到待办,先跳转网页
    跳转网页    /todo/task
    点击    //div[contains(text(),'我的任務')]
    任务/项目名称搜索(繁体)    ${taskname}    ${state}
    #点击    //div[@class='activity-info-box']
    #点击    //span[contains(text(),'${taskname}')]
    点击卡片    ${taskname}
    当前页面可见    //span[contains(text(),'一鍵報工')]
    sleep    10
    点击    //span[contains(text(),'一鍵報工')]
    Sleep    3
    点击    //span[contains(text(),'提交')]
    #    Sleep    3
    点击    //span[contains(text(),'確定')]
    当前页面可见字符    撤回

报工后撤回
    [Arguments]    ${taskname}    ${state}=null
    #搜索发出的pcc任务卡
    #点击代办
    #这里识别不到待办,先跳转网页
    跳转网页    /todo/task
    点击    //div[contains(text(),'我的任务')]
    任务/项目名称搜索    ${taskname}    ${state}
    #点击    //div[@class='activity-info-box']
    #点击    //span[contains(text(),'${taskname}')]
    点击卡片    ${taskname}
    当前页面可见    //span[contains(text(),'一键报工')]
    当前页面可见    //div[contains(text(),'待处理')]
    点击    //span[contains(text(),'一键报工')]
    Sleep    3
    点击    //span[contains(text(),'提交')]
    #    Sleep    3
    点击    //span[contains(text(),'确定')]
    Sleep    30
    点击    //span[contains(text(),'撤回')]
    点击    //span[contains(text(),'确定')]
    Sleep    5

项目信息录入
    [Arguments]    ${info}    ${content}
    输入    //p[contains(text(),'${info}')]/following::input[1]    ${content}
    Press Key    //p[contains(text(),'${info}')]/following::input[1]    \\13

退回重办
    [Arguments]    ${taskname}
    #搜索生成的签核卡
    #待办跳转
    跳转网页    /todo/task
    点击    //div[contains(text(),'我的任务')]
    #搜索    ${taskname}
    任务/项目名称搜索    ${taskname}
    #    Sleep    5
    点击卡片    ${taskname}
    当前页面可见    //span[contains(text(),'退回重办')]
    点击    //span[contains(text(),'退回重办')]
    点击    //textarea[@id='re_opinion']
    Sleep    3
    输入    //textarea[@id='re_opinion']    重办
    点击    //span[contains(text(),'确定')]
    当前页面可见    //p[contains(text(),'退回重办')]

退回重签
    [Arguments]    ${taskname}
    #搜索生成的签核卡
    #待办跳转
    跳转网页    /todo/task
    点击    //div[contains(text(),'我的任务')]
    #搜索    ${taskname}
    任务/项目名称搜索    ${taskname}
    点击卡片    ${taskname}
    当前页面可见    //span[contains(text(),'退回重签')]
    点击    //span[contains(text(),'退回重签')]
    Sleep    3
    输入    //textarea    自动化测试退回重签备注
    点击    //span[contains(text(),'确定')]
    点击    //span[contains(text(),'确定')]
    当前页面可见    //p[contains(text(),'退回重签')]

点击卡片
    [Arguments]    ${taskname}
    当前页面可见    (//div[@class='todo-card-item-container ng-star-inserted']//*[contains(text(),'${taskname}')])[1]
    点击    (//div[@class='todo-card-item-container ng-star-inserted']//*[contains(text(),'${taskname}')])[1]
    Sleep    3
    #等待loading消失
    当前页面不可见元素    //span[@class='ant-spin-dot ant-spin-dot-spin ng-star-inserted']

暂停项目
    [Arguments]    ${projectname}
    #搜索
    跳转到待办页    /todo/project
    点击    //div[contains(text(),'我的项目')]
    任务/项目名称搜索    ${projectname}
    点击    //span[contains(text(),'${projectname}')]
    Sleep    1
    点击    //div[contains(text(),'暂停项目')]
    Sleep    1
    点击    //span[contains(text(),'确定')]
    Sleep    1
    #点击    //div[contains(text(),'请选择')]
    #点击    //div[@class='open-txt ng-star-inserted']
    #点击    //i[@class='anticon kaichuang-icon']
    #此处原因选择改来改去...
    点击    //span[@class='label-name']/following::div[1]
    当前页面不可见字符    数据加载中
    #Sleep    5
    输入    //div[@class='ath-input-content-box ng-star-inserted']//input[@type='text']    正常结案
    #sleep    5
    点击    //button[@class='ath-btn ant-btn ant-btn-primary ant-btn-sm ant-btn-icon']
    #sleep    3
    Execute Javascript    document.querySelector('.ant-radio-input').click()
    #Sleep    1
    点击    //button[@class='ath-btn ant-btn ant-btn-primary ng-star-inserted active']//span[contains(text(),'确定')]
    Sleep    5
    输入    //textarea[@maxlength='255']    已完成
    点击    //span[contains(text(),'确定')]
    Sleep    5

撤回
    [Arguments]    ${taskname}
    点击    //span[contains(text(),'撤回')]
    点击    //span[contains(text(),'确定')]

加签
    [Arguments]    ${taskname}    ${additionalSignatory}    ${state}=null
    #待办跳转
    跳转网页    /todo/task
    点击    //div[contains(text(),'我的任务')]
    #搜索    ${taskname}
    任务/项目名称搜索    ${taskname}    ${state}
    点击卡片    ${taskname}
    点击    //span[contains(text(),'加签')]
    #点击    //input[@class='ant-select-selection-search-input ng-untouched ng-pristine ng-valid']
    Sleep    2
    输入    //div[@role='document']//input[@class='ant-select-selection-search-input ng-untouched ng-pristine ng-valid']    ${additionalSignatory}
    Sleep    2
    点击    //span[contains(text(),'${additionalSignatory}')]
    点击    //textarea[@id='opinion']
    输入    //textarea[@id='opinion']    加一个节点
    Sleep    1
    点击    //span[contains(text(),'确定')]
    Sleep    1
    点击    //span[contains(text(),'确定')]
    当前页面可见    //p[contains(text(),'加签')]

任务卡关注
    [Arguments]    ${taskname}
    点击    //div[contains(text(),'待办')]
    点击    //div[contains(text(),'我的任务')]
    点击    //span[contains(text(),'搜索')]
    Ente Text    ${taskname}
    点击    //span[@type='addon']
    点击    //i[@class='font-class more-button']

结案
    [Arguments]    ${projectname}    ${status}=已结束
    #待办无法识别,先用url直接跳转
    跳转网页    /todo/project
    点击    //div[contains(text(),'我的项目')]
    sleep    3
    任务/项目名称搜索    ${projectname}
    #    sleep    5
    #    点击    //span[contains(text(),'${projectname}')]
    点击卡片    ${projectname}
    点击    //div[contains(text(),'结案')]
    #    Sleep    1
    #    点击    //div[contains(text(),'请选择')]
    #    Sleep    5
    #    输入    //div[@class='ath-input-content-box ng-star-inserted']//input[@type='text']    正常结案
    #    sleep    5
    #    点击    //button[@class='ath-btn ant-btn ant-btn-primary ant-btn-sm ant-btn-icon']
    #    sleep    3
    #    Selenium2Library.Execute Javascript    document.querySelector('.ant-radio-input').click()
    #    Sleep    1
    #    点击    //button[@class='ath-btn ant-btn ant-btn-primary ng-star-inserted active']//span[contains(text(),'确定')]
    #    Sleep    5
    输入    //textarea[@maxlength='255']    已完成
    点击    //span[contains(text(),'确定')]
    Sleep    10
    Reload Page
    Sleep    10
    当前页面可见字符    ${status}


结案后任务卡信息查询
    点击    //div[contains(text(),'进度追踪')]
    点击     //div[@class='ath-tooltip-wrapper-inner']//div[@class='task-name'][contains(text(),'${taskname}')]
    点击    //span[contains(text(),'已完成')]
    当前页面可见    //div[contains(text(),'100%')]



子母项目结案
    [Arguments]    ${projectname}
    #待办无法识别,先用url直接跳转
    跳转网页    /todo/project
    点击    //div[contains(text(),'我的项目')]
    sleep    3
    任务/项目名称搜索    ${projectname}
    sleep    5
    点击    //span[contains(text(),'${projectname}')]
    Sleep    15
    点击    //button[contains(text(),'结案')]
    #    Sleep    1
    #    点击    //span[@class='label-name']/following::div[1]
    #    当前页面不可见字符    数据加载中
    #    #Sleep    5
    #    输入    //div[@class='ath-input-content-box ng-star-inserted']//input[@type='text']    正常结案
    #    #sleep    5
    #    点击    //button[@class='ath-btn ant-btn ant-btn-primary ant-btn-sm ant-btn-icon']
    #    #sleep    3
    #    Selenium2Library.Execute Javascript    document.querySelector('.ant-radio-input').click()
    #    #Sleep    1
    #    点击    //button[@class='ath-btn ant-btn ant-btn-primary ng-star-inserted active']//span[contains(text(),'确定')]
    #    #此处的sleep不可注释
    #    Sleep    5
    #    输入    //textarea[@maxlength='255']    已完成
    点击    //span[contains(text(),'确定')]
    Sleep    10
    #    Reload Page
    #    当前页面可见字符    已结束
    当前页面若文本不存在自动刷新页面    5    已结束

指定结案
    [Arguments]    ${projectname}
    #待办无法识别,先用url直接跳转
    跳转网页    /todo/project
    点击    //div[contains(text(),'我的项目')]
    sleep    3
    任务/项目名称搜索    ${projectname}
    sleep    5
    点击    //span[contains(text(),'${projectname}')]
    点击    //div[contains(text(),'指定结案')]
    Sleep    1
    点击    //span[@class='label-name']/following::div[1]
    当前页面不可见字符    数据加载中
    #Sleep    5
    输入    //div[@class='ath-input-content-box ng-star-inserted']//input[@type='text']    正常结案
    #sleep    5
    点击    //button[@class='ath-btn ant-btn ant-btn-primary ant-btn-sm ant-btn-icon']
    #sleep    3
    Execute Javascript    document.querySelector('.ant-radio-input').click()
    #Sleep    1
    点击    //button[@class='ath-btn ant-btn ant-btn-primary ng-star-inserted active']//span[contains(text(),'确定')]
    #此处的sleep不可注释
    Sleep    5
    输入    //textarea[@maxlength='255']    已完成
    点击    //span[contains(text(),'确定')]
    Sleep    10
    #    Reload Page
    #    当前页面可见字符    已结束
    当前页面若文本不存在自动刷新页面    5    已结束

指定结案(繁体)
    [Arguments]    ${projectname}
    #待办无法识别,先用url直接跳转
    跳转网页    /todo/project
    点击    //div[contains(text(),'我的項目')]
    sleep    3
    任务/项目名称搜索(繁体)    ${projectname}
    sleep    5
    点击    //span[contains(text(),'${projectname}')]
    点击    //div[contains(text(),'指定結案')]
    Sleep    1
    点击    //span[@class='label-name']/following::div[1]
    当前页面不可见字符    數據載入中
    #Sleep    5
    输入    //div[@class='ath-input-content-box ng-star-inserted']//input[@type='text']    正常结案
    #sleep    5
    点击    //button[@class='ath-btn ant-btn ant-btn-primary ant-btn-sm ant-btn-icon']
    #sleep    3
    Execute Javascript    document.querySelector('.ant-radio-input').click()
    #Sleep    1
    点击    //button[@class='ath-btn ant-btn ant-btn-primary ng-star-inserted active']//span[contains(text(),'確定')]
    #此处的sleep不可注释
    Sleep    5
    输入    //textarea[@maxlength='255']    已完成
    点击    //span[contains(text(),'確定')]
    Sleep    10
    #    Reload Page
    #    当前页面可见字符    已结束
    当前页面若文本不存在自动刷新页面    5    已結束


继续项目
    [Arguments]    ${projectname}
    点击    //div[contains(text(),'继续项目')]
    Sleep    2
    点击    //span[contains(text(),'确定')]
    Sleep    1
    点击    //div[@class='change-reason-type ng-star-inserted']
    当前页面不可见字符    数据加载中
    #    Sleep    1
    输入    //div[@class='ath-input-content-box ng-star-inserted']//input[@type='text']    正常结案
    #    sleep    1
    点击    //button[@class='ath-btn ant-btn ant-btn-primary ant-btn-sm ant-btn-icon']
    #    sleep    3
    Execute Javascript    document.querySelector('.ant-radio-input').click()
    #    Sleep    3
    点击    //button[@class='ath-btn ant-btn ant-btn-primary ng-star-inserted active']//span[contains(text(),'确定')]
    #    Sleep    3
    输入    //section[@class='ant-input-group ath-input-group-wrap-inner']//input[@type='text']    继续项目备注
    Sleep    5
    点击    //span[contains(text(),'确认')]
    #    Sleep    2
    #    点击    //span[contains(text(),'是')]
    Sleep    5

搜索
    [Arguments]    ${searchname}
    #搜索
    任务/项目名称搜索    ${searchname}
    sleep    20

项目卡添加至他人Athena
    [Arguments]    ${projectname}    ${sharingUsernameCN}
    #搜索发出的pcc任务卡
    #点击代办
    Sleep    5
    #这里识别不到待办,先跳转网页
    跳转网页    /todo/project
    任务/项目名称搜索    ${projectname}
    项目卡分享    ${projectname}    ${sharingUsernameCN}

项目卡分享
    [Arguments]    ${projectname}    ${SharingName}
    点击    //span[contains(text(),'${projectname}')]
    js点击    //div[@class='project-operate-list ng-star-inserted']/div
    js点击    //span[contains(text(),'添加至他人Athena')]
    Comment    #勾选被分享的项目卡
    Comment    点击    //label[@class='batch-checkbox ant-checkbox-wrapper ath-checkbox-wrapper ng-untouched ng-pristine ng-valid ng-star-inserted']
    Comment    点击    //span[contains(text(),'确定')]
    #转派人选择
    输入    //input[@placeholder="请输入关键字"]    ${SharingName}
    #转派人
    点击    //span[contains(text(),'全选搜索结果')]
    Sleep    1
    点击    //span[contains(text(),'确定')]
    Should Contain    //span[contains(text(),'添加成功')]    添加成功
    Sleep    5

被分享项目卡
    [Arguments]    ${projectname}
    #取消分享
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'我的项目')]
    任务/项目名称搜索    ${projectname}
    鼠标悬停    //span[contains(text(),'${projectname}')]
    当前页面可见字符    分享人
#    Sleep    5
    点击    //span[contains(text(),'取消查看')]
#    Sleep    10
#    #搜索项目卡
#    #此处后面看怎么优化
#    视图模式设定    卡片
#    清空筛选
#    #点击    //span[contains(text(),'搜索')]
#    ${element_exists}    Run Keyword And Return Status    Element Should Be Visible    //i[@class="anticon ng-star-inserted"]
#    Run Keyword If    ${element_exists}    点击    //i[@class="anticon ng-star-inserted"]
#    ...    ELSE    Log    Element does not exist.
#    输入    //input[@placeholder='可用空格分隔关键词']    ${projectname}
#    点击    //span[@type='addon']/button/i
#    Sleep    3
    #当前页面可见    //span[contains(text(),'${name}')]
    #当前页面可见    //span[@class='card-item-name-title'][contains(text(),'${name}')]
    #再次执行一次搜索，因为搜索里面有断言，会失败，此处忽略失败，最终结果有 当前页面是否存在 暂无项目需要跟踪 字符来判断，此处只是用来触发一次查询
    Run Keyword And Ignore Error    任务/项目名称搜索    ${projectname}
    当前页面可见字符    暂无项目需要跟踪哦~

分享项目卡详情展示
    [Arguments]    ${projectname}
    跳转网页    /todo/task
    点击    //div[contains(text(),'我的项目')]
    任务/项目名称搜索    ${projectname}
    点击    //span[contains(text(),'${projectname}')]
    当前页面不可见字符    暂停项目

任务卡分享
    [Arguments]    ${taskname}    ${SharingName}
    Sleep    2
    Comment    #勾选被分享的项目卡
    Comment    点击    //label[@class='batch-checkbox ant-checkbox-wrapper ath-checkbox-wrapper ng-untouched ng-pristine ng-valid ng-star-inserted']
    Comment    点击    //span[contains(text(),'确定')]
    #转派人选择
    输入    //input[@placeholder="请输入关键字"]    ${SharingName}
    #转派人
    点击    //span[contains(text(),'全选搜索结果')]
    点击    //span[contains(text(),'确定')]
#    Should Contain    //span[contains(text(),'添加成功')]    添加成功
    当前页面可见字符    添加成功

被分享任务卡
    [Arguments]    ${taskname}
    #取消分享
    跳转网页    /todo/task
    点击    //div[contains(text(),'我的任务')]
    任务/项目名称搜索    ${taskname}
    鼠标悬停    //span[contains(text(),'${taskname}签核')]
    当前页面可见字符    分享人
    点击    //span[contains(text(),'取消查看')]
    #搜索项目卡
    #此处后面看怎么优化
#    视图模式设定    卡片
#    清空筛选
#    #点击    //span[contains(text(),'搜索')]
#    ${element_exists}    Run Keyword And Return Status    Element Should Be Visible    //i[@class="anticon ng-star-inserted"]
#    Run Keyword If    ${element_exists}    点击    //i[@class="anticon ng-star-inserted"]
#    ...    ELSE    Log    Element does not exist.
#    输入    //input[@placeholder='可用空格分隔关键词']    ${taskname}
#    点击    //span[@type='addon']
#    Sleep    3
#    #当前页面可见    //span[contains(text(),'${name}')]
#    #当前页面可见    //span[@class='card-item-name-title'][contains(text(),'${name}')]
#    Sleep    10
    #再次执行一次搜索，因为搜索里面有断言，会失败，此处忽略失败，最终结果有 当前页面是否存在 暂无工作需要处理哦 字符来判断，此处只是用来触发一次查询
    Run Keyword And Ignore Error    任务/项目名称搜索    ${taskname}
    当前页面可见字符    暂无工作需要处理哦

被分享的任务卡详情展示
    [Arguments]    ${taskname}
    跳转网页    /todo/task
    任务/项目名称搜索    ${taskname}
    点击    //span[contains(text(),'${taskname}')]
    当前页面可见    //span[contains(text(),'同意')]
    Sleep    3

#a分享项目卡给b，b不能操作项目卡，a分享任务卡给b，b能操作任务卡
任务卡添加至他人Athena
    [Arguments]    ${username}
    Comment    点击    //div[contains(text(),'待办')]
    跳转网页    /todo/task
    点击    //div[contains(text(),'我的任务')]
    Comment    点击    //span[contains(text(),'搜索')]
    Comment    Ente Text    ${taskname}
    Comment    点击    //span[@type='addon']
    任务/项目名称搜索    ${taskname}
    #点击    //div[@class='activity-item']
    点击卡片    ${taskname}
    当前页面可见    //span[contains(text(),'同意')]
    #任务卡添加至他人Athena
#    点击    //div[@class="icon-box ng-star-inserted"]/div/*[name()='svg']
    打开详情页更多
    点击    //span[contains(text(),'添加至他人Athena')]
    任务卡分享    ${taskname}    ${username}

向前加签
    [Arguments]    ${taskname}    ${additionalSignatory}
    #进入待办
    跳转网页    /todo/task
    点击    //div[contains(text(),'我的任务')]
    #搜索    ${taskname}
    任务/项目名称搜索    ${taskname}
    点击卡片    ${taskname}
    点击    //span[contains(text(),'加签')]
    点击    //div[@class="ant-spin-container ng-star-inserted"]/form/nz-form-item[1]/nz-form-control/div/div/ath-radio-group/label[1]/span[1]
    #点击    //input[@class='ant-select-selection-search-input ng-untouched ng-pristine ng-valid']
    Sleep    2
    输入    //div[@role='document']//input[@class='ant-select-selection-search-input ng-untouched ng-pristine ng-valid']    ${additionalSignatory}
    Sleep    2
    点击    //span[contains(text(),'${additionalSignatory}')]
    点击    //textarea[@id='opinion']
    输入    //textarea[@id='opinion']    添加一个节点
    Sleep    1
    点击    //span[contains(text(),'确定')]
    Sleep    1
    点击    //span[contains(text(),'确定')]
    Sleep    10

前置任务选择
    [Arguments]    ${taskname}
    点击    //span[contains(text(),'前置任务')]/following::input[@class='ant-select-selection-search-input ng-untouched ng-pristine ng-valid'][1]
    点击    //span[@title='${taskname}']

项目删除
    [Arguments]    ${projectname}
    跳转到待办页    /todo/project
    点击    //div[contains(text(),'我的项目')]
    任务/项目名称搜索    ${projectname}
    点击卡片    ${projectname}
#    Sleep    10
#    点击    //i[@athtooltipplacement='bottom'][@ngxclipboard='']
#    当前页面可见字符    复制成功
#    ${url}    get_clipboard_data
#    Go To    ${url}
#    Sleep    5
    Sleep    10
    点击    //p[contains(text(),'项目删除')]
    点击    //span[contains(text(),'确定')]
    点击    //span[contains(text(),'确定')]
    #删除完了验证
    跳转网页    /todo/project
    刷新页面
    当前页面不存在元素    //span[contains(text(),'${projectname}')]

项目删除(删除)
    [Arguments]    ${projectname}
    跳转到待办页    /todo/project
    点击    //div[contains(text(),'我的項目')]
    任务/项目名称搜索    ${projectname}
    点击卡片    ${projectname}
#    Sleep    10
#    点击    //i[@athtooltipplacement='bottom'][@ngxclipboard='']
#    当前页面可见字符    复制成功
#    ${url}    get_clipboard_data
#    Go To    ${url}
#    Sleep    5
    点击    //p[contains(text(),'專案刪除')]
    点击    //span[contains(text(),'確定')]
    点击    //span[contains(text(),'確定')]
    #删除完了验证
    跳转到待办页    /todo/project
    刷新页面
    当前页面不存在元素    //span[contains(text(),'${projectname}')]


项目详情里程碑切换
    [Arguments]    ${taskname}
    跳转网页    /todo/task
    点击    //div[contains(text(),'我的项目')]
    #搜索    ${taskname}
    任务/项目名称搜索    ${taskname}
    点击卡片    ${taskname}
    js点击    //div[contains(text(),'项目发起签核')]
    当前页面可见字符    暂无数据
    点击    //div[contains(text(),'项目计划维护')]
    当前页面可见字符    ${taskname}
    点击    //div[contains(text(),'项目启动签核')]
    当前页面可见字符    签核
    点击    //div[contains(text(),'派工')]
    当前页面可见字符    暂无数据
    点击    //div[contains(text(),'进度追踪')]
    当前页面可见字符    暂无数据
    当前页面可见字符    ${taskname}

任务删除
    [Arguments]    ${taskname}
    鼠标悬停    //div[@class='wbs-card-container no-track-pages']//*[contains(text(),'${taskname}')]
    点击    删除
    点击    确定

历史项目/任务查询转派记录
    [Arguments]    ${return}
    js点击    //div[@title='${return}']
    打开详情页更多
    js点击    //li[contains(text(),'转派记录')]
    当前页面可见    //h3[contains(text(),'转派记录')]
    当前页面可见    //p[contains(text(),'转派UI自动化测试')]

添加收藏
    [Arguments]    ${key}
    #项目卡添加详情
    项目卡详情添加收藏    ${key}
    查询项目卡快照    ${key}
    根据时间筛选快照    1    30
    根据关键字搜索快照    ${key}
    进入快照详情    ${key}
    删除快照
    #任务卡添加详情
    任务详情添加收藏    ${key}
    查询任务卡快照    ${key}
    根据时间筛选快照    1    30
    根据关键字搜索快照    ${key}
    进入快照详情    ${key}
    删除快照
