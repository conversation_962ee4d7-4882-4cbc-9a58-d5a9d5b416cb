*** Settings ***
Documentation     高伟
Library           SeleniumLibrary
Resource          ../关键字/业务关键字/Athena平台控件.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/公共方法.robot
Resource          ../关键字/系统关键字/web.robot
Resource          ../配置/全局参数.robot
Test Setup        Run Keywords    环境设定
Test Teardown     Close Browser


*** Test Cases ***
常用-添加分组后删除
    ${ownerUsername}    Set Variable If    '${ENV}'=='private.test'    default
    ${ownerPassword}    Set Variable If    '${ENV}'=='private.test'    1qaz@WSX
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    点击常用
    新增分组    项目模板    项目类型
    输入分组名称后确定    测试组
    删除分组

常用-添加分组后 添加常用 再删除分组
    ${ownerUsername}    Set Variable If    '${ENV}'=='private.test'    default
    ${ownerPassword}    Set Variable If    '${ENV}'=='private.test'    1qaz@WSX
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    点击常用
    新增分组    项目模板    项目类型
    输入分组名称后确定    测试组
    新增分组后，添加常用    项目模板
    删除分组



