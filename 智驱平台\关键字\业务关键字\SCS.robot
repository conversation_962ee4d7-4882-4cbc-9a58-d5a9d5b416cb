*** Settings ***
Resource          SCS.robot
Resource          ../../元素/Athena平台元素.robot
Library           SeleniumLibrary
Resource          Athena平台.robot
Resource          PCC.robot
Resource          业务数据录入.robot

*** Keywords ***
维护单体基础资料
    [Arguments]    ${key}    ${project}    ${money}    ${report}    ${term}
    点击顶部菜单    全部
    进入基线标准作业    业务数据录入
    #维护公司信息
    #维护公司信息-客商信息
    查询所需作业    客商信息
    客商信息（单体）    transaction_party_no    ${key}    transaction_party_name    relation_party_flag
    关闭作业页签
    #维护公司信息-参照表
    查询所需作业    参照表
    参照表（单体）    account_std_no    ${project}    account_std_name    ${money}    reference_table_type    ${report}    ${term}
    关闭作业页签
    #维护公司信息-公司信息
    查询所需作业    公司信息
    公司信息（单体）    company_no    ${key}    function_currency_no    relation_party_no    ${key}    account_std_no1    ${project}    account_std_no2    ${money}    app_id

客商信息（单体）
    [Arguments]    ${transaction_party_no}    ${no}    ${transaction_party_name}    ${relation_party_flag}
    点击    //span[contains(text(),'新增')]
    ${ele_counts}    获取元素数量    //div[@class='ag-center-cols-clipper']/div/div/div
    点击    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${transaction_party_no}"]
    输入    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${transaction_party_no}"]//input    ${no}
    点击    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${transaction_party_name}"]
    输入    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${transaction_party_name}"]//input    ${no}
    点击    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${transaction_party_no}"]
    Comment    点击    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${relation_party_flag}"]//input
    点击    //button[@class='ath-btn ant-btn ant-btn-primary ant-btn-lg ng-star-inserted']
    当前页面可见字符    操作成功

参照表（单体）
    [Arguments]    ${account_std_no}    ${key}    ${account_std_name}    ${no}    ${reference_table_type}    ${plan}    ${aa}
    #科目参照表
    点击    //span[contains(text(),'新增')]
    ${ele_counts}    获取元素数量    //div[@class='ag-center-cols-clipper']/div/div/div
    点击    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${account_std_no}"]
    输入    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${account_std_no}"]//input    ${key}
    点击    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${account_std_name}"]
    Sleep    3
    点击    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${account_std_name}"]
    Sleep    3
    输入    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${account_std_name}"]//input    ${key}
    Sleep    3
    点击    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${account_std_no}"]
    点击    //button[@class='ath-btn ant-btn ant-btn-primary ant-btn-lg ng-star-inserted']
    当前页面可见字符    操作成功
    #现金流量参照表
    其它类型参照表    ${account_std_no}    ${no}    ${account_std_name}    ${reference_table_type}    现金流量参照表
    #报表项目参照表
    其它类型参照表    ${account_std_no}    ${plan}    ${account_std_name}    ${reference_table_type}    报表项目参照表
    #期间参照表
    其它类型参照表    ${account_std_no}    ${aa}    ${account_std_name}    ${reference_table_type}    期间参照表

公司信息（单体）
    [Arguments]    ${company_no}    ${key}    ${function_currency_no}    ${relation_party_no}    ${key}    ${account_std_no1}    ${project}    ${account_std_no2}    ${money}    ${app_id}
    点击    //span[contains(text(),'新增')]
    ${ele_counts}    获取元素数量    //div[@class='ag-center-cols-clipper']/div/div/div
    #组织编号
    点击    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${company_no}"]
    输入    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${company_no}"]//input    ${key}
    #组织名称
    点击    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${company_name}"]
    输入    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${company_name}"]//input    ${key}
    #本位币
    鼠标悬停    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${function_currency_no}"]
    点击    //div[@class='ag-center-cols-clipper']/div/div/div[44]/div[@col-id="${function_currency_no}"]/div[1]/span/cell-renderer/div[1]//span[@class="edit hidden-icon"]/i
    单选开窗选择    RMB
    #关系人编码
    鼠标悬停    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${relation_party_no}"]
    点击    //div[@class='ag-center-cols-clipper']/div/div/div[44]/div[@col-id="${relation_party_no}"]/div[1]/span/cell-renderer/div[1]//span[@class="edit hidden-icon"]/i
    单选开窗选择    ${key}
    #会计科目参照表
    鼠标悬停    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${account_std_no1}"]
    点击    //div[@class='ag-center-cols-clipper']/div/div/div[44]/div[@col-id="${account_std_no1}"]/div[1]/span/cell-renderer/div[1]//span[@class="edit hidden-icon"]/i
    单选开窗选择    ${project}
    #现金流量参照表
    鼠标悬停    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${account_std_no21}"]
    点击    //div[@class='ag-center-cols-clipper']/div/div/div[44]/div[@col-id="${account_std_no2}"]/div[1]/span/cell-renderer/div[1]//span[@class="edit hidden-icon"]/i
    单选开窗选择    ${money}
    #AP地址
    点击    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${app_id}"]
    输入    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${app_id}"]//input    ********
    点击    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="old_company_no"]
    点击    //button[@class='ath-btn ant-btn ant-btn-primary ant-btn-lg ng-star-inserted']
    当前页面可见字符    操作成功

单选开窗选择
    [Arguments]    ${a}
    输入    //div[@class="ath-input-content-box ng-star-inserted"]//input    ${a}
    点击    //div[@class="search-btns ng-star-inserted"]//span[contains(text(),'搜索')]
    点击    //ath-table[@class="table-container ag-theme-athena ng-star-inserted"]/div[2]/ag-grid-angular/div/div[2]/div[2]/div[3]/div[1]/div[1]/div/div/span//input
    点击    //span[contains(text(),'提交')]
    当前页面可见字符    操作成功

其它类型参照表
    [Arguments]    ${account_std_no}    ${no}    ${account_std_name}    ${reference_table_type}    ${type}
    刷新页面
    点击    //span[contains(text(),'新增')]
    ${ele_counts}    获取元素数量    //div[@class='ag-center-cols-clipper']/div/div/div
    点击    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${account_std_no}"]
    输入    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${account_std_no}"]//input    ${no}
    点击    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${account_std_name}"]
    Sleep    2
    点击    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${account_std_name}"]
    Sleep    2
    输入    //div[@class='ag-center-cols-clipper']/div/div/div[${ele_counts}]/div[@col-id="${account_std_name}"]//input    ${no}
    Sleep    2
    点击    //div[contains(text(),'${no}')]/ancestor::div[@role='row']/div[@col-id='${reference_table_type}']
    Sleep    5
    点击    //span[@class='option-item-label'][contains(text(),'${type}')]
    点击    //button[@class='ath-btn ant-btn ant-btn-primary ant-btn-lg ng-star-inserted']
    当前页面可见字符    操作成功
