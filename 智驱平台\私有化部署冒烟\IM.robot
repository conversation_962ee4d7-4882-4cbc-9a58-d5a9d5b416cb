*** Settings ***
Documentation     高伟
Library           SeleniumLibrary
Resource          ../关键字/业务关键字/Athena平台控件.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/公共方法.robot
Resource          ../关键字/系统关键字/web.robot
Resource          ../配置/全局参数.robot
Test Setup        Run Keywords    环境设定
Test Teardown     Close Browser



*** Keywords ***
IM菜单巡检
    [Arguments]    ${tenantName}    ${deptName}    ${name}
    ${ownerUsername}    Set Variable If    '${ENV}'=='private.test'    default
    ${ownerPassword}    Set Variable If    '${ENV}'=='private.test'    1qaz@WSX
    登录ATHENA平台    ${ownerUsername}    ${ownerPassword}
    点击即时对话
    当前页面包含元素    //div[contains(text(),"对话")]
    当前页面包含元素    //div[contains(text(),"通讯录")]
    当前页面包含元素    //div[contains(text(),"项目/任务历史对话")]
    当前页面包含元素    //div[contains(text(),"客服")]
    Sleep    2
    点击    //div[contains(text(),"通讯录")]
    当前页面元素包含    //p[contains(text(),"${tenantName}")]
    ${exist_fold}    判断元素是否可见    //div[@class="title fold"]
    Run Keyword If    ${exist_fold}    点击    //div[@class='friends']//div[@class='title']//*[local-name()='svg']
    Sleep    2
    当前页面包含元素    //p[contains(text(),"${deptName}")]
    ${exist_name}    判断元素是否可见    //span[contains(text(),"${name}")]
    Run Keyword If    ${exist_name}    点击    //p[contains(text(),"${deptName}")]
    Sleep    2
    当前页面包含元素    //span[contains(text(),"${name}")]