*** Settings ***
Library           SeleniumLibrary
Resource          ../系统关键字/web.robot
Resource          ../../元素/Athena平台元素.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../系统关键字/接口.robot
Resource          Athena平台控件.robot
Resource          Athena平台.robot
Resource          待办.robot
Resource          ../../配置/Athena平台.robot
Resource          待办.robot

*** Keywords ***
视图模式设定
    [Arguments]    ${mode}
    ${view}    获取元素字符    //div[@class='switch hover-active ng-star-inserted']
    Run Keyword If    '${view}'=='${mode}'    点击    //div[@class='switch hover-active ng-star-inserted']
    #Sleep    10

视图切换
    [Arguments]    ${type}
    点击    //div[contains(text(),'${type}')]
    ${view}    获取元素字符    //div[@class='switch hover-active ng-star-inserted']
    js点击    //div[@class='switch hover-active ng-star-inserted']
    Run Keyword If    '${view}'=='列表'    当前页面可见字符    摘要信息
    ...    ELSE    当前页面不可见字符    摘要信息

我的任务分组
    #    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #    登录ATHENA平台    ${username}    ${password}
    常规分组
    高级分组
    #    [Teardown]    Run Keywords    关闭浏览器

我的任务排序
    #    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #    登录ATHENA平台    ${username}    ${password}
    我的任务排序入口
    #    [Teardown]    Run Keywords    关闭浏览器

任务运营单元
    #    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #    登录ATHENA平台    ${username}    ${password}
    运营单元
    #    [Teardown]    Run Keywords    关闭浏览器

任务卡筛选
    #    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #    登录ATHENA平台    ${username}    ${password}
    常规筛选
    高级筛选
    #    [Teardown]    Run Keywords    关闭浏览器

任务显示
    [Arguments]    ${key}
    跳转网页    /todo/task
    Sleep    5
    js点击    //div[contains(text(),'我的任务')]
    点击    //span[contains(text(),'分组')]
    点击    //*[contains(text(),'常规分组')]
    点击    //span[contains(text(),'重置')]
    点击    //span[contains(text(),'确定')]
    ${state}    Run Keyword And Return Status    任务/项目名称搜索    ${key}
    Comment    ${key}    Get Element Count    //p[contains(text(),'更正采购员信息')]
    Run Keyword If    '${state}'=='False'
    ...    任务卡隐藏后显示    ${key}
    Run Keyword If    '${state}'=='True'
    ...    任务卡显示后隐藏    ${key}
    Log    敬请期待
    #    [Teardown]    Run Keywords    关闭浏览器

我的项目分组
    #    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestKm01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #    登录ATHENA平台    ${username}    ${password}
    项目常规分组
    项目高级分组

项目高级分组
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'我的项目')]
    点击    //span[contains(text(),'分组')]
    点击    //*[contains(text(),'高级分组')]
    点击    //span[contains(text(),'重置')]
    Sleep    2
    #项目类型分组
    点击    //div[@class="group-set-container ng-star-inserted"]/ath-tabs/div/div/div[2]/div/div/nz-cascader/div/div
    点击    //span[contains(text(),'项目类型')]
    点击    //div[@class='delete-condition ng-star-inserted']    #删除高级分组
    Sleep    2
    点击    //div[@class='group-set-container ng-star-inserted']/ath-tabs/div/div/div[2]/div/div/nz-cascader/div/div
    点击    //span[contains(text(),'项目类型')]
    点击    //span[contains(text(),'确定')]
    should contain    //h5[contains(text(),'分组目录')]    分组目录
    should contain    //p[contains(text(),'项目中控台')]    项目中控台
    Sleep    3

我的项目筛选
    Log    已在分享全链路中验证

我的项目排序
    #    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #    登录ATHENA平台    ${username}    ${password}
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'我的项目')]
    js点击    //span[contains(text(),'排序')]
    点击    //span[contains(text(),'重置')]
    Sleep    5
    #条件拖动
    Drag And Drop By Offset    //div[@class="cdk-drop-list sort-item-list"]/div[1]/div[1]    0    100
    Sleep    5
    点击    //span[contains(text(),'确定')]
    Sleep    5
    Should Contain    //div[contains(text(),'4')]    4
    Sleep    2
    #降序
    js点击    //span[contains(text(),'排序')]
    点击    //div[@class='cdk-drop-list sort-item-list']/div[1]/div[3]
    点击    //span[contains(text(),'确定')]
    Sleep    5
    #删除排序条件
    js点击    //span[contains(text(),'排序')]
    点击    //div[@class='cdk-drop-list sort-item-list']/div[1]/*[name()='svg']
    点击    //span[contains(text(),'确定')]
    Sleep    5

团队任务显示
    Log    敬请期待

团队任务分组
    #    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #    登录ATHENA平台    ${username}    ${password}
    团队任务常规分组
    团队任务高级分组

团队任务常规分组
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'团队任务')]
    js点击    //span[contains(text(),'分组')]
    点击    //*[contains(text(),'常规分组')]
    #不分组
    点击    //span[contains(text(),'不分组')]
    点击    //span[contains(text(),'确定')]
    Sleep    3
    js点击    //span[contains(text(),'分组')]
    按任务类型分组
    按是否逾期分组
    按项目类型分组
    按成员分组
    按部门分组

团队项目高级分组
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'我的项目')]
    js点击    //span[contains(text(),'分组')]
    点击    //*[contains(text(),'高级分组')]
    点击    //span[contains(text(),'重置')]
    Sleep    2
    #项目类型分组
    点击    //div[@class="group-set-container ng-star-inserted"]/ath-tabs/div/div/div[2]/div/div/nz-cascader/div/div
    点击    //span[contains(text(),'项目类型')]
    点击    //div[@class='delete-condition ng-star-inserted']
    Sleep    2
    点击    //div[@class='group-set-container ng-star-inserted']/ath-tabs/div/div/div[2]/div/div/nz-cascader/div/div
    点击    //span[contains(text(),'项目类型')]
    点击    //span[contains(text(),'确定')]
    should contain    //h5[contains(text(),'分组目录')]    分组目录
    should contain    //p[contains(text(),'项目中控台')]    项目中控台
    Sleep    3

按成员分组
    #升序
    js点击    //span[contains(text(),'分组')]
    点击    //span[contains(text(),'按成员分组')]
    点击    //span[contains(text(),'确定')]
    Sleep    5
    should contain    //h5[contains(text(),'分组目录')]    分组目录
    Sleep    3
    #降序
    js点击    //span[contains(text(),'分组')]
    点击    //span[contains(text(),'按成员分组')]/following::span[contains(text(),'降序')][1]
    点击    //span[contains(text(),'确定')]
    Sleep    3
    should contain    //h5[contains(text(),'分组目录')]    分组目录
    Sleep    3

团队任务高级分组
    js点击    //span[contains(text(),'分组')]
    点击    //*[contains(text(),'高级分组')]
    点击    //span[contains(text(),'重置')]
    Sleep    2
    点击    //div[@class='group-set-container ng-star-inserted']/ath-tabs/div/div/div[2]/div/div/nz-cascader/div/div
    点击    //li[@title="是否逾期"]
    点击    //div[@class='delete-condition ng-star-inserted']    #删除高级分组
    Sleep    2
    点击    //div[@class='group-set-container ng-star-inserted']/ath-tabs/div/div/div[2]/div/div/nz-cascader/div/div
    点击    //li[@title="是否逾期"]
    点击    //span[contains(text(),'确定')]
    Sleep    2
    should contain    //h5[contains(text(),'分组目录')]    分组目录
    should contain    //p[contains(text(),'逾期任务')]    逾期任务
    Sleep    3
    #最多设置三个分组条件
    js点击    //span[contains(text(),'分组')]
    点击    //*[contains(text(),'高级分组')]
    点击    //div[@class='group-set-container ng-star-inserted']/ath-tabs/div/div/div[2]/div/div[2]/nz-cascader/div/div
    点击    //span[contains(text(),'项目类型')]
    点击    //div[@class='group-set-container ng-star-inserted']/ath-tabs/div/div/div[2]/div/div[3]/nz-cascader/div/div
    点击    //span[@class='ng-star-inserted'][contains(text(),'成员')]
    点击    //span[contains(text(),'确定')]
    Sleep    2
    should contain    //h5[contains(text(),'分组目录')]    分组目录
    should contain    //p[contains(text(),'逾期任务')]    逾期任务
    Sleep    3
    js点击    //span[contains(text(),'分组')]
    点击    //*[contains(text(),'高级分组')]
    点击    //span[contains(text(),'重置')]
    Sleep    2
    点击    //span[contains(text(),'确定')]
    Sleep    2

按部门分组
    #升序
    js点击    //span[contains(text(),'分组')]
    点击    //span[contains(text(),'按部门分组')]
    点击    //span[contains(text(),'确定')]
    Sleep    5
    should contain    //h5[contains(text(),'分组目录')]    分组目录
    Sleep    3
    #降序
    js点击    //span[contains(text(),'分组')]
    点击    //span[contains(text(),'按部门分组')]/following::span[contains(text(),'降序')][1]
    点击    //span[contains(text(),'确定')]
    Sleep    3
    should contain    //h5[contains(text(),'分组目录')]    分组目录
    Sleep    3

团队任务排序
    #    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #    登录ATHENA平台    ${username}    ${password}
    团队任务排序功能

团队任务排序功能
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'团队任务')]
    js点击    //span[contains(text(),'排序')]
    点击    //span[contains(text(),'重置')]
    Sleep    5
    #条件拖动
    Drag And Drop By Offset    //div[@class="cdk-drop-list sort-item-list"]/div[1]/div[1]    0    100
    Sleep    5
    点击    //span[contains(text(),'确定')]
    Sleep    5
    Should Contain    //div[contains(text(),'3')]    3
    Sleep    2
    #删除条件
    js点击    //span[contains(text(),'排序')]
    点击    //div[@class='cdk-drop-list sort-item-list']/div[1]/*[name()='svg']
    点击    //span[contains(text(),'确定')]
    Sleep    5

团队任务筛选
    [Arguments]    ${tasktype}
    #    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #    登录ATHENA平台    ${username}    ${password}
    团队任务常规筛选
    团队任务高级筛选    ${tasktype}

团队任务常规筛选
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'团队任务')]
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'常规筛选')]
    Sleep    2
    点击    //div[@class="filter-list-container ng-star-inserted"]/app-todo-comm-filter/div[2]
    点击    //span[contains(text(),'未读')]
    Sleep    2
    点击    //*[contains(text(),'高级筛选')]
    点击    //*[contains(text(),'常规筛选')]
    点击    //span[contains(text(),'确定')]
    Sleep    5
    当前页面可见字符    New
    Sleep    5
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'常规筛选')]
    点击    //span[contains(text(),'重置')]
    点击    //span[contains(text(),'确定')]
    Sleep    5

团队任务高级筛选
    [Arguments]    ${tasktype}
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //div[@class='filter-item-row']/ath-select
    点击    //span[contains(text(),'任务类型')]
    输入    //app-todo-filter-item[@class='ng-star-inserted']//input    ${tasktype}
    点击    //span[@class="ant-checkbox"]
    点击    //*[contains(text(),'常规筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //span[contains(text(),'确定')]
    Sleep    5
    当前页面可见字符    签核
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //span[contains(text(),'重置')]
    Sleep    5
    点击    //span[contains(text(),'确定')]
    Sleep    2
    #删除高级筛选条件
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //div[@class='filter-item-row']/ath-select
    点击    //span[contains(text(),'任务类型')]
    输入    //app-todo-filter-item[@class='ng-star-inserted']//input    ${tasktype}
    点击    //span[@class="ant-checkbox"]
    点击    //*[contains(text(),'常规筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //div[@class="filter-list-container ng-star-inserted"]/app-todo-senior-filter/app-todo-senior-filter-item[1]/div/div[4]
    点击    //span[contains(text(),'确定')]
    Sleep    2

团队项目按成员分组
    #升序
    js点击    //span[contains(text(),'分组')]
    点击    //span[contains(text(),'按成员分组')]
    点击    //span[contains(text(),'确定')]
    Sleep    5
    should contain    //h5[contains(text(),'分组目录')]    分组目录
    Sleep    3
    #降序
    js点击    //span[contains(text(),'分组')]
    点击    //span[contains(text(),'按成员分组')]/following::span[contains(text(),'降序')][1]
    点击    //span[contains(text(),'确定')]
    Sleep    3
    should contain    //h5[contains(text(),'分组目录')]    分组目录
    Sleep    3

团队项目按部门分组
    #升序
    js点击    //span[contains(text(),'分组')]
    点击    //span[contains(text(),'按部门分组')]
    点击    //span[contains(text(),'确定')]
    Sleep    5
    should contain    //h5[contains(text(),'分组目录')]    分组目录
    Sleep    3
    #降序
    js点击    //span[contains(text(),'分组')]
    点击    //span[contains(text(),'按部门分组')]/following::span[contains(text(),'降序')][1]
    点击    //span[contains(text(),'确定')]
    Sleep    3
    should contain    //h5[contains(text(),'分组目录')]    分组目录
    Sleep    3

团队项目常规筛选
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'团队项目')]
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'常规筛选')]
    Sleep    2
    点击    //div[@class="filter-list-container ng-star-inserted"]/app-todo-comm-filter/div[2]
    点击    //span[contains(text(),'未读')]
    Sleep    2
    点击    //*[contains(text(),'高级筛选')]
    点击    //*[contains(text(),'常规筛选')]
    点击    //span[contains(text(),'确定')]
    Sleep    5
    当前页面可见字符    New
    Sleep    5
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'常规筛选')]
    点击    //span[contains(text(),'重置')]
    点击    //span[contains(text(),'确定')]
    Sleep    5

团队项目高级筛选
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //div[@class='filter-item-row']/ath-select
    点击    //span[contains(text(),'阅读状态')]
    输入    //app-todo-filter-item[@class='ng-star-inserted']//input    未读
    点击    //span[contains(text(),'未读')]
    点击    //*[contains(text(),'常规筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //span[contains(text(),'确定')]
    Sleep    5
    当前页面可见字符    New
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //span[contains(text(),'重置')]
    Sleep    5
    点击    //span[contains(text(),'确定')]
    Sleep    2
    #删除高级筛选条件
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //div[@class='filter-item-row']/ath-select
    点击    //span[contains(text(),'阅读状态')]
    输入    //app-todo-filter-item[@class='ng-star-inserted']//input    未读
    点击    //span[contains(text(),'未读')]
    点击    //*[contains(text(),'常规筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //div[@class='todo-common-tool-container ng-star-inserted']/ath-tabs/div/div/div[2]/div/app-todo-senior-filter/app-todo-senior-filter-item[1]/div/div[4]/i//*[name()='svg']
    Sleep    3
    点击    //span[contains(text(),'确定')]
    Sleep    3

团队项目排序
    #    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #    登录ATHENA平台    ${username}    ${password}
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'团队项目')]
    js点击    //span[contains(text(),'排序')]
    点击    //span[contains(text(),'重置')]
    Sleep    5
    #条件拖动
    Drag And Drop By Offset    //div[@class="cdk-drop-list sort-item-list"]/div[1]/div[1]    0    100
    Sleep    5
    点击    //span[contains(text(),'确定')]
    Sleep    5
    Should Contain    //div[contains(text(),'4')]    4
    Sleep    2
    #降序
    js点击    //span[contains(text(),'排序')]
    点击    //div[@class='cdk-drop-list sort-item-list']/div[1]/div[3]
    点击    //span[contains(text(),'确定')]
    Sleep    5
    #删除排序条件
    js点击    //span[contains(text(),'排序')]
    点击    //div[@class='cdk-drop-list sort-item-list']/div[1]/*[name()='svg']
    点击    //span[contains(text(),'确定')]
    Sleep    5

团队项目分组
    #    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #    登录ATHENA平台    ${username}    ${password}
    团队项目常规分组
    团队项目高级分组

团队项目筛选
    #    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    ProdHwAthenaAutoTestAi01    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestAi01
    #    登录ATHENA平台    ${username}    ${password}
    团队项目常规筛选
    团队项目高级筛选

团队项目常规分组
    跳转网页    /todo/task
    点击    //div[contains(text(),'团队项目')]
    js点击    //span[contains(text(),'分组')]
    点击    //*[contains(text(),'常规分组')]
    #不分组
    点击    //span[contains(text(),'不分组')]
    点击    //span[contains(text(),'确定')]
    Sleep    3
    js点击    //span[contains(text(),'分组')]
    按项目卡项目类型分组
    Comment    按创建时间分组分组
    团队项目按成员分组
    团队项目按部门分组

切换我的任务我的项目
    [Arguments]    ${key}
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'我的项目')]
    任务/项目名称搜索    ${key}
    当前页面可见    //*[contains(text(),'退货签收主键：${key}')]
    点击    //div[contains(text(),'我的任务')]
    任务/项目名称搜索    ${key}
    当前页面可见    //*[contains(text(),'退货签收主键：${key}')]

我的任务模糊搜索
    [Arguments]    ${key}    ${a}
    跳转网页    /todo/task
    点击    //div[contains(text(),'我的任务')]
    清空筛选
    按任务/项目类型筛选    多人或签（职能）   我的任务
    任务/项目名称搜索    ${key}
    当前页面可见字符    ${a}
    Sleep    5

我的项目模糊搜索
    [Arguments]    ${key}    ${plan}
    跳转网页    /todo/task
    点击    //div[contains(text(),'我的项目')]
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'常规筛选')]
    点击    //span[contains(text(),'重置')]
    点击    //span[contains(text(),'确定')]
    任务/项目名称搜索    ${key}
    当前页面可见字符    ${plan}

任务卡隐藏后显示
    [Arguments]    ${key}
    js点击    //span[contains(text(),'显示')]
    点击    //span[@class='ant-switch-handle']
    js点击    //span[contains(text(),'显示')]
    Comment    任务/项目名称搜索    1735264018
    当前页面可见字符    更正采购员信息
    点击    //span[contains(text(),'显示')]
    点击    //span[@class='ant-switch-handle']
    Comment    任务/项目名称搜索
    当前页面不可见字符    更正采购员信息

任务卡显示后隐藏
    [Arguments]    ${key}
    js点击    //span[contains(text(),'显示')]
    点击    //span[@class='ant-switch-handle']
    js点击    //span[contains(text(),'显示')]
    Comment    任务/项目名称搜索    1735264018
    当前页面不可见字符    更正采购员信息
    #隐藏
    js点击    //span[contains(text(),'显示')]
    点击    //span[@class='ant-switch-handle']
    js点击    //span[contains(text(),'显示')]
    Comment    任务/项目名称搜索（暂无数据）    1735264018
    当前页面可见字符    更正采购员信息
    #恢复默认不打开
    js点击    //span[contains(text(),'显示')]
    点击    //span[@class='ant-switch-handle']

团队任务展示任务卡
    [Arguments]    ${key}
    #团队任务
    跳转网页    /todo/team-tasks
    Sleep    5
    任务/项目名称搜索    ${key}
    Sleep    5
    当前页面包含元素    //span[contains(text(),' 【hl_test】多人或签（人员） ')]
    当前页面包含元素    //span[contains(text(),' 【黄蕾】多人或签（人员） ')]
    #团队项目
    跳转网页    /todo/team-projects
    Sleep    5
    任务/项目名称搜索    ${key}
    Sleep    5
    当前页面包含元素    //div[@class="card-item-name"]/span/span[contains(text(),'退货成功')]
    关闭浏览器

常规条件筛选
    [Arguments]    ${project}
    js点击    //span[contains(text(),'筛选')]
    点击    //span[contains(text(),'常规筛选')]
    输入    //span[contains(text(),'项目类型')]/preceding::input[1]    ${project}
    点击    //span[@class='font-highlight'][contains(text(),'${project}')]
    点击    //span[contains(text(),'确定')]
    任务/项目名称搜索    2024
    当前页面可见字符    2024
    Sleep    5

切换筛选类型
    [Arguments]    ${filterType}
    元素存在则点击    //div/ath-tab-normal-title/span[text()='${filterType}']
    当前页面可见    //div/ath-tab-normal-title/span[text()='${filterType}']
    
卡片视图切换
    #    跳转网页    /todo/task
    跳转到待办页    /todo/task
    视图切换    我的任务
    视图切换    我的任务
    视图切换    我的项目
    视图切换    我的项目
    视图切换    团队任务
    视图切换    团队任务
    视图切换    团队项目
    视图切换    团队项目

团队项目任务卡片展示
    [Arguments]    ${key}
    #团队任务
    Capture Page Screenshot
    跳转网页    /todo/team-tasks
    Capture Page Screenshot
    Sleep    5
    任务/项目名称搜索    ${key}
    Sleep    5
    当前页面包含元素    //span[contains(text(),'退货签收主键：${key}')]
    #团队项目
    跳转网页    /todo/team-projects
    Sleep    5
    任务/项目名称搜索    ${key}
    Sleep    5
    当前页面包含元素    //span[contains(text(),'退货签收主键：${key}')]

模糊搜索任务卡/项目卡
    [Arguments]    ${key}    ${date}    ${name}    ${pdate}    ${pname}
    切换我的任务我的项目    ${key}
    我的任务模糊搜索    ${date}    ${name}
    我的项目模糊搜索    ${pdate}    ${pname}

按任务/项目类型筛选
    [Arguments]    ${key}   ${filteTable}
    js点击    //div[contains(text(),'${filteTable}')]
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'常规筛选')]
    Sleep    2
    点击    //div[@class="filter-list-container ng-star-inserted"]/app-todo-comm-filter/div[1]
    输入    //div[@class="filter-list-container ng-star-inserted"]/app-todo-comm-filter/div[1]//input    ${key}
    点击    //span[@class="ant-checkbox"]
    Sleep    2
    点击    //*[contains(text(),'高级筛选')]
    点击    //*[contains(text(),'常规筛选')]
    点击    //span[contains(text(),'确定')]

# 按项目类型筛选
#     [Arguments]    ${key}
#     js点击    //div[contains(text(),'我的项目')]
#     js点击    //span[contains(text(),'筛选')]
#     点击    //span[contains(text(),'常规筛选')]
#     Sleep    2
#     点击    //div[@class="filter-list-container ng-star-inserted"]/app-todo-comm-filter/div[1]
#     输入    //div[@class="filter-list-container ng-star-inserted"]/app-todo-comm-filter/div[1]//input    ${key}
#     点击    //span[contains(text(),'全部')]
#     Sleep    2
#     点击    //span[contains(text(),'高级筛选')]
#     点击    //span[contains(text(),'常规筛选')]
#     点击    //span[contains(text(),'确定')]

按阅读状态筛选
    [Arguments]    ${key}
    js点击    //div[contains(text(),'我的任务')]
    js点击    //span[contains(text(),'筛选')]
    点击    //span[contains(text(),'常规筛选')]
    Sleep    2
    点击    //div[@class="filter-list-container ng-star-inserted"]/app-todo-comm-filter/div[2]
    点击    //span[contains(text(),'${key}')]
    Sleep    2
    点击    //span[contains(text(),'高级筛选')]
    点击    //span[contains(text(),'常规筛选')]
    点击    //span[contains(text(),'确定')]

按任务状态筛选
    [Arguments]    ${key}
    js点击    //div[contains(text(),'我的任务')]
    js点击    //span[contains(text(),'筛选')]
    点击    //span[contains(text(),'常规筛选')]
    Sleep    2
    点击    //div[@class="filter-list-container ng-star-inserted"]/app-todo-comm-filter/div[3]
    点击    //span[contains(text(),'${key}')]
    Sleep    2
    点击    //span[contains(text(),'高级筛选')]
    点击    //span[contains(text(),'常规筛选')]
    点击    //span[contains(text(),'确定')]

按任务来源筛选
    [Arguments]    ${key}
    js点击    //div[contains(text(),'我的任务')]
    js点击    //span[contains(text(),'筛选')]
    点击    //span[contains(text(),'常规筛选')]
    Sleep    2
    点击    //div[@class="filter-list-container ng-star-inserted"]/app-todo-comm-filter/div[4]
    点击    //span[contains(text(),'${key}')]
    Sleep    2
    点击    //span[contains(text(),'高级筛选')]
    点击    //span[contains(text(),'常规筛选')]
    点击    //span[contains(text(),'确定')]

按完成时间筛选
    [Arguments]    ${key}
    js点击    //div[contains(text(),'我的任务')]
    js点击    //span[contains(text(),'筛选')]
    点击    //span[contains(text(),'常规筛选')]
    Sleep    2
    点击    //div[@class="filter-list-container ng-star-inserted"]/app-todo-comm-filter/div[5]
    点击    //span[contains(text(),'${key}')]
    Sleep    2
    点击    //span[contains(text(),'高级筛选')]
    点击    //span[contains(text(),'常规筛选')]
    点击    //span[contains(text(),'确定')]

高级筛选循环
    [Arguments]    ${type}    ${key}    ${keywords}    ${task}    ${project}
    #任务卡循环高级筛选
    FOR    ${type}    ${key}    ${keywords}    IN
    ...    任务类型    行事历    HL的行事历
    ...    阅读状态    未读    未读
    ...    任务状态    当日需求完成    当日需求完成
    ...    任务来源    转派    转派
    任务高级筛选    ${type}    ${key}    ${keywords}
    END
    完成时间筛选（高级筛选）    完成时间    ${task}
    #项目卡循环高级筛选
    FOR    ${type}    ${key}    ${keywords}    IN
    ...    项目类型    退货成功    退货成功
    ...    阅读状态    未读    未读
    ...    项目状态    当日需求完成    当日需求完成
    ...    项目来源    转派    转派
    ...    任务进行状态    有任务进行中    有任务进行中
    ...    其它    其它    其它
    项目高级筛选    ${type}    ${key}    ${keywords}
    END
    完成时间筛选（高级筛选）    完成时间    ${project}

任务高级筛选
    [Arguments]    ${type}    ${key}    ${keywords}
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //div[@class='filter-item-row']/ath-select
    点击    //span[contains(text(),'${type}')]
    输入    //app-todo-filter-item[@class='ng-star-inserted']//input    ${key}
    点击    //span[@class='font-highlight']
    点击    //*[contains(text(),'常规筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //span[contains(text(),'确定')]
    Sleep    5
    任务/项目名称搜索    ${keywords}
    当前页面可见字符    ${keywords}
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //span[contains(text(),'重置')]
    Sleep    5
    点击    //span[contains(text(),'确定')]
    Sleep    2
    #删除高级筛选条件
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //div[@class='filter-item-row']/ath-select
    点击    //span[contains(text(),'${type}')]
    输入    //app-todo-filter-item[@class='ng-star-inserted']//input    ${key}
    点击    //span[@class='font-highlight']
    点击    //*[contains(text(),'常规筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //div[@class="filter-list-container ng-star-inserted"]/app-todo-senior-filter/app-todo-senior-filter-item[1]/div/div[4]
    点击    //span[contains(text(),'确定')]
    Sleep    2

项目高级筛选
    [Arguments]    ${type}    ${key}    ${keywords}
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //div[@class='filter-item-row']/ath-select
    点击    //span[contains(text(),'${type}')]
    输入    //app-todo-filter-item[@class='ng-star-inserted']//input    ${key}
    点击    //span[@class='font-highlight']
    点击    //*[contains(text(),'常规筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //span[contains(text(),'确定')]
    Sleep    5
    任务/项目名称搜索    ${keywords}
    当前页面可见字符    ${keywords}
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //span[contains(text(),'重置')]
    Sleep    5
    点击    //span[contains(text(),'确定')]
    Sleep    2
    #删除高级筛选条件
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //div[@class='filter-item-row']/ath-select
    点击    //span[contains(text(),'${type}')]
    输入    //app-todo-filter-item[@class='ng-star-inserted']//input    ${key}
    点击    //span[@class='font-highlight']
    点击    //*[contains(text(),'常规筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //div[@class="filter-list-container ng-star-inserted"]/app-todo-senior-filter/app-todo-senior-filter-item[1]/div/div[4]
    点击    //span[contains(text(),'确定')]
    Sleep    2

完成时间筛选（高级筛选）
    [Arguments]    ${type}    ${keywords}
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //div[@class='filter-item-row']/ath-select
    点击    //span[contains(text(),'${type}')]
    时间选择    1    30
    点击    //*[contains(text(),'常规筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //span[contains(text(),'确定')]
    Sleep    5
    任务/项目名称搜索    ${keywords}
    当前页面可见字符    ${keywords}
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //span[contains(text(),'重置')]
    Sleep    5
    点击    //span[contains(text(),'确定')]
    Sleep    2
    #删除高级筛选条件
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //div[@class="filter-list-container ng-star-inserted"]/app-todo-senior-filter/app-todo-senior-filter-item[1]/div/div[4]
    点击    //span[contains(text(),'确定')]
    Sleep    2

时间选择
    [Arguments]    ${StarTime}    ${EndTime}
    Sleep    10
    鼠标悬停    //ath-range-picker[@nzformat='yyyy/MM/dd']
    点击    //ath-range-picker[@nzformat='yyyy/MM/dd']
    Sleep    10
    Comment    当前页面可见    //div[@class='ant-picker-panels']
    点击    //td[@role='gridcell']/div[contains(text(),'${starttime}')]
    Sleep    3
    点击    //td[@role='gridcell']/div[contains(text(),'${endtime}')]
    # \ executejavascript    window.document.getElementsByClassName('ng-star-inserted')[0].value='${starttime}'
    # \ Sleep \ 10
    # \ executejavascript    window.document.getElementsByClassName('ng-star-inserted')[1].value='${endtime}'
    Comment    当前页面可见    //div[text()='2024/10']

待办取消分组
    [Arguments]    ${groupTable}
    点击      //div[contains(text(),'${groupTable}')]
    js点击    //span[contains(text(),'分组')]
    点击    //*[contains(text(),'常规分组')]
    元素存在则点击     //span[contains(text(),'不分组')]
    点击    //span[contains(text(),'确定')]

按任务类型筛选
    [Arguments]    ${key}
    js点击    //div[contains(text(),'我的任务')]
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'常规筛选')]
    Sleep    2
    点击    //div[@class="filter-list-container ng-star-inserted"]/app-todo-comm-filter/div[1]
    输入    //div[@class="filter-list-container ng-star-inserted"]/app-todo-comm-filter/div[1]//input    ${key}
    点击    //span[@class="ant-checkbox"]
    Sleep    2
    点击    //*[contains(text(),'高级筛选')]
    点击    //*[contains(text(),'常规筛选')]
    点击    //span[contains(text(),'确定')]

#我的任务/团队任务/我的项目/团队项目筛选重置
重置待办筛选
    [Arguments]    ${tabName}
    js点击    //div[contains(text(),'${tabName}')]
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'常规筛选')]
    当前页面可见  //button[@class='ath-btn ant-btn ant-btn-default']/span[text()='重置']
    点击  //button[@class='ath-btn ant-btn ant-btn-default']/span[text()='重置']
    元素存在则点击  //button[@class='ath-btn ant-btn ant-btn-primary']
    当前页面不可见元素  //app-todo-filter/app-todo-common-tool/span/div

人员上线
    点击(元素不一定可见)    //input[@type='radio']
    点击    //span[contains(text(),'人员上线')]

进入指定任务卡
    点击    //li[contains(text(),'首页')]
    点击    //div[contains(text(),'待办')]
    sleep    5
    点击    //div[contains(text(),'我的任务')]
    #重置筛选
    点击    //span[contains(text(),'筛选')]
    点击    //span[contains(text(),'重置')]
    点击    //span[contains(text(),'确定')]
    点击    (//span[contains(text(),'搜索')])[2]
#    点击    //*[@id="main-content"]/app-todo/app-todo-nav/nav/div[2]/app-todo-search/div/ath-search/div/div/div/ath-input-group
    输入    //*[@id="main-content"]/app-todo/app-todo-nav/nav/div[2]/app-todo-search/div/ath-search/div/div/div/ath-input-group/div[2]/section/span/input    1752455671
    点击    //button[@class='ath-search-single-input-icon-btn ant-btn ant-btn-primary ant-input-search-button ant-btn-icon-only ng-star-inserted']
#    进入任务卡
    点击    //span[contains(text(),'申请采购商品')]

打开表格设定开窗（任务卡）
    点击    //*[@id="dy-athena-table"]/ath-table/div[1]/div[3]/div/column-setting/span

进入指定项目卡
    点击    //li[contains(text(),'首页')]
    点击    //div[contains(text(),'待办')]
    sleep    5
    点击    //div[contains(text(),'我的项目')]
    #重置筛选
    点击    //span[contains(text(),'筛选')]
    点击    //span[contains(text(),'重置')]
    点击    //span[contains(text(),'确定')]
    点击    //*[@id="main-content"]/app-todo/app-todo-nav/nav/div[2]/app-todo-search/div/ath-search/div/div/span
#    点击    //*[@id="main-content"]/app-todo/app-todo-nav/nav/div[2]/app-todo-search/div/ath-search/div/div/div
    输入    //*[@id="main-content"]/app-todo/app-todo-nav/nav/div[2]/app-todo-search/div/ath-search/div/div/div/ath-input-group/div[2]/section/span/input    1752444921593
    点击    //*[@id="main-content"]/app-todo/app-todo-nav/nav/div[2]/app-todo-search/div/ath-search/div/div/div/ath-input-group/div[2]/section/span/span/button
#    进入项目卡
    点击    //span[contains(text(),'采购员信息审查')]

打开表格设定开窗（项目卡）
    点击    //*[@id="task-content-content-id"]/app-task-rendering/div[1]/div/dynamic-project-table/div/ath-table/div[1]/div[3]/div/column-setting

进入指定收藏任务卡
    点击    //li[contains(text(),'首页')]
    点击    //p[contains(text(),'收藏')]
    sleep    5
    输入    //*[@id="main-content"]/app-snapshot/div/div[1]/div[1]/ath-input-group/div[2]/section/input    20250714自动化测试用
    点击    //*[@id="main-content"]/app-snapshot/div/div[2]/div[2]/div/div[2]/div[2]/p
#    进入任务卡
    点击    //*[@id="main-content"]/app-snapshot/div/app-snapshot-detail/div/div[1]/div[2]/div[2]

打开表格设定开窗（收藏任务卡）
    点击    //*[@id="dy-athena-table"]/ath-table/div[1]/div[3]/div/column-setting/span

进入指定消息任务卡
    点击    //*[@id="main-header"]/app-main-menu/ath-tab-menus/div/ul/li
    点击    //*[@id="main-header"]/angular-mf-adapter[2]/app-main-toolbar/div/div[6]/div/ath-badge/nz-badge/div/i
    sleep    5
    点击    //*[@id="main-content"]/app-message-center/div[1]/div[2]/ath-search/div/div/span
    输入    //*[@id="main-content"]/app-message-center/div[1]/div[2]/ath-search/div/div/div/ath-input-group/div[2]/section/span/input    手动任务-2-1752460915
    点击    //*[@id="main-content"]/app-message-center/div[1]/div[2]/ath-search/div/div/div/ath-input-group/div[2]/section/span/span/button
#    进入任务卡
    点击    //*[@id="main-content"]/app-message-center/div[2]/remind-panel/div/div/div/div/div[2]/div/div/span

打开表格设定开窗（消息任务卡）
    点击    //*[@id="dy-athena-table"]/ath-table/div[1]/div[3]/div/column-setting/span



#    打开任务详情列表--罗丹
打开任务详情列表
    点击    //div[contains(text(),'我的任务')]
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'常规筛选')]
    点击    //span[contains(text(),'重置')]
    点击    //span[contains(text(),'确定')]
    点击   (//div[@class='todo-card-item-top-container'])[1]
    sleep  2
    点击   (//div[@class='flodIcon ng-star-inserted'])
    sleep  2

切换任务与任务详情
    点击   //*[@id="main-header"]/app-main-menu/ath-tab-menus/div/div[1]/ul/li[1]
    sleep  2
    点击  //*[@id="main-header"]/app-main-menu/ath-tab-menus/div/div[1]/ul/li[2]
    sleep  2