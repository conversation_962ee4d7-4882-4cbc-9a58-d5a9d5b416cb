*** Settings ***
Documentation     陈金明
Suite Setup       Run Keywords    环境设定
Suite Teardown    Run Keywords    关闭浏览器
Resource          ../关键字/业务关键字/邮件管理.robot
Resource          ../关键字/业务关键字/Athena平台.robot

*** Test Cases ***
邮件管理
    [Documentation]    陈金明
    ...    目前只在生产执行
    ${username}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    pur003    '${ENV}'=='microsoft.prod'    ppm06
    ${password}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    pur003    '${ENV}'=='microsoft.prod'    ppm06ppm06
    ${unique}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    设变导航（进阶版）    '${ENV}'=='microsoft.prod'    ATHENA-DCP_设变导航
    ${tanent}    Set Variable If    '${ENV}'=='huawei.test'    TestAthenaAutoTestAi001    '${ENV}'=='huawei.prod'    Athena演示租户    '${ENV}'=='microsoft.prod'    WF測試租戶
    Set Global Variable    ${email}    ${唯一标识}
    登录Athena平台    ${username}    ${password}    tenant=${tanent}
    点击顶部菜单    全部
    点击右侧菜单    邮件管理
    Sleep    12
    #${unique}作为表格中定位的唯一参照物,必须保证在表格中唯一
    启用/停用    ${unique}
    查看    ${unique}
    编辑    ${unique}
    删除    ${unique}
