*** Settings ***
Library           SeleniumLibrary
Resource          ../系统关键字/web.robot
Resource          ../../元素/Athena平台元素.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../系统关键字/接口.robot

*** Keywords ***
打开表格设定开窗
    [Arguments]    ${workname}
    当前页面可见  //span[@class='ag-icon ag-icon-setting']
    鼠标悬停   //span[@class='ag-icon ag-icon-setting']
    Sleep   3
    Comment   当前页面可见   //div[@class='ant-tooltip-inner ng-tns-c94-108']
    点击   //div[contains(text(),'${workname}')]
    Sleep   3
    点击   //column-setting[@class='column-setting toolbar-icon ng-star-inserted']
    Sleep   3
    当前页面可见    //span[contains(text(),'表格设定')]


关闭表格设定开窗
    当前页面可见    //span[contains(text(),'表格设定')]
    点击  //span[@class='ag-icon ag-icon-cross']

重置表格设定
    当前页面可见   //span[text()='重置']
    点击   //span[text()='重置']
    Sleep   3
    当前页面可见  //div[text()='请确认是否要重置表格设置？重置操作将可能触发表格页面刷新。']
    点击  //span[contains(text(),'确定')]
显示/隐藏表格字段
    [Arguments]    ${tablekey}
    鼠标悬停    //span/span[text()='${tablekey}']
    点击   //span/span[text()='${tablekey}']
    Comment   当前页面可见  //button[@nztype='primary']
    点击  //button[@nztype='primary']
    当前页面不可见元素    //span[@title='${tablekey}']
    打开表格设定开窗    表格_高级查询作业测试
    重置表格设定
    Sleep   10
    当前页面可见    //span[@title='${tablekey}']

表格字段冻结
    [Arguments]    ${tablekey}
    鼠标悬停    //span/span[text()='${tablekey}']
    Sleep   3
    当前页面可见   //*[@id="viewportList-0"]/div[2]/span[@class='ag-icon ag-icon-lock ng-star-inserted']
    点击  //*[@id="viewportList-0"]/div[2]/span[@class='ag-icon ag-icon-lock ng-star-inserted']
    当前页面可见    //*[@id="viewportList-0"]/div[2]/span[@class='ag-icon ag-icon-lock ng-star-inserted pinned pinned-boundary']
    Comment   当前页面可见  //button[@nztype='primary']
    点击  //button[@nztype='primary']
    Sleep   3
    当前页面可见  //div[@class='ag-body-horizontal-scroll-container']
    打开表格设定开窗    表格_高级查询作业测试
    重置表格设定
    Sleep   10
    当前页面可见  //div[@class='ag-body-horizontal-scroll-viewport']
    
# 调整表格字段顺序
#     Drag And Drop By Offset    //*[@id="viewportList-0"]/div[2]/span[2]    0    200
#     Sleep   3
#     Comment   当前页面可见  //button[@nztype='primary']
#     点击  //button[@nztype='primary']
#     打开表格设定开窗    表格_高级查询作业测试
    #重置表格设定

调整表格字段顺序
    [Arguments]    ${tablekey}      ${targetkey}    ${workname}
    Comment  鼠标滑动到底部  //span/span[text()='${tablekey}']
    Sleep   3
    鼠标悬停  //span/span[text()='${tablekey}']
    Mouse Down  //span/span[text()='${tablekey}']
    Drag And Drop   //*[contains(@class,'cdk-drop-list')]/div/label/span/span[text()='${tablekey}']/following::span[@class='row-drag-operation ag-icon ag-icon-row-drag'][1]    //*[contains(@class,'cdk-drop-list')]/div/label/span/span[text()='${targetkey}']/following::span[@class='row-drag-operation ag-icon ag-icon-row-drag'][1]
    Sleep   3
    元素存在则点击  //div/button[@class='ath-btn ant-btn ant-btn-primary']
    # 打开表格设定开窗    ${workname}
    # 当前页面可见  //*[@id="viewportList-0"]/div/label/span/span[text()='${tablekey}']/following::span[@class='row-drag-operation ag-icon ag-icon-row-drag'][1]  3


行高设定
    [Arguments]    ${setitem}
    js点击     //div/table-layout-height-setting/span/i[@class='anticon arrow-down']
    当前页面可见  //div[@class='itemChecked-height itemDrop-active']    3
    元素存在则点击  //div[@class='itemChecked-height']/span[text()='${setitem}']
    #当前页面可见  //*[@class="cdk-overlay-pane"]/div/ul/li/ath-tooltip-wrapper/div/div[@class='itemChecked-height itemDrop-active']/span[text()='${setitem}']  3
    js点击     //div/table-layout-height-setting/span/i[@class='anticon arrow-down']
    Sleep   3
    #刷新作业
    元素存在则点击   //app-base-data-tab-title/div/div/div[@class='action-icon']/i
    Sleep   5
    点击   //app-base-data-tab-title/div/div[contains(text(),' 表格_高级查询作业测试 ')]
    Sleep   5
    js点击     //div/table-layout-height-setting/span/i[@class='anticon arrow-down']
    当前页面可见  //*[@class="cdk-overlay-pane"]/div/ul/li/ath-tooltip-wrapper/div/div[@class='itemChecked-height itemDrop-active']/span[text()='${setitem}']
    #收起行高设定icon
    js点击   //div/table-layout-height-setting/span/i[@class='anticon arrow-down']
    Sleep   3

列宽设定
    当前页面可见  //ath-table-layout-setting-tool/span/i[@class='anticon arrow-down']
    鼠标悬停  //ath-table-layout-setting-tool/span/i[@class='anticon arrow-down']
    点击  //ath-table-layout-setting-tool/span/i[@class='anticon arrow-down']
    当前页面可见  //li[@class='ant-dropdown-menu-item'][text()='自适应']
    鼠标悬停  //li[@class='ant-dropdown-menu-item'][text()='自适应']
    点击  //li[@class='ant-dropdown-menu-item'][text()='自适应']
    当前页面可见   //div[@data-colid='test_sign_case_id']/div/span

打开高级查询开窗
    ${query_element_exists}    判断元素是否可见    xpath=//advanced-query/span/span[@class='expand-icon expand-icon-down ant-dropdown-trigger']
    Run Keyword If    ${query_element_exists}   选择常用条件    常用条件1
    Run Keyword If    ${query_element_exists}   删除常用条件    常用条件1
    当元素不可见则刷新页面  1   //span[@class='advanced-query-icon ng-star-inserted']
    当前页面可见   //span[@class='advanced-query-icon ng-star-inserted']
    鼠标悬停  //span[@class='advanced-query-icon ng-star-inserted']
    当前页面可见    //div[contains(@class,'ant-tooltip-inner')]
    点击  //div[@class='title'][contains(text(),'表格_后端筛选')]
    Sleep   3
    js点击  //span[@class='advanced-query-icon ng-star-inserted']
    当前页面可见  //div[@class='query-header-tab']/div[1]

选择常用条件
    [Arguments]    ${conditionname}
    当前页面可见   //advanced-query/span/span[@class='expand-icon expand-icon-down ant-dropdown-trigger']
    点击  //advanced-query/span/span[@class='expand-icon expand-icon-down ant-dropdown-trigger']
    当前页面可见  //*[@class='cdk-overlay-pane']/div/ul/li/ath-tooltip-wrapper/div/div[@class='itemChecked']
    元素存在则点击  //*[@class='cdk-overlay-pane']/div/ul/li/ath-tooltip-wrapper/div/div[text()='${conditionname}']
    当前页面可见  //advanced-query/span/span[@class='expand-icon query-icon-com']/span

快速查询/数值型
    [Arguments]    ${datamin}   ${datamax}  
    当前页面可见  //div[@class='query-header-tab']/div[1]
    #数字输入框最值
    点击  //ath-quick-query/div/ul/li[1]/div/div/div/ath-input-number[1]
    输入  //ath-quick-query/div/ul/li[1]/div/div/div/ath-input-number[1]//input[@placeholder='最小值']  ${datamin}
    Sleep   3
    点击  //ath-quick-query/div/ul/li[1]/div/div/div/ath-input-number[2]
    输入  //ath-quick-query/div/ul/li[1]/div/div/div/ath-input-number[2]//input[@placeholder='最大值']  ${datamax}
    
快速查询/枚举型
    [Arguments]    ${option}
    当前页面可见  //div[@class='query-header-tab']/div[1]
    #选择框
    元素存在则点击  //div[@class='enum-query']
    当前页面可见  //div[@class='cdk-overlay-pane ag-custom-component-popup']
    元素存在则点击  //span[@class='option-item-label'][text()='${option}']
    #关闭蒙层
    Execute Javascript    document.querySelector('.ag-custom-component-popup.cdk-overlay-backdrop-showing').style.display = 'none'
    Sleep   3
    点击  //div[@class='enum-query']
    当前页面可见  //ath-select-item[@title='${option}']
    提交查询
    #断言查询结果
    当前页面可见  //span[@class='query-number ng-star-inserted']
    当前页面可见  //span[@title='${option}']

快速查询/字符型
    [Arguments]    ${querycontent}
    元素存在则点击  //ath-quick-query/div/ul/li[3]/div/div
    输入  //ath-quick-query/div/ul/li[3]/div/div/input  ${querycontent}
    提交查询
    断言高级查询结果  ${querycontent}

快速查询/日期型
    [Arguments]    ${starttime}     ${endtime}
    元素存在则点击  //div[@class='query-content-time']
    当前页面可见    //div[@class='ant-picker-panels']
    点击  //input[@placeholder='开始日期']
    Sleep  3
    输入   //input[@placeholder='开始日期']    ${starttime}
    点击   //input[@placeholder='结束日期']
    输入   //input[@placeholder='结束日期']    ${endtime}
    Press Keys    //input[@placeholder='结束日期']    RETURN
    提交查询
    #断言日期查询结果
    当前页面可见  //span[@class='query-number ng-star-inserted']
    Should Contain  //span[@title='${starttime}']  ${starttime}

展开/收起查询条件
    元素存在则点击  //span[@class='ant-divider-inner-text ng-star-inserted']
    当前页面可见  //i[@class='anticon select-arrow-down ng-star-inserted isOpen']

断言查询结果
    [Arguments]    ${datamin} 
    当前页面可见  //span[@class='query-number ng-star-inserted']
    Should Contain  //div[@class='ath-control-outer'][contains(text(),'11')]  ${datamin}

提交查询
    当前页面可见   //button[@class='ath-btn ant-btn ant-btn-primary ng-star-inserted']
    点击  //button[@class='ath-btn ant-btn ant-btn-primary ng-star-inserted']

重置查询结果
    打开高级查询开窗
    元素存在则点击  //button[@class='ath-btn ant-btn ant-btn-default'][2]
    Sleep   3
    web.当前页面不可见元素  //span[@class='query-number ng-star-inserted']

组合排序添加排序条件
    [Arguments]    ${sorttype}    ${sortkey}   ${sortitem}
    #选择排序字段 组合排序：query-sort-box 多条件排序：modal-content-box composite-sort-box
    鼠标悬停  //div[@class='${sorttype}']/div/div/ath-select[1]
    #当前页面不存在元素  //div/ath-select[1]/nz-select-clear/i
    点击   //div[@class='${sorttype}']/div/div/ath-select[1]
    当前页面可见  //ath-option-container
    点击  //div/ath-tooltip-wrapper/div/span[text()='${sortkey}']
    #选择排序顺序
    鼠标悬停  //div[@class='${sorttype}']/div/div/ath-select[2]
    #当前页面不存在元素  //div/ath-select[1]/nz-select-clear/i
    点击  //div[@class='${sorttype}']/div/div/ath-select[2]
    当前页面可见  //ath-option-container
    点击  //div/ath-tooltip-wrapper/div/span[text()='${sortitem}']

断言多条件排序结果
    [Arguments]    ${sortkey}
    当前页面可见  //span[@class='sort-number ng-star-inserted']
    js点击    //span[@class='sort-number ng-star-inserted']
    当前页面可见  //ath-select-item[@title='${sortkey}']
    元素存在则点击  //span[@class='ag-icon ag-icon-cross']

点击添加条件
    元素存在则点击  //div[@class='add-condition ng-star-inserted']
    当前页面可见  //div[@class='cdk-drop-list drop-list']

删除排序条件
    元素存在则点击  //span[@class='ag-icon ag-icon-delete']

多条件排序提交
    当前页面可见   //button[@class='ath-btn ant-btn ant-btn-primary']
    点击  //button[@class='ath-btn ant-btn ant-btn-primary']

高级查询切换查询页签
    [Arguments]    ${querytitle}
    元素存在则点击  //div[@class='header-title']/span[text()='${querytitle}']
    当前页面可见  //div[@class='actived-tab']/div/span[text()='${querytitle}']

高级查询添加查询条件
     [Arguments]    ${querykey}   ${condition}    ${querycontent}
     #查询字段
     鼠标悬停  //div[@class='container-field']
     点击  //div[@class='container-field']
     当前页面可见  //div[@class='athena-option-container']
     元素存在则点击  //ath-tooltip-wrapper/div/span[text()='${querykey}']
     当前页面可见  //ath-select-item[@title='${querykey}']
     #判断条件
     鼠标悬停  //div[@class='container-formula']
     点击  //div[@class='container-formula']
     当前页面可见  //div[@class='athena-option-container']
     元素存在则点击  //span[@class='option-item-label'][text()='${condition}']
     当前页面可见  //ath-select-item[@title='${condition}']
     #查询内容
     鼠标悬停  //div[@class='container-field-value']
     点击  //div[@class='container-field-value']
     Sleep  3
     输入  //div[@class='container-field-value']/ath-input-group/div/section/input  ${querycontent}
     鼠标悬停  //div[@class='container-field-value']
     当前页面可见  //i[@class='anticon ant-input-clear-icon ng-star-inserted']
     #提交查询

断言高级查询结果
    [Arguments]    ${querycontent}
     当前页面可见  //app-dynamic-input-display[@title='${querycontent}']

打开多条件排序开窗
    当前页面可见  //composite-sort/span
    点击  //composite-sort/span
    当前页面可见  //div[@class='ant-modal-title']

恢复默认排序
    元素存在则点击  //div[@class='modal-footer composite-sort-footer ng-star-inserted']/button/span[contains(text(),'恢复默认排序')]
    Sleep   3
    当前页面不可见元素  //*[@id="dy-athena-table"]/ath-table/div[1]/composite-sort/span/span

断言组合排序结果
    当前页面可见  //span[@class='query-number ng-star-inserted']
    当前页面可见  //span[@class='ag-header-icon ag-header-label-icon ag-sort-icon ng-star-inserted active']

保存为常用条件
    [Arguments]    ${conditionname}
    当前页面可见  //button[@class='ath-btn ant-btn ant-btn-default ng-star-inserted']
    点击  //button[@class='ath-btn ant-btn ant-btn-default ng-star-inserted']
    当前页面可见  //div[@class='modal-content-common']
    点击  //div[@class='ath-input-content-box ng-star-inserted']
    输入  //div[@class='ath-input-group-cocoon ath-input-inner-label']/section/div/textarea  ${conditionname}
    元素存在则点击  //button[@class='ath-btn ant-btn confirm ant-btn-primary']
    #断言保存常用条件结果
    当前页面可见  //span/span[@class='expand-icon expand-icon-down ant-dropdown-trigger']
    点击  //span/span[@class='expand-icon expand-icon-down ant-dropdown-trigger']
    当前页面可见  //div[@class='itemChecked itemDrop-active'][text()='${conditionname}']
    点击  //span/span[@class='expand-icon expand-icon-down ant-dropdown-trigger']

删除常用条件
    [Arguments]    ${conditionname}
    元素存在则点击  //advanced-query/span/span[@class='expand-icon query-icon-com']/i
    当前页面可见  //span[text()='${conditionname}']
    #清除蒙层
    #Execute Javascript    document.querySelector('.ath-table-query-model .modal-content .advanced-query-list').style.overflow-y = 'none'
    Sleep   3
    鼠标悬停  //div[@class='advanced-query-content']/div
    当前页面可见  //ath-query-common/div/div/i[@class='anticon ant-input-clear-icon']
    #鼠标悬停  //ath-query-common/div/div/i[@class='anticon ant-input-clear-icon']
    点击  //ath-query-common/div/div/i[@class='anticon ant-input-clear-icon']
    #删除提示
    当前页面可见  //div[text()='删除【常用查询】后将无法恢复，是否确认删除?']
    元素存在则点击  //button[@class='ant-btn ant-btn-primary ng-star-inserted']
    Sleep   3
    当前页面不可见元素  //div[@class='item-detail']
    元素存在则点击  //button[@class='ath-btn ant-btn ant-btn-default'][2]
    当前页面不可见元素  //span/span[@class='expand-icon expand-icon-down ant-dropdown-trigger']





   



