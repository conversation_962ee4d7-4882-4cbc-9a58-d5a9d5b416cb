*** Settings ***
Resource          ../系统关键字/web.robot
Resource          ../业务关键字/Athena平台.robot

*** Keywords ***
区间选择
    [Arguments]    ${arg}
    点击    //span[text()='${arg}']

项目名称选择
    [Arguments]    ${projectName}
    输入    //*[text()='项目名称']/following::input[1]    ${projectName}
    点击    //span[contains(text(),'${projectName}')]
    Sleep    2
    Click Element    //div[@class="cdk-overlay-backdrop nz-overlay-transparent-backdrop cdk-overlay-backdrop-showing"]    #处理透明蒙层,收起下拉框

当责者选择
    [Arguments]    ${owner}
    点击    //*[text()='当责者']/following::input[1]
    点击    //span[@class='option-item-label'][contains(text(),'${owner}')]
    Click Element    //div[@class="cdk-overlay-backdrop nz-overlay-transparent-backdrop cdk-overlay-backdrop-showing"]    #处理透明蒙层,收起下拉框

搜索
    点击    //span[@class='ng-star-inserted'][contains(text(),'搜索')]
    Sleep    10

进度查询数据校验
    [Arguments]    ${goal}    ${interval}    ${projectName}    ${owner}
    #简单检查
    Page Should Contain    ${goal}
    Page Should Contain    ${interval}
    Page Should Contain    ${projectName}
    Page Should Contain    ${owner}

重置
    点击    //span[contains(text(),'重置')]

按输入历史项目和任务查询条件查询
    [Arguments]    ${project_no}=null    ${project_name}=null    ${scheduled_start_time}=null    ${scheduled_end_time}=null    ${card_type}=null    ${status}=null    ${create_time}=null    ${end_time}=null    ${project_source}=null    ${project_operation}=null    ${project_cycle_start}=null    ${project_cycle_end}=null
    刷新页面
    点击    //span[contains(text(),'历史项目/任务')]
    Log    ${card_type}
    #重置排序
    点击    //span[contains(text(),'排序')]
    点击    //span[contains(text(),'重置')]
    筛选    scheduled_start_time=${scheduled_start_time}    scheduled_end_time=${scheduled_end_time}    card_type=${card_type}    status=${status}    create_time=${create_time}    end_time=${end_time}    project_source=${project_source}    project_operation=${project_operation}    project_cycle_start=${project_cycle_start}    project_cycle_end=${project_cycle_end}
    js点击    //div[@class='ath-search-icon-with-text ng-star-inserted']//span[contains(text(),'搜索')]
    Run Keyword If    '${project_no}'!=' '    输入    //input[@placeholder='请输入关键字搜索']    ${project_no}
    Sleep    3
    js点击    //button[@class='ath-search-single-input-icon-btn ant-btn ant-btn-primary ant-input-search-button ant-btn-icon-only ng-star-inserted']
    Sleep    3
    当前页面可见字符    ${project_name}

日期输入
    [Arguments]    ${startTime}    ${endTime}
    #    点击    //span[contains(text(),'预计时间')][contains(@class,'ath-date-picker-inner-label')]
    ${startTimeLoc}    按顺序获取元素    //input[@placeholder="yyyyMMdd"]    0
    输入    ${startTimeLoc}    ${startTime}
    Sleep    1
    Press Keys    ${startTimeLoc}    RETURN
    Sleep    1
    ${endTimeLoc}    按顺序获取元素    //input[@placeholder="yyyyMMdd"]    1
    输入    ${endTimeLoc}    ${endTime}
    Sleep    1
    Press Keys    ${startTimeLoc}    RETURN
    Sleep    1

卡片类型选择
    [Arguments]    ${type}
    点击    //*[contains(text(),'全部卡片类型')]
    点击    //span[text()='${type}']
    Sleep    2

状态选择
    [Arguments]    ${status}
    点击    //*[contains(text(),'全部状态')]
    点击    //span[@class="option-item-label" and text()='${status}']

历史项目/任务查询数据校验
    [Arguments]    ${card_ype}    ${project_no}    ${project_name}    ${affiliated_project}    ${status}
    #简单检查
    当前页面可见字符    ${card_ype}
    当前页面可见字符    ${project_no}
    当前页面可见字符    ${project_name}
    当前页面可见字符    ${affiliated_project}
    当前页面可见字符    ${status}
#    当前页面可见字符    ${planTime}
    #    当前页面可见字符    ${relTime}
    #    当前页面可见字符    ${info}

排序
    [Arguments]    ${option1}    ${option2}    ${order}
    点击    //span[contains(text(),'排序')]
    点击    //span[contains(@class,'ant-cascader-picker-label')]
    Run Keyword If    '${option1}'=='卡片类型'    点击    //span[@class='ng-star-inserted'][contains(text(),'卡片类型')]
    Run Keyword If    '${order}'=='升序'    点击    //*[text()='升序']    ELSE    点击    //*[text()='降序']
    点击    xpath=(//span[contains(@class,'ant-cascader-picker-label')])[2]
    Run Keyword If    '${option2}'=='完成时间'    点击    //span[@class='ng-star-inserted'][contains(text(),'完成时间')]
    Run Keyword If    '${order}'=='升序'    点击    xpath=(//*[text()='升序'])[2]    ELSE    点击    xpath=(//*[text()='降序'])[2]
    点击    //span[contains(text(),'确定')]
    Sleep    2
    ${value1}    获取元素字符    //div[@class='ag-center-cols-container']/div[1]/div[6]
    ${value2}    获取元素字符    //div[@class='ag-center-cols-container']/div[2]/div[6]
    ${value1}    Convert Date    ${value1}    result_format=epoch
    ${value2}    Convert Date    ${value2}    result_format=epoch
    ${is_big_than}    Evaluate    ${value1} > ${value2}
    # 记录判断结果
    # 断言判断结果是否符合预期
    Should Be True    ${is_big_than}
    
    
    
表格设定-隐藏列
    [Arguments]    ${col}
    点击    //span[@class='ag-icon ag-icon-setting']
    Unselect Checkbox    //span//span[contains(text(),'${col}')]/ancestor::label//input
    点击    	//button[@class='ath-btn ant-btn ant-btn-primary']
    当前页面不可见字符    ${col}


表格设定-显示列
    [Arguments]    ${col}
    点击    //span[@class='ag-icon ag-icon-setting']
    select Checkbox    //span//span[contains(text(),'${col}')]/ancestor::label//input
    点击    	//button[@class='ath-btn ant-btn ant-btn-primary']
    当前页面可见字符    ${col}

数据导出
    点击    //span[contains(text(),'数据导出')]
    点击    //span[contains(text(),'确定')]
    当前页面可见字符    文档生成完成
    Sleep    5
    跳转网页    /import-and-export?code=export
    ${create_time}    获取元素字符    //div[@class='ag-center-cols-container']/div[1]/div[@col-id='createTime']
    Log    ${create_time}
    ${current_epoch}=    Get Current Date    result_format=epoch
    # 注意日期分隔符改为
    ${target_epoch}=    Convert Date    ${create_time}    date_format=%Y/%m/%d %H:%M:%S    result_format=epoch
    Log    ${target_epoch}
    ${time_diff}=    Evaluate    abs(${target_epoch} - ${current_epoch})
    #如果是30s内生成的判断为本次自动化生成的导出记录
    Should Be True    ${time_diff} <= 30    msg=非本次自动化产生的导出记录


进度查询
    [Arguments]    ${interval}    ${projectName}    ${owner}
    重置
    区间选择    ${interval}
    项目名称选择    ${projectName}
    当责者选择    ${owner}
    搜索

项目数据跳转校验
    [Arguments]    ${keywords}
    #热库
    刷新页面
    #筛选重置
    js点击    //*[contains(text(),'筛选')]
    js点击    //*[contains(text(),'重置')]
    Sleep    3
    Comment    #筛选项目卡
    Comment    js点击    //*[contains(text(),'筛选')]
    Comment    输入    //app-todo-comm-filter[@class="history-filter ng-star-inserted"]/div[2]//input    任务
    Comment    点击    //span[@class='ant-checkbox']//input[@type='checkbox']
    Comment    点击    //*[contains(text(),'确定')]
    #搜索关键字
    点击    //span[contains(text(),'搜索')]
    Run Keyword If    '${keywords}'!=' '    输入    //input[@placeholder='请输入关键字搜索']    ${keywords}
    点击    //button[@class='ath-search-single-input-icon-btn ant-btn ant-btn-primary ant-input-search-button ant-btn-icon-only ng-star-inserted']
    Sleep    3
    点击    //*[text()='所属项目名称']/following::div[@col-id="projectName"][1]
    当前页面可见字符    2025
    #关闭当前页面
    点击    //li[@class='ath-tab-menus-li ng-star-inserted ath-tab-menus-active']/i
    #冷库
    点击    //div[@class="ant-tabs-tab ng-star-inserted"]/div/div/div
    当前页面可见字符    暂无数据

筛选
    [Arguments]    ${scheduled_start_time}=null    ${scheduled_end_time}=null    ${card_type}=null    ${status}=null    ${create_time}=null    ${end_time}=null    ${project_source}=null    ${project_operation}=null    ${project_cycle_start}=null    ${project_cycle_end}=null
    点击    //span[contains(text(),'筛选')]    
    Run Keyword If    '${scheduled_start_time}' != 'null'    预计时间    ${scheduled_start_time}    ${scheduled_end_time}
    Run Keyword If    '${card_type}' != 'null'    卡片类型    ${card_type}
    Run Keyword If    '${status}' != 'null'    状态    ${status}
    Run Keyword If    '${create_time}' != 'null'    创建时间    ${create_time}    ${end_time}
    Run Keyword If    '${project_source}' != 'null'    项目/任务来源    ${project_source}
    Run Keyword If    '${project_operation}' != 'null'    项目任务操作    ${project_operation}
    Run Keyword If    '${project_cycle_start}' != 'null'    项目时距操作    ${project_cycle_start}    ${project_cycle_end}
    点击    //span[contains(text(),'确定')]

预计时间
    [Arguments]    ${scheduled_start_time}    ${scheduled_end_time}
    输入    xpath=(//input[@placeholder='开始日期'])[1]    ${scheduled_start_time}
    Press Key    xpath=(//input[@placeholder='开始日期'])[1]    \\13
    输入    xpath=(//input[@placeholder='结束日期'])[1]    ${scheduled_end_time}
    Press Key    xpath=(//input[@placeholder='结束日期'])[1]    \\13

卡片类型
    [Arguments]    ${card_type}
    点击    //ath-select-inside-label-placeholder//span[text()='卡片类型']/parent::*/preceding-sibling::*    
    Sleep    3
    IF    '${card_type}'=='全部'
        点击    //span[@class='ath-content']            
    ELSE
        点击    //span[@class='ant-tree-title'][contains(text(),'${card_type}')]
    END
    Sleep    2
    Open Context Menu    //span[contains(text(),'确定')]
    Sleep    2

状态
    [Arguments]    ${status}
    点击    //ath-select-inside-label-placeholder//span[text()='状态']/parent::*/preceding-sibling::*    
    点击    //span[@class='ant-tree-title'][contains(text(),'${status}')]    
    点击    //div[@class='cdk-overlay-container']

创建时间    
    [Arguments]    ${create_time}    ${end_time}
    输入    xpath=(//input[@placeholder='开始日期'])[2]    ${create_time}    
    Sleep    2
    Press Key    xpath=(//input[@placeholder='开始日期'])[2]    \\13
    输入    xpath=(//input[@placeholder='结束日期'])[2]    ${end_time}    
    点击    //div[@class='cdk-overlay-container']

项目时距操作
    [Arguments]    ${project_cycle_start}    ${project_cycle_end}
    输入    xpath=(//input[@placeholder='开始日期'])[3]    ${project_cycle_start}
    Sleep    2
    Press Key    xpath=(//input[@placeholder='开始日期'])[3]    \\13
    输入    xpath=(//input[@placeholder='结束日期'])[3]    ${project_cycle_end}
    点击    //div[@class='cdk-overlay-container']
    
项目/任务来源
    [Arguments]    ${project_source}
    点击    //ath-select-inside-label-placeholder//span[text()='项目/任务来源']/parent::*/preceding-sibling::*
    点击    //span[@class='ant-tree-title'][contains(text(),'${project_source}')]    
    点击    //div[@class='cdk-overlay-container']

项目任务操作
    [Arguments]    ${project_operation}
    点击    //ath-select-inside-label-placeholder//span[contains(text(),'项目/任务操作')]/parent::*/preceding-sibling::*    
    点击    //span[@class='ant-tree-title'][contains(text(),'${project_operation}')]    
    点击    //div[@class='cdk-overlay-container']