*** Settings ***
Library           SeleniumLibrary
Resource          ../../元素/Athena平台元素.robot
Resource          ../../配置/全局参数.robot
Resource          ../../配置/Athena平台.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../系统关键字/web.robot
Resource          ../系统关键字/接口.robot

*** Keywords ***
ABI报表查询
    点击    //div[@class='dynamic-form-operation-icon']
    点击    //div[@class='ag-pinned-left-header']/div/div
    Sleep    10
    点击    //span[contains(text(),'提交')]
    Sleep    10
    点击    //span[@class='ng-star-inserted'][contains(text(),'查询')]
    Sleep    10

报表设计
    点击    //span[contains(text(),'报表设计')]
    Sleep    10
    当前页面不可见字符    数据加载中
    点击    //a[contains(text(),'复制')]
    Sleep    10
    ${name}    生成毫秒时间戳
    输入    //section[@class='ant-input-group ath-input-group-wrap-inner']//input[@type='text']    ${name}
    点击    //span[contains(text(),'确定')]
    Sleep    20

报表导出
    点击    //span[@class='bi-menu-item-text'][contains(text(),'导出')]
    Sleep    10
    点击    //span[contains(text(),'确定')]
    Sleep    10

常用条件添加/进入
    Reload Page
    Sleep    10
    点击    //button/span[contains(text(),'重置')]
    点击    //div[@class='dynamic-form-operation-icon']
    Sleep    10
    点击    //div[@class='ag-pinned-left-header']/div/div
    Sleep    10
    点击    //span[contains(text(),'提交')]
    Sleep    10
    #点击    //span[@class="ant-checkbox"]
    点击    //span[contains(text(),'保存为常用条件')]
    ${code}    生成毫秒时间戳
    ${name}    Set Variable    ${code}自动化测试
    点击    //section[@class='ant-input-group ath-input-group-wrap-inner']/input[@type='text']
    输入    //input[contains(@class,'filter-condition-name ant-input')]    ${name}
    点击    //span[@class='ng-star-inserted'][contains(text(),'查询')]
    Sleep    10
    点击    //span[@class='bi-menu-item-text'][contains(text(),'查询')]
    Sleep    5
    点击    //div[contains(text(),'常用条件')]
    Sleep    2
    点击    //div[contains(text(),'${name}')]
    Sleep    2
    #打印
    点击    //span[@class='bi-menu-item-text'][contains(text(),'打印')]
    当前页面可见字符    报表打印
    当前页面可见    //span[contains(text(),'文件')]
    Sleep    30
    Comment    点击    //span[contains(text(),'文件')]
    Comment    点击    //span[contains(text(),'打印')]
    Comment    当前页面可见    //div[@class=" gc-spread-designer-filemenu-print-item"]/div/div
    点击    //i[@class="anticon ant-modal-close-icon anticon-close ng-star-inserted"]
    点击    //span[@class='ng-star-inserted'][contains(text(),'查询')]
    Sleep    5

常用条件删除
    Reload Page
    Sleep    5
    点击    //div[contains(text(),'常用条件')]
    Sleep    3
    #点击    //div[@class="comm-condition-list"]/div[1]/div[3]
    点击    //div[contains(@class,'comm-condition-item')][1]/div[@class='comm-condition-delete']
    Sleep    3
    点击    //span[contains(text(),'确定')]
    Sleep    3
