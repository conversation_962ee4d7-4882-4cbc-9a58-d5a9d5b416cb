import requests
import time
from typing import Optional, Dict, Any

# 配置常量
LOGIN_URL = "https://www.letuo.club/apis/auth/login"
QUERY_EVENT_URL = "https://www.letuo.club/apis/orderEvent/query"
SUBMIT_ORDER_URL = "https://www.letuo.club/apis/order/submit"

# 请求超时设置（秒）
REQUEST_TIMEOUT = 10
MAX_QUERY_ATTEMPTS = 30
QUERY_RETRY_INTERVAL = 0.5

# 用户凭证（建议通过环境变量或输入获取）
CREDENTIALS = {
    "username": "<EMAIL>",
    "password": "Cjm820412000",
    "validateCode": "",
    "token": "519d6a2b-6577-460f-99ae-e85bcf85697a",
    "code": "RU5L"
}

QUERY_PARAMS = {
    "pageIndex": 1,
    "pageSize": 10,
    "sortField": "id",
    "sortType": "descending",
    "value": {
        "teamId": 11,
        "status": 0,
        "name": ""
    }
}

ORDER_DATA_TEMPLATE = {
    "id": 0,
    "name": "紫燕夫妻肺片套餐",
    "description": "",
    "avatar": "https://www.letuo.club/meal/meal/36_896c0708258c4aefacedaee94d93361a.jpeg",
    "price": 3000,
    "teamId": 11,
    "shopId": 7,
    "createrId": 0,
    "createrName": "",
    "productId": 16,
    "productName": "紫燕夫妻肺片套餐",
    "num": 1,
    "status": 0,
    "imgs": ""
}


def create_session() -> requests.Session:
    """创建并配置请求会话"""
    session = requests.Session()
    session.headers.update({"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)"})
    return session


def perform_login(session: requests.Session) -> bool:
    """执行登录操作"""
    try:
        response = session.post(
            LOGIN_URL,
            json=CREDENTIALS,
            timeout=REQUEST_TIMEOUT
        )
        response.raise_for_status()

        # 验证登录是否成功
        if not response.cookies.get("JSESSIONID"):
            print("登录成功但未获取到JSESSIONID")
            return False
        return True
    except requests.exceptions.RequestException as e:
        print(f"登录请求失败: {str(e)}")
        return False


def fetch_event_id(session: requests.Session) -> Optional[int]:
    """获取有效的event ID"""
    for attempt in range(MAX_QUERY_ATTEMPTS):
        try:
            response = session.post(
                QUERY_EVENT_URL,
                json=QUERY_PARAMS,
                timeout=REQUEST_TIMEOUT
            )
            response.raise_for_status()

            result = response.json()
            if result.get("totalCount", 0) > 0 and len(result.get("data", [])) > 0:
                if (event_id := result["data"][0].get("eventId")):
                    return event_id

            time.sleep(QUERY_RETRY_INTERVAL)
        except (requests.exceptions.RequestException, KeyError) as e:
            print(f"查询事件ID失败（尝试 {attempt + 1}/{MAX_QUERY_ATTEMPTS}）: {str(e)}")
    return None


def submit_meal_order(session: requests.Session, event_id: int) -> bool:
    """提交订餐请求"""
    order_data = {**ORDER_DATA_TEMPLATE, "eventId": event_id}
    try:
        response = session.post(
            SUBMIT_ORDER_URL,
            json=order_data,
            timeout=REQUEST_TIMEOUT
        )
        response.raise_for_status()

        # 检查业务逻辑是否成功
        if response.json().get("success", False):
            print("订餐成功")
            return True
        print("订餐请求成功但业务逻辑失败")
        return False
    except requests.exceptions.RequestException as e:
        print(f"订餐请求失败: {str(e)}")
        return False


def main_workflow():
    """主业务流程"""
    session = create_session()

    if not perform_login(session):
        print("终止流程：登录失败")
        return

    if not (event_id := fetch_event_id(session)):
        print("终止流程：获取事件ID失败")
        return

    if not submit_meal_order(session, event_id):
        print("终止流程：订餐失败")
        return

    print("整个流程执行成功")


if __name__ == "__main__":
    main_workflow()