*** Settings ***
Library           SeleniumLibrary
Resource          ../系统关键字/web.robot

*** Keywords ***
进入交付设计器
    [Arguments]    ${menu}
    点击    //div[contains(text(),'${menu}')]

导入数据
    [Arguments]    ${tenant}    ${application}    ${parameter}    ${unit}    ${group}
    当元素不可见则刷新页面    3    //span[contains(text(),'导入数据')]
    点击    //span[contains(text(),'导入数据')]
    #选择租户
    ${loc}    按顺序获取元素    //ath-select-search[@class='ant-select-selection-search ng-star-inserted']    0
    点击    ${loc}
    点击    //span[contains(text(),'${tenant}')]    #选择对应租户
    ${loc}    按顺序获取元素    //ath-select-search[@class='ant-select-selection-search ng-star-inserted']    1
#    点击    ${loc}
    #此处无法滑动自定义控件的下拉选项，改成修改元素的属性，进行搜索后选择
    Execute Javascript    document.querySelector('.ant-select-selection-search-input.ng-untouched.ng-pristine.ng-valid').removeAttribute('style');
    Execute Javascript    document.querySelector('.ant-select-selection-search-input.ng-untouched.ng-pristine.ng-valid').removeAttribute('readonly');
    输入    (//input[@class='ant-select-selection-search-input ng-untouched ng-pristine ng-valid'])[1]    ${application}
    当前页面可见    //span[@class='search-Highlight'][contains(text(),'${application}')]
    点击    //span[@class='search-Highlight'][contains(text(),'${application}')]    #选择对应应用
    Click Element    //input[@class='ant-select-selection-search-input ng-untouched ng-pristine ng-valid']
    #Click Element    ${loc}    #点击对应参数
    当前页面不可见元素    //*[@class='anticon-spin']
    点击    //span[@class='ant-tree-title'][contains(text(),'${parameter}')]
    点击    //div[contains(text(),'选择导入来源')]
    Click Element    //input[@class='ant-select-selection-search-input ng-untouched ng-pristine ng-valid']
    当前页面不可见元素    //*[@class='anticon-spin']
    #Click Element    ${loc}
    点击    //span[contains(text(),'${unit}')]    #选择运营单元
    #点击空白处
    点击    //div[@class='cdk-overlay-container']
    #点击取消
    点击    //span[contains(text(),'确定')]
#    元素存在则点击    //span[contains(text(),'确定')]    5
    #校验 复制中
    当前页面可见    //div[@class='load-content-text'][text()='复制中']
    #校验复制结果
    当前页面可见    //div[@class='ant-modal-title']
    #关闭导入数据结果页面
    点击    //span[contains(text(),'知道了')]
    #进入运营单元（集团级）
    点击    //div[contains(text(),'${group}')]
#    #进入对应应用
    滑动元素到可见区域    //div[contains(text(),'${application}')]/ancestor::div[@class='app-card-container']//nz-spin
    当前页面不可见元素     //span[@class='ant-spin-dot ant-spin-dot-spin ng-star-inserted']
    点击    //div[@class='title-box ant-col'][contains(text(),'${application}')]
    点击    //div[contains(@class,'fth-menu-box')]/div[1]
    元素存在则点击    //span[contains(text(),'确定')]
#    点击    //ath-select-item[contains(text(),'三月内')]
#    点击    //span[contains(text(),'两月内')]
#    #导入后如果2边的订单负责人不是2个租户都存在的账号，可能造成订单项目负责人为空，此处选择第一个人，避免因人员为空而造成的无法生效
    #断言导入后的结果是否正确，写死
    当前页面可见    //ath-select-item[contains(text(),'HL18271405997 hl_test')]
    点击    //span[text()='订单项目负责人']/following::ath-select-arrow[1]
    #重新选择一个订单负责人，以便下次导入后验证导入结果
    Click Element    //span[contains(text(),'qcsupplier001 供应商1')]
    Sleep    5
    js点击    //span[@class='ng-star-inserted']
#    当前页面可见字符    设定已生效
#    当前页面不可见字符    设定已生效


点击通用设定
    [Arguments]    ${menu}
    当元素不可见则刷新页面    3    //*[contains(text(),'${menu}')]
    点击    //*[contains(text(),'${menu}')]

设定通用参数设定/待办工作台设定/敏捷数据设定
    [Arguments]    ${menu}
    元素存在则点击    //*[contains(text(),'${menu}')]

点击敏捷数据设定
    [Arguments]    ${menu}
    当元素不可见则刷新页面    3    //*[contains(text(),'${menu}')]
    点击    //li[contains(text(),'${menu}')]

生效通用参数设定
    当前页面可见    //div[@class='form-item-padding']
    元素存在则点击    //button[@athtype='primary']
    #当前页面可见字符    设定已生效    15

选择任务/项目
    [Arguments]    ${menu}
    点击    //div[@class='ath-normal-title'][contains(text(),'${menu}')]

选择分组设定/排序设定/筛选设定/卡面呈现
    [Arguments]    ${menu}
    点击    //div[contains(text(),'${menu}')]
    sleep    10
    当前页面可见    //div/ath-tabs[2]/nz-tabs-nav/div/div/div/div[@aria-selected='true']

点击设定
    当前页面不可见元素    //button[@disabled='true']
    点击    //div[3]/button
    Comment    当前页面可见    设定
#    当前页面可见字符    设定已生效    30

卡面呈现设定
    [Arguments]    ${showmode}
    当前页面可见    //button[@disabled='true']    3
    元素存在则点击    //div[@class='setting-title'][contains(text(),'${showmode}')]
    Sleep    3
    点击    //button[@class='ath-btn ant-btn ant-btn-primary']
    当前页面可见    //div[@class='option-container ng-star-inserted selected-display']    10

敏数设定同义词配置/新建同义词
    [Arguments]    ${properNoun}    ${entityTypeName}
    当前页面可见    //div[contains(text(),'同义词配置')]
    元素存在则点击    //app-set-options-button/div/button[1]/span
    当前页面可见    //div[@class='ant-modal-title']    3
    元素存在则点击    //input[@id='properNoun']
    输入    //input[@id='properNoun']    ${properNoun}
    元素存在则点击    //nz-select[@id='entityTypeName']
    当前页面可见    //nz-option-item[1]/div    3
    元素存在则点击    //div[@class='ant-select-item-option-content'][contains(text(),'${entityTypeName}')]
    元素存在则点击    //button[@class='ant-btn ant-btn-primary ng-star-inserted']
    当前页面可见    //td[@class='ant-table-cell'][contains(text(),'${properNoun}')]    3

敏数设定同义词配置/删除同义词
    [Arguments]    ${properNoun}
    当前页面可见    //td[@class='ant-table-cell'][contains(text(),'${properNoun}')]    3
    元素存在则点击    //a[@class='op-delete']
    当前页面可见    //div[contains(@class,'ant-popover-message-title')]    3
    元素存在则点击    //button[contains(@class,'ant-btn-primary ant-btn-sm')]
    当前页面不可见元素    //td[@class='ant-table-cell'][contains(text(),'${properNoun}')]    3

应用设定
    [Arguments]    ${group}    ${app}
    #进入运营单元
    当前页面可见    //div[contains(text(),'${group}')]    10
    #进入运营单元（集团级）
    点击    //div[contains(text(),'${group}')]
    #进入对应应用
    滑动元素到可见区域    //div[contains(text(),'应用参数设定')]/ancestor::div[@class='app-card-container']//nz-spin
    当前页面不可见元素     //span[@class='ant-spin-dot ant-spin-dot-spin ng-star-inserted']
    #再次点击
    点击    //div[@class='title-box ant-col'][contains(text(),'${app}')]
    当前页面可见    //div/div[@class='fth-box ng-star-inserted fth-selected']/i
    #应用参数设定
    点击    //div/div[@class='fth-box ng-star-inserted fth-selected']/i


数据驱动模型设定
    元素存在则点击    //div[@class='fth-menu-box expend box-selected ng-star-inserted']/div[2]
    当前页面可见    //span[@class='activity-name activity-title']    3

项目/任务要素设定
    元素存在则点击    //div[@class='fth-menu-box expend box-selected ng-star-inserted']/div[3]
    当前页面可见    //div[@class='task-left left-scroll-box ng-star-inserted']    3

切换项目要素设定页签
    [Arguments]    ${tabname}
    元素存在则点击    //div[@class='unitName ng-star-inserted']/span[contains(text(),'${tabname}')]
    当前页面可见    //div[@class='ant-tabs-tab ng-star-inserted ant-tabs-tab-active']    10

打开进阶设定
    [Arguments]    ${settype}    ${setname}
    元素存在则点击    //div[@class='task-name'][text()='项目设定']
    鼠标悬停    //div/span/span[@title='${setname}']/following-sibling::app-advanced-${settype}-setting/a/span
    点击    //div/span/span[@title='${setname}']/following-sibling::app-advanced-${settype}-setting/a/span
    Sleep    5
    当前页面可见    //div[@class='panel-center-container']    10
    点击    //button[@class='ath-btn ant-btn ant-btn-primary']
    当前页面可见字符    保存成功    30

关闭进阶设定
    元素存在则点击    //div[@class='cdk-overlay-container']/div[3]/div/div/div[2]/div/div/div[1]/div/button/i
    当前页面不可见元素    //div[@class='panel-center-container']    3

生效应用设定
    元素存在则点击    //button/span[contains(text(),'生效')]

机制生效
    [Arguments]    ${application}  ${mechanismName}  ${topic}
    点击    //div[@class='unit-card-container jituan']
    #等待loading消失
    当前页面不可见元素    //span[@class='ant-spin-dot ant-spin-dot-spin ng-star-inserted']
    #点击议题
    点击    //div[contains(text(),'${topic}')]
    当前页面可见    //div[@class='ant-col'][contains(text(),'${topic}')]
    #    点击    //div[contains(text(),'${application}')]
    点击    //div[@class='word'][contains(text(),'${mechanismName}')]
    点击    //span[text()=' 生效 ']
    当前页面可见字符    应用机制设定结果已生效！
    Sleep    10
    点击    //span[text()=' 生效 ']
    当前页面可见字符    应用机制设定结果已生效！
    Sleep    40
    #项目要素设定
    #bugid：145814
    点击    //div[contains(text(),'${application}')]
    点击    //div[@class='nav-sub ng-star-inserted']/div[4]/div[3]
    当前页面不可见字符    采购管理_项目_0002
    Sleep    10

应用下的机制生效
    [Arguments]    ${application}  ${mechanismName}
    点击    //div[@class='unit-card-container jituan']
    #等待loading消失
    当前页面不可见元素    //span[@class='ant-spin-dot ant-spin-dot-spin ng-star-inserted']
    #进入应用
    滑动元素到可见区域    //div[contains(text(),'${application}')]/ancestor::div[@class='app-card-container']//*[contains(text(),'机制设定')]
    当前页面不可见元素     //span[@class='ant-spin-dot ant-spin-dot-spin ng-star-inserted']

    点击    //div[contains(text(),'${application}')]
    点击    //div[@class='word'][contains(text(),'${mechanismName}')]
#    点击展开，生效该机制下的所有能力
    点击    //button[@type='button']
    点击    //span[text()=' 生效 ']
    当前页面可见字符    应用机制设定结果已生效！
    Sleep    10
    点击    //span[text()=' 生效 ']
    当前页面可见字符    应用机制设定结果已生效！
    Sleep    40

调整时机类型参数时间
    [Arguments]    ${parameter_name}
#    根据参数名称选择改变哪个时机参数
    点击    //app-frequency-param//div[contains(text(),'${parameter_name}')]

返回
    [Arguments]    ${top}
    点击    //a[contains(text(),'${top}')]

交付设计器进入应用设定页面    [Arguments]    ${app}
        滑动元素到可见区域    //div[contains(text(),'${app}')]/ancestor::div[@class='app-card-container']//nz-spin
        当前页面不可见元素     //span[@class='ant-spin-dot ant-spin-dot-spin ng-star-inserted']
        点击    //div[@class='title-box ant-col'][contains(text(),'${app}')]
        当前页面可见    //div/div[@class='fth-box ng-star-inserted fth-selected']/i