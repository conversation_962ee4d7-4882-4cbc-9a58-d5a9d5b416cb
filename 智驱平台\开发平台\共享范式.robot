*** Settings ***
Suite Setup       Run Keywords    环境设定
Suite Teardown    Run Keywords    关闭浏览器
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/业务关键字/Athena平台.robot
Resource          ../关键字/业务关键字/交付设计器.robot
Resource          ../关键字/业务关键字/开发平台.robot
Resource          ../关键字/业务关键字/公共方法.robot

*** Test Cases ***
新增共享范式
    [Documentation]    陈金明
    #需求范式发版41220
    #新增共享范式-新增机制-机制原理-机制能力-发布机制-生效机制-发起项目-检查机制能力是否生效
    ${username}    Set Variable If    '${ENV}'=='paas'    ${username}    '${ENV}'=='huawei.test'    ${username}    '${ENV}'=='huawei.prod'    ${username}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${password}    Set Variable If    '${ENV}'=='paas'    ${password}    '${ENV}'=='huawei.test'    ${password}    '${ENV}'=='huawei.prod'    ${password}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${application}    Set Variable If    '${ENV}'=='paas'    质量测试专用应用    '${ENV}'=='huawei.test'    采购管理88CN    '${ENV}'=='huawei.prod'    采购管理88CN
    ${appCode}    Set Variable If    '${ENV}'=='paas'    qctest001    '${ENV}'=='huawei.test'    purchase88CN    '${ENV}'=='huawei.prod'    purchase88CN
    ${tenant}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    智驱中台工作台    '${ENV}'=='huawei.prod'    智驱中台工作台
    ${tenantId}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    IntelligentDriveCenterWorkbench    '${ENV}'=='huawei.prod'    IntelligentDriveCenterWorkbench
    ${person_value}    Set Variable If    '${ENV}'=='paas'    测试职能    '${ENV}'=='huawei.test'    测试职能    '${ENV}'=='huawei.prod'    测试职能
    ${deploy_to_env}    Set Variable If    '${ENV}'=='paas'    大陆正式区（阿里）    '${ENV}'=='huawei.test'    大陆测试区    '${ENV}'=='huawei.prod'    大陆正式区
    ${token}    Set Variable If    '${ENV}'=='paas'    fb6f63fb-e9a0-4968-95ea-76e9481c29c3    '${ENV}'=='huawei.test'    1eee320f-c9f3-4f26-b827-7baa72d72ba8    '${ENV}'=='huawei.prod'    661c4af4-2b02-4c7a-9d47-e3b9836236c1
    Set Global Variable    ${topic}    自动化测试议题
    Set Global Variable    ${token}
    Set Global Variable    ${tenantId}
    #注释：共享范式新建
    #注释：范式名称和机制名称定义
    ${time}    生成秒时间戳
    Set Global Variable    ${paradigmName}    ${time}
    Set Global Variable    ${mechanismName}    ${time}
    登录开发平台    ${username}    ${password}    ${tenant}
    开发平台.点击顶部菜单    资产中心
    点击左侧菜单    范式管理
    Sleep    5
    新增共享范式    ${paradigmName}    ${application}
    搜索范式    ${paradigmName}
    #注释：此处需要添加范式处新增的部分
    范式发布    ${paradigmName}     ${deploy_to_env}
    #注释：新增机制并发版
    开发平台.点击顶部菜单    解决方案中心
    搜索应用    ${application}
    进入应用配置页    ${appCode}
    选择浏览器窗体    -1
    点击左侧菜单    范式设定
    范式列表搜索    ${paradigmName}
    点击左侧菜单    机制设计
    新增机制    ${mechanismName}    ${paradigmName}
    进入机制设计页    ${mechanismName}
    新增机制原理    机制原理名称    机制原理描述
    新增指派能力-特定人员    指派能力-按职能ID    指派能力描述    职能    ${person_value}    #支持,人员,职能和部门成员
    应用发布    ${deploy_to_env}    ${tenant}
    关闭浏览器
    #以下为调试代码
    #Set Global Variable    ${paradigmCode}    purchase88CN_PD_FGhbjaNC
    #Set Global Variable    ${mechanismCode}    ud_m_d2a9ee7cc105429ba9bb6e903b6137fa
    #Set Global Variable    ${mechanismName}    1734655822
    #以上为调试代码
    #购买范式（采用接口形式）定义范式购买的请求入参 
    ${data}    Set Variable     {"code":"${appCode}","operationUnit":null,"showType":"app"}
    ${mechanismCode_list}    查询已购买机制列表加需购买列表    ${data}
    ${mechanismCode_list}    Evaluate    json.dumps(${mechanismCode_list})    modules=json
    ${data}    Set Variable    {"appCode":"${appCode}","paradigm":"${paradigmCode}","mechanismCodes":${mechanismCode_list}}
    购买范式    ${data}
    #注释：Athena平台生效机制
    登录Athena平台    ${username}    ${password}
    租户切换    ${tenant}
    Athena平台.点击顶部菜单    全部
    进入交付设计器    交付设计器
    选择浏览器窗体    -1
    机制生效    ${application}    ${mechanismName}    ${topic}
    关闭浏览器
    #注释：发起项目检查机制逻辑，通过接口方式
    ${projectCode}    Set Variable If    '${ENV}'=='paas'    purchase_project_0001    '${ENV}'=='huawei.test'    purchase88CN_PU_HLnhr4A8    '${ENV}'=='huawei.prod'    purchase88CN_PU_HLnhr4A8
    ${data}    Set Variable    {"projectCode":"${projectCode}","process_EOC":{},"variables":{"classification_mode":"1","from_inquiry":"false","is_inquiry":"false"},"dispatchData":[{}]}
    发起项目    ${data}
    ${serialNumber}    Set Variable    ${response_dict["response"]["serialNumber"]}
    Sleep    20
    ${data}    Set Variable    {"locale":"zh_CN","serialNumber":"${serialNumber}"}
    获取项目详情    ${data}
    ${taskUid}    Set Variable    ${response_dict["response"]["taskInstances"][0]["data"]["taskUid"]}
    ${data}    Set Variable    {"taskUid":"${taskUid}"}
    获取任务详情    ${data}
    ${performerId}    Set Variable    ${response_dict["response"]["list"][0]["steps"][0]["performerId"]}
    #注释：*******************为测试职能设置的人员，在鼎捷云配置
    指派能力断言    <EMAIL>    ${performerId}
    #注释：完成机制能力校验后的数据清理，删除机制和范式
    登录开发平台    ${username}    ${password}    ${tenant}
    开发平台.点击顶部菜单    解决方案中心
    搜索应用    ${application}
    进入应用配置页    ${appCode}
    选择浏览器窗体    -1
    点击左侧菜单    机制设计
    机制列表搜索    ${mechanismName}
    删除机制    ${mechanismName}
    跳转到首页
    开发平台.点击顶部菜单    资产中心
    点击左侧菜单    范式管理
    Sleep    5
    搜索范式    ${paradigmName}
    共享范式解绑    ${paradigmName}    ${application}
    删除共享范式    ${paradigmName}
    [Teardown]    Run Keywords    关闭浏览器

复制-复制和共享范式新增修改删除解绑
    [Documentation]    陈金明
    #注释：需求范式发版41220
    #注释：新增共享范式-新增机制-机制原理-机制能力-发布机制-生效机制-发起项目-检查机制能力是否生效
    ${username}    Set Variable If    '${ENV}'=='paas'    ${username}    '${ENV}'=='huawei.test'    ${username}    '${ENV}'=='huawei.prod'    ${username}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${password}    Set Variable If    '${ENV}'=='paas'    ${password}    '${ENV}'=='huawei.test'    ${password}    '${ENV}'=='huawei.prod'    ${password}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${application}    Set Variable If    '${ENV}'=='paas'    质量测试专用应用    '${ENV}'=='huawei.test'    采购管理88CN    '${ENV}'=='huawei.prod'    采购管理88CN
    ${appCode}    Set Variable If    '${ENV}'=='paas'    qctest001    '${ENV}'=='huawei.test'    purchase88CN    '${ENV}'=='huawei.prod'    purchase88CN
    ${tenant}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    智驱中台工作台    '${ENV}'=='huawei.prod'    智驱中台工作台
    ${tenantId}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    IntelligentDriveCenterWorkbench    '${ENV}'=='huawei.prod'    IntelligentDriveCenterWorkbench
    ${person_value}    Set Variable If    '${ENV}'=='paas'    测试职能    '${ENV}'=='huawei.test'    测试职能    '${ENV}'=='huawei.prod'    测试职能
    ${deploy_to_env}    Set Variable If    '${ENV}'=='paas'    大陆正式区（阿里）    '${ENV}'=='huawei.test'    大陆测试区    '${ENV}'=='huawei.prod'    大陆正式区
    ${token}    Set Variable If    '${ENV}'=='paas'    fb6f63fb-e9a0-4968-95ea-76e9481c29c3    '${ENV}'=='huawei.test'    1eee320f-c9f3-4f26-b827-7baa72d72ba8    '${ENV}'=='huawei.prod'    661c4af4-2b02-4c7a-9d47-e3b9836236c1
    Set Global Variable    ${topic}    自动化测试议题
    Set Global Variable    ${token}
    Set Global Variable    ${tenantId}
    #注释：共享范式新建
    #注释：范式名称和机制名称定义
    ${time}    生成秒时间戳
    Set Global Variable    ${paradigmName}    ${time}
    Set Global Variable    ${mechanismName}    ${time}
    ${time}    生成秒时间戳
    Set Global Variable    ${paradigmName}    ${time}
    登录开发平台    ${username}    ${password}    ${tenant}
    开发平台.点击顶部菜单    资产中心
    点击左侧菜单    范式管理
    Sleep    10
    新增复制范式    ${paradigmName}    ${application}
    #注释：此处需要添加范式处新增的部分
    搜索范式    ${paradigmName}
    删除共享范式    ${paradigmName}
#    [Teardown]    Run Keywords    关闭浏览器
#
#新增共享复制范式
#    [Documentation]    陈金明
#    #注释：需求范式发版41220
#    #注释：新增共享范式-新增机制-机制原理-机制能力-发布机制-生效机制-发起项目-检查机制能力是否生效
#    ${username}    Set Variable If    '${ENV}'=='paas'    ${username}    '${ENV}'=='huawei.test'    ${username}    '${ENV}'=='huawei.prod'    ${username}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
#    ${password}    Set Variable If    '${ENV}'=='paas'    ${password}    '${ENV}'=='huawei.test'    ${password}    '${ENV}'=='huawei.prod'    ${password}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
#    ${application}    Set Variable If    '${ENV}'=='paas'    质量测试专用应用    '${ENV}'=='huawei.test'    采购管理88CN    '${ENV}'=='huawei.prod'    采购管理88CN
#    ${appCode}    Set Variable If    '${ENV}'=='paas'    qctest001    '${ENV}'=='huawei.test'    purchase88CN    '${ENV}'=='huawei.prod'    purchase88CN
#    ${tenant}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    智驱中台工作台    '${ENV}'=='huawei.prod'    智驱中台工作台
#    ${tenantId}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    IntelligentDriveCenterWorkbench    '${ENV}'=='huawei.prod'    IntelligentDriveCenterWorkbench
#    ${person_value}    Set Variable If    '${ENV}'=='paas'    测试职能    '${ENV}'=='huawei.test'    测试职能    '${ENV}'=='huawei.prod'    测试职能
#    ${deploy_to_env}    Set Variable If    '${ENV}'=='paas'    大陆正式区（阿里）    '${ENV}'=='huawei.test'    大陆测试区    '${ENV}'=='huawei.prod'    大陆正式区
#    ${token}    Set Variable If    '${ENV}'=='paas'    fb6f63fb-e9a0-4968-95ea-76e9481c29c3    '${ENV}'=='huawei.test'    1eee320f-c9f3-4f26-b827-7baa72d72ba8    '${ENV}'=='huawei.prod'    661c4af4-2b02-4c7a-9d47-e3b9836236c1
#    Set Global Variable    ${topic}    自动化测试议题
#    Set Global Variable    ${token}
#    Set Global Variable    ${tenantId}
#    ${time}    生成秒时间戳
#    Set Global Variable    ${paradigmName}    ${time}
#    登录开发平台    ${username}    ${password}    ${tenant}
#    开发平台.点击顶部菜单    资产中心
#    点击左侧菜单    范式管理
#    Sleep    10
    新增共享和复制范式    ${paradigmName}    ${application}    是
    搜索范式    ${paradigmName}
    编辑共享范式    ${paradigmName}
    #注释：此处需要添加范式处新增的部分
    搜索范式    ${paradigmName}修改
    共享范式解绑    ${paradigmName}修改    ${application}
    删除共享范式    ${paradigmName}修改
#    [Teardown]    Run Keywords    关闭浏览器
#
#共享范式解绑
#    [Documentation]    陈金明
#    #共享范式下已经存在机制则不允许删除，此处采用预置数据测试删除逻辑
#    #范式名称定义
#    #需求范式发版41220
#    #新增共享范式-新增机制-机制原理-机制能力-发布机制-生效机制-发起项目-检查机制能力是否生效
#    ${username}    Set Variable If    '${ENV}'=='paas'    ${username}    '${ENV}'=='huawei.test'    ${username}    '${ENV}'=='huawei.prod'    ${username}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
#    ${password}    Set Variable If    '${ENV}'=='paas'    ${password}    '${ENV}'=='huawei.test'    ${password}    '${ENV}'=='huawei.prod'    ${password}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
#    ${application}    Set Variable If    '${ENV}'=='paas'    质量测试专用应用    '${ENV}'=='huawei.test'    采购管理88CN    '${ENV}'=='huawei.prod'    采购管理88CN
#    ${appCode}    Set Variable If    '${ENV}'=='paas'    qctest001    '${ENV}'=='huawei.test'    purchase88CN    '${ENV}'=='huawei.prod'    purchase88CN
#    ${tenant}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    智驱中台工作台    '${ENV}'=='huawei.prod'    智驱中台工作台
#    ${tenantId}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    IntelligentDriveCenterWorkbench    '${ENV}'=='huawei.prod'    IntelligentDriveCenterWorkbench
#    ${person_value}    Set Variable If    '${ENV}'=='paas'    测试职能    '${ENV}'=='huawei.test'    测试职能    '${ENV}'=='huawei.prod'    测试职能
#    ${deploy_to_env}    Set Variable If    '${ENV}'=='paas'    大陆正式区（阿里）    '${ENV}'=='huawei.test'    大陆测试区    '${ENV}'=='huawei.prod'    大陆正式区
#    ${token}    Set Variable If    '${ENV}'=='paas'    fb6f63fb-e9a0-4968-95ea-76e9481c29c3    '${ENV}'=='huawei.test'    1eee320f-c9f3-4f26-b827-7baa72d72ba8    '${ENV}'=='huawei.prod'    661c4af4-2b02-4c7a-9d47-e3b9836236c1
#    Set Global Variable    ${topic}    自动化测试议题
#    #注释：范式名称定义
#    ${time}    生成秒时间戳
#    Set Global Variable    ${paradigmName}    ${time}
#    登录开发平台    ${username}    ${password}    ${tenant}
#    开发平台.点击顶部菜单    资产中心
#    点击左侧菜单    范式管理
#    Sleep    10
    新增共享范式    ${paradigmName}    ${application}
    搜索范式    ${paradigmName}
    共享范式解绑    ${paradigmName}    ${application}
    共享范式解绑后校验    ${paradigmName}
    删除共享范式    ${paradigmName}
    [Teardown]    Run Keywords    关闭浏览器

#共享范式通过编辑解绑
#    [Documentation]    陈金明
#    #共享范式下已经存在机制则不允许删除，此处采用预置数据测试删除逻辑
#    #范式名称定义
#    #需求范式发版41220
#    #新增共享范式-新增机制-机制原理-机制能力-发布机制-生效机制-发起项目-检查机制能力是否生效
#    ${username}    Set Variable If    '${ENV}'=='paas'    ${username}    '${ENV}'=='huawei.test'    ${username}    '${ENV}'=='huawei.prod'    ${username}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
#    ${password}    Set Variable If    '${ENV}'=='paas'    ${password}    '${ENV}'=='huawei.test'    ${password}    '${ENV}'=='huawei.prod'    ${password}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
#    ${application}    Set Variable If    '${ENV}'=='paas'    质量测试专用应用    '${ENV}'=='huawei.test'    采购管理88CN    '${ENV}'=='huawei.prod'    采购管理88CN
#    ${appCode}    Set Variable If    '${ENV}'=='paas'    qctest001    '${ENV}'=='huawei.test'    purchase88CN    '${ENV}'=='huawei.prod'    purchase88CN
#    ${tenant}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    智驱中台工作台    '${ENV}'=='huawei.prod'    智驱中台工作台
#    ${tenantId}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    IntelligentDriveCenterWorkbench    '${ENV}'=='huawei.prod'    IntelligentDriveCenterWorkbench
#    ${person _value}    Set Variable If    '${ENV}'=='paas'    测试职能    '${ENV}'=='huawei.test'    测试职能    '${ENV}'=='huawei.prod'    测试职能
#    ${deploy_to_env}    Set Variable If    '${ENV}'=='paas'    大陆正式区（阿里）    '${ENV}'=='huawei.test'    大陆测试区    '${ENV}'=='huawei.prod'    大陆正式区
#    ${token}    Set Variable If    '${ENV}'=='paas'    fb6f63fb-e9a0-4968-95ea-76e9481c29c3    '${ENV}'=='huawei.test'    1eee320f-c9f3-4f26-b827-7baa72d72ba8    '${ENV}'=='huawei.prod'    661c4af4-2b02-4c7a-9d47-e3b9836236c1
#    Set Global Variable    ${topic}    自动化测试议题
#    #注释：范式名称定义
#    ${time}    生成秒时间戳
#    Set Global Variable    ${paradigmName}    ${time}
#    登录开发平台    ${username}    ${password}    ${tenant}
#    开发平台.点击顶部菜单    资产中心
#    点击左侧菜单    范式管理
    新增共享范式    ${paradigmName}    ${application}
    搜索范式    ${paradigmName}
    编辑共享范式解绑    ${paradigmName}
    共享范式解绑后校验    ${paradigmName}
    删除共享范式    ${paradigmName}
#    [Teardown]    Run Keywords    关闭浏览器

#共享范式解绑限制
#    [Documentation]    陈金明
#    #共享范式下已经存在机制则不允许删除，此处采用预置数据测试删除逻辑
#    #范式名称定义
#    #需求范式发版41220
#    #新增共享范式-新增机制-机制原理-机制能力-发布机制-生效机制-发起项目-检查机制能力是否生效
#    ${username}    Set Variable If    '${ENV}'=='paas'    ${username}    '${ENV}'=='huawei.test'    ${username}    '${ENV}'=='huawei.prod'    ${username}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
#    ${password}    Set Variable If    '${ENV}'=='paas'    ${password}    '${ENV}'=='huawei.test'    ${password}    '${ENV}'=='huawei.prod'    ${password}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
    ${application}    Set Variable If    '${ENV}'=='paas'    质量测试专用应用    '${ENV}'=='huawei.test'    采购管理88CN    '${ENV}'=='huawei.prod'    采购管理88CN
#    ${appCode}    Set Variable If    '${ENV}'=='paas'    qctest001    '${ENV}'=='huawei.test'    purchase88CN    '${ENV}'=='huawei.prod'    purchase88CN
#    ${tenant}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    智驱中台工作台    '${ENV}'=='huawei.prod'    智驱中台工作台
#    ${tenantId}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    IntelligentDriveCenterWorkbench    '${ENV}'=='huawei.prod'    IntelligentDriveCenterWorkbench
#    ${person_value}    Set Variable If    '${ENV}'=='paas'    测试职能    '${ENV}'=='huawei.test'    测试职能    '${ENV}'=='huawei.prod'    测试职能
#    ${deploy_to_env}    Set Variable If    '${ENV}'=='paas'    大陆正式区（阿里）    '${ENV}'=='huawei.test'    大陆测试区    '${ENV}'=='huawei.prod'    大陆正式区
#    ${token}    Set Variable If    '${ENV}'=='paas'    fb6f63fb-e9a0-4968-95ea-76e9481c29c3    '${ENV}'=='huawei.test'    1eee320f-c9f3-4f26-b827-7baa72d72ba8    '${ENV}'=='huawei.prod'    661c4af4-2b02-4c7a-9d47-e3b9836236c1
#    Set Global Variable    ${topic}    自动化测试议题
    Set Global Variable    ${paradigmName}    预置共享范式不可删除
    Set Global Variable    ${application}    ${application}
#    登录开发平台    ${username}    ${password}    ${tenant}
#    开发平台.点击顶部菜单    资产中心
#    点击左侧菜单    范式管理
    搜索范式    ${paradigmName}
    共享范式解绑    ${paradigmName}    ${application}
    当前页面可见字符    当前已选择应用不可解绑，请先移除${application}中范式下关联的机制设计内容及组件清单
    点击    //span[contains(text(),'取消')]
#    [Teardown]    Run Keywords    关闭浏览器
#
#共享范式编辑解绑限制
#    [Documentation]    陈金明
#    #需求范式发版41220
#    #新增共享范式-新增机制-机制原理-机制能力-发布机制-生效机制-发起项目-检查机制能力是否生效
#    ${username}    Set Variable If    '${ENV}'=='paas'    ${username}    '${ENV}'=='huawei.test'    ${username}    '${ENV}'=='huawei.prod'    ${username}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
#    ${password}    Set Variable If    '${ENV}'=='paas'    ${password}    '${ENV}'=='huawei.test'    ${password}    '${ENV}'=='huawei.prod'    ${password}    '${ENV}'=='microsoft.prod'    ProdWrAthenaAutoTestSd01    '${ENV}'=='muihuawei.test'    TestAthenaAutoTestAi002
#    ${application}    Set Variable If    '${ENV}'=='paas'    质量测试专用应用    '${ENV}'=='huawei.test'    采购管理88CN    '${ENV}'=='huawei.prod'    采购管理88CN
#    ${appCode}    Set Variable If    '${ENV}'=='paas'    qctest001    '${ENV}'=='huawei.test'    purchase88CN    '${ENV}'=='huawei.prod'    purchase88CN
#    ${tenant}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    智驱中台工作台    '${ENV}'=='huawei.prod'    智驱中台工作台
#    ${tenantId}    Set Variable If    '${ENV}'=='paas'    自动化测试环境    '${ENV}'=='huawei.test'    IntelligentDriveCenterWorkbench    '${ENV}'=='huawei.prod'    IntelligentDriveCenterWorkbench
#    ${person_value}    Set Variable If    '${ENV}'=='paas'    测试职能    '${ENV}'=='huawei.test'    测试职能    '${ENV}'=='huawei.prod'    测试职能
#    ${deploy_to_env}    Set Variable If    '${ENV}'=='paas'    大陆正式区（阿里）    '${ENV}'=='huawei.test'    大陆测试区    '${ENV}'=='huawei.prod'    大陆正式区
#    ${token}    Set Variable If    '${ENV}'=='paas'    fb6f63fb-e9a0-4968-95ea-76e9481c29c3    '${ENV}'=='huawei.test'    1eee320f-c9f3-4f26-b827-7baa72d72ba8    '${ENV}'=='huawei.prod'    661c4af4-2b02-4c7a-9d47-e3b9836236c1
#    Set Global Variable    ${topic}    自动化测试议题
#    #注释：共享范式下已经存在机制则不允许删除，此处采用预置数据测试删除逻辑
#    #注释：范式名称定义
#    Set Global Variable    ${paradigmName}    预置共享范式不可删除
#    Set Global Variable    ${application}    ${application}
#    登录开发平台    ${username}    ${password}    ${tenant}
#    开发平台.点击顶部菜单    资产中心
#    点击左侧菜单    范式管理
    搜索范式    ${paradigmName}    
    编辑共享范式解绑限制    ${paradigmName}    ${application}
