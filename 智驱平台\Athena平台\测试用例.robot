*** Settings ***
Resource          ../关键字/系统关键字/接口.robot
Resource          ../关键字/业务关键字/接口域名.robot
Library           Collections
#Library           OperatingSystem


*** Test Cases ***
case1
    Set Global Variable    ${token}    4c262427-2019-4786-8995-10f21fa8b8ce
    Set Global Variable    ${tenantId}    IntelligentDriveCenterWorkbench
    ${mechanisms_list}    Create List
    ${data}    Set Variable     {"code":"purchase88CN","operationUnit":null,"showType":"app"}
    查询应用机制列表    ${data}    
    ${count}    Get Length    ${response_dict['data']['mechanisms']}
    Log    ${count}

#
#    ${index}    Set Variable    0
#    WHILE    ${index}<${count}
#        ${mem_code}    Set Variable    ${response_dict['data']['mechanisms'][${index}]['code']}
#        ${index}    Evaluate    ${index}+1
#        Append To List    ${mechanisms_list}    ${mem_code}
#    END
#    Log Many    ${mechanisms_list}
#
*** Keywords ***
查询应用机制列表
    [Arguments]    ${data}
    POST    isv-gateway.apps.digiwincloud.com.cn    /deliverydesigner/application/getMechanismsAndParadigm    ${data}