*** Settings ***
Library    SeleniumLibrary
Library    DatabaseLibrary
Library    String
Resource          ../关键字/系统关键字/web.robot
Resource          ../关键字/系统关键字/接口.robot
Resource          ../关键字/系统关键字/mysql.robot
Resource          ../配置/人员手机号码.robot
Resource          ../配置/DB.robot


*** Variables ***
${BASE_URL}        http://172.16.2.154:9989/view/
${WEBHOOK_URL}     qyapi.weixin.qq.com
${WEBHOOK_KEY}     8d14b45b-29a2-421a-b709-2b411151c18a
${BROWSER_OPTIONS}    add_argument('--lang=zh-CN');add_argument('--no-sandbox');add_argument('--disable-gpu');add_argument('--disable-dev-shm-usage')

${SUCCESS}    0
${ALL}    0

*** Keywords ***
检查项目状态并通知
    [Arguments]    ${view_path}
    Open Browser    ${BASE_URL}${view_path}    headlesschrome    options=${BROWSER_OPTIONS}
    ${ele_counts}    获取元素数量    //span[@class='pass-text']
    ${sum}    Evaluate    ${ele_counts} + 1
    FOR    ${index}    IN RANGE    1    ${sum}
        ${text}    获取元素字符    (//span[@class='pass-text'])[ ${index}]
        # 使用Split String关键字按空格分割字符串
        ${parts}    Split String    ${text}    ${SPACE}
        # 提取第一个数字（4）
        ${s}    Set Variable    ${parts[0]}
        # 提取第三个元素（5）
        ${a}    Set Variable    ${parts[2]}
        # 如果需要将变量转换为整数
        ${s_int}    Convert To Integer    ${s}
        ${a_int}    Convert To Integer    ${a}
        ${SUCCESS}    Evaluate    ${SUCCESS} + ${s_int}
        ${ALL}    Evaluate    ${ALL} + ${a_int}
        ${FAIL}    Evaluate    ${ALL} - ${SUCCESS}

    END
    ${SUCCESS_RATE}    Evaluate    ${SUCCESS}/${ALL}
    ${SUCCESS_RATE_PERCENT}    Evaluate    "{:.2%}".format(${SUCCESS_RATE})
    Log    成功率（百分比格式）: ${SUCCESS_RATE_PERCENT}
    发送企业微信通知    ${SUCCESS}    ${FAIL}    ${ALL}    ${SUCCESS_RATE_PERCENT}
    Close Browser

发送企业微信通知
    [Arguments]    ${SUCCESS}    ${FAIL}    ${ALL}    ${SUCCESS_RATE_PERCENT}
    ${data}    Set Variable    {"msgtype":"text","text":{"content":"今日测试区共执行${ALL}个UI场景用例，其中失败${FAIL}个，自动化通过率 ${SUCCESS_RATE_PERCENT}"}}
    POST-UI    ${WEBHOOK_URL}    cgi-bin/webhook/send?key=${WEBHOOK_KEY}    ${data}

*** Test Cases ***
主测试流程
    连接数据库    ${MSDB}
#    检查项目状态并通知    01预生产和生产冒烟回归/
    检查项目状态并通知    02测试环境冒烟必测/
    断开数据库连接