*** Settings ***
Library           SeleniumLibrary
Resource          ../系统关键字/web.robot
Resource          ../../配置/全局参数.robot
Resource          ../系统关键字/公共方法.robot
Resource          ../系统关键字/接口.robot
Resource          Athena平台控件.robot
Resource          ../../元素/开发平台元素.robot
Resource          ../../配置/域名.robot
Resource          Athena平台.robot

*** Keywords ***
判断是否设置代理人
    [Arguments]    ${name}
    ${Keywords}    Get Element Count    //a[contains(text(),'设置')]
    Run Keyword If    '${Keywords}'=='0'
    ...    Run Keyword    点击    //ath-switch[@id='openAgent']//button[@type='button']/span[1]
    Run Keyword If    '${Keywords}'=='1'
    ...    Run Keyword    点击    //a[contains(text(),'设置')]
    Sleep    3

关闭标签
    FOR    ${a}    IN RANGE    4
        sleep    1
        ${count}    Get Element Count    //li[@class='ath-tab-menus-li ng-star-inserted ath-tab-menus-active']/i[@nztype='close']
        Run Keyword If    '${count}'>'0'    点击    //li[@class='ath-tab-menus-li ng-star-inserted ath-tab-menus-active']/i[@nztype='close']
    END

清空筛选
    #此处使用js点击，避免因消息弹窗遮盖按钮
    Sleep    3
    js点击    //span[contains(text(),'筛选')]
    js点击    //span[contains(text(),'重置')]
    js点击    //span[contains(text(),'确定')]

清空筛选(繁体)
    #此处使用js点击，避免因消息弹窗遮盖按钮
    Sleep    3
    js点击    //span[contains(text(),'篩選')]
    js点击    //span[contains(text(),'重置')]
    js点击    //span[contains(text(),'確定')]

搜索
    [Arguments]    ${type}    ${query}    ${result}
    ${element_exists}    判断元素是否可见    //app-smart-search[@class='ng-star-inserted']
    IF    '${element_exists}'=='False'
        点击    //*[contains(text(),'搜索')]
    END
    点击    //div[@class='search-tab-text'][contains(text(),'${type}')]
    输入    //input[@placeholder='请输入关键词进行搜索']    ${query}
    当前页面可见字符    ${result}
    点击    //span[@class='keywords']

鼎捷云打开判断
    [Arguments]    ${window}=鼎捷云
    ${title}    Get Title
    选择窗体    ${window}
    Close Window
    选择窗体    ${title}

项目/任务搜索
    [Arguments]    ${type}    ${query}    ${result}
    搜索    ${type}    ${query}    ${result}

发起项目/基础资料/报表搜索
    [Arguments]    ${type}    ${query}    ${result}
    搜索    ${type}    ${query}    ${result}
    当前页面可见字符    ${type}
    当前页面可见字符    ${query}

友情链接
    点击顶部菜单    友情链接
    当前页面可见字符    鼎捷云控制台

判断后跳转到目标页面
    [Arguments]    ${path}
    Sleep    10
    ${url}    Get Location
    Capture Page Screenshot
    Run Keyword If    '${url}' != '${ATHENA_ENV}${path}'    Go To    ${ATHENA_ENV}${path}
    Sleep    10
    Wait Until Location Is    ${ATHENA_ENV}${path}
    Capture Page Screenshot
    #等待loading消失
    当前页面不可见元素    //span[@class='ant-spin-dot ant-spin-dot-spin ng-star-inserted']
    当前页面可见字符    我的任务

常规分组
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'我的任务')]
    点击    //span[contains(text(),'分组')]
    点击    //*[contains(text(),'常规分组')]
    #不分组
    点击    //span[contains(text(),'不分组')]
    点击    //span[contains(text(),'确定')]
    Sleep    3
    点击    //span[contains(text(),'分组')]
    按任务类型分组
    按是否逾期分组
    按项目类型分组

按任务类型分组
    #升序
    点击    //span[contains(text(),'重置')]
    Sleep    2
    点击    //span[contains(text(),'确定')]
    Sleep    2
    Comment    should contain    //h5[contains(text(),'分组目录')]    分组目录
    Comment    should contain    //p[contains(text(),'手动任务')]    手动任务
    Sleep    2
    点击    //span[contains(text(),'分组')]
    #降序
    点击    //span[contains(text(),'重置')]
    点击    //div[@class="group-set-item common-item group-set-item-selected ng-star-inserted"]//span[contains(text(),'降序')]
    点击    //span[contains(text(),'确定')]
    Sleep    2

按是否逾期分组
    #升序
    点击    //span[contains(text(),'分组')]
    点击    //span[contains(text(),'按是否逾期分组')]
    点击    //span[contains(text(),'确定')]
    Sleep    5
    Comment    should contain    //h5[contains(text(),'分组目录')]    分组目录
    Comment    should contain    //p[contains(text(),'逾期任务')]    逾期任务
    Sleep    3
    #降序
    点击    //span[contains(text(),'分组')]
    点击    //div[@class="group-item-list ng-star-inserted"]/div[2]/ath-radio-group/label[2]/span[2]
    点击    //span[contains(text(),'确定')]
    Sleep    3
    should contain    //h5[contains(text(),'分组目录')]    分组目录
    should contain    //p[contains(text(),'逾期任务')]    逾期任务
    Sleep    3

按项目类型分组
    #升序
    点击    //span[contains(text(),'分组')]
    点击    //span[contains(text(),'按项目类型分组')]
    点击    //span[contains(text(),'确定')]
    should contain    //h5[contains(text(),'分组目录')]    分组目录
    should contain    //p[contains(text(),'行事历')]    行事历
    Sleep    3
    #降序
    点击    //span[contains(text(),'分组')]
    点击    //div[@class="group-item-list ng-star-inserted"]/div[3]/ath-radio-group/label[2]/span[2]
    点击    //span[contains(text(),'确定')]
    Sleep    3
    should contain    //h5[contains(text(),'分组目录')]    分组目录
    should contain    //p[contains(text(),'行事历')]    行事历
    should contain    //span[@class='todo-common-tool-option todo-common-tool-hover-active todo-common-tool-option-active']//div[@class='active-num ng-star-inserted'][contains(text(),'1')]    1
    Sleep    3

高级分组
    点击    //span[contains(text(),'分组')]
    点击    //*[contains(text(),'高级分组')]
    点击    //span[contains(text(),'重置')]
    Sleep    2
    点击    //div[@class='group-set-container ng-star-inserted']/ath-tabs/div/div/div[2]/div/div/nz-cascader/div/div
    点击    //li[@title="是否逾期"]
    点击    //div[@class='delete-condition ng-star-inserted']    #删除高级分组
    Sleep    2
    点击    //div[@class='group-set-container ng-star-inserted']/ath-tabs/div/div/div[2]/div/div/nz-cascader/div/div
    点击    //li[@title="是否逾期"]
    点击    //span[contains(text(),'确定')]
    Sleep    2
    should contain    //h5[contains(text(),'分组目录')]    分组目录
    should contain    //p[contains(text(),'逾期任务')]    逾期任务
    Sleep    3

我的任务排序入口
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'我的任务')]
    js点击    //span[contains(text(),'排序')]
    点击    //span[contains(text(),'重置')]
    Sleep    5
    #条件拖动
    Drag And Drop By Offset    //div[@class="cdk-drop-list sort-item-list"]/div[1]/div[1]    0    100
    Sleep    5
    点击    //span[contains(text(),'确定')]
    Sleep    5
    Should Contain    //div[contains(text(),'3')]    3
    Sleep    2

运营单元
    跳转网页    /todo/task
    Sleep    5
    Js点击    //div[contains(text(),'我的任务')]
    Sleep    5
    js点击    //span[contains(text(),'更多')]
    点击    //span[@class="ant-switch-handle"]
    当前页面可见字符    全部
    Sleep    5
    点击    //span[@class="ant-switch-handle"]
    Sleep    5

常规筛选
    跳转网页    /todo/task
    Sleep    5
    js点击    //div[contains(text(),'我的任务')]
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'常规筛选')]
    Sleep    2
    点击    //div[@class="filter-list-container ng-star-inserted"]/app-todo-comm-filter/div[2]
    点击    //span[contains(text(),'未读')]
    Sleep    2
    点击    //*[contains(text(),'高级筛选')]
    点击    //*[contains(text(),'常规筛选')]
    点击    //span[contains(text(),'确定')]
    Sleep    5
    当前页面可见字符    New
    Sleep    5
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'常规筛选')]
    点击    //span[contains(text(),'重置')]
    点击    //span[contains(text(),'确定')]
    Sleep    5

高级筛选
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //div[@class='filter-item-row']/ath-select
    点击    //span[contains(text(),'任务类型')]
    输入    //app-todo-filter-item[@class='ng-star-inserted']//input    行事历
    点击    //span[@class='font-highlight']
    点击    //*[contains(text(),'常规筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //span[contains(text(),'确定')]
    Sleep    5
    任务/项目名称搜索    HL的行事历
    当前页面可见字符    HL的行事历
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //span[contains(text(),'重置')]
    Sleep    5
    点击    //span[contains(text(),'确定')]
    Sleep    2
    #删除高级筛选条件
    js点击    //span[contains(text(),'筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //div[@class='filter-item-row']/ath-select
    点击    //span[contains(text(),'任务类型')]
    输入    //app-todo-filter-item[@class='ng-star-inserted']//input    行事历
    点击    //span[@class='font-highlight']
    点击    //*[contains(text(),'常规筛选')]
    点击    //*[contains(text(),'高级筛选')]
    点击    //div[@class="filter-list-container ng-star-inserted"]/app-todo-senior-filter/app-todo-senior-filter-item[1]/div/div[4]
    点击    //span[contains(text(),'确定')]
    Sleep    2

项目常规分组
    跳转网页    /todo/task
    Sleep    5
    点击    //div[contains(text(),'我的项目')]
    js点击    //span[contains(text(),'分组')]
    点击    //*[contains(text(),'常规分组')]
    #不分组
    点击    //span[contains(text(),'不分组')]
    点击    //span[contains(text(),'确定')]
    Sleep    3
    点击    //span[contains(text(),'分组')]
    按项目卡项目类型分组
    Comment    按创建时间分组分组

按项目卡项目类型分组
    #升序
    点击    //span[contains(text(),'重置')]
    Sleep    2
    点击    //span[contains(text(),'确定')]
    Sleep    2
    should contain    //h5[contains(text(),'分组目录')]    分组目录
    should contain    //p[contains(text(),'项目中控台')]    项目中控台
    Sleep    2
    点击    //span[contains(text(),'分组')]
    #降序
    点击    //span[contains(text(),'按项目类型分组')]
    点击    //span[contains(text(),'确定')]
    Sleep    2

按创建时间分组分组
    #升序
    js点击    //span[contains(text(),'分组')]
    点击    //div[@class="group-item-list ng-star-inserted"]/div[3]
    点击    //span[contains(text(),'确定')]
    Sleep    3
    Should Contain    //span[contains(text(),'分组目录')]    分组目录
    Sleep    3
    #降序
    js点击    //span[contains(text(),'分组')]
    点击    //div[@class="group-item-list ng-star-inserted"]/div[3]//*[contains(text(),'降序')]
    点击    //span[contains(text(),'确定')]
    Sleep    3

用户下拉菜单展示
    [Arguments]    ${meu}
    点击    //span[contains(text(),'${meu}')]
    点击    //span[contains(text(),'设置')]

全局搜索切换TAB
    [Arguments]    ${type}    ${query}    ${result}    ${yquery}    ${bquery}
    ${element_exists}    判断元素是否可见    //app-smart-search[@class='ng-star-inserted']
    IF    '${element_exists}'=='False'
        点击    //*[contains(text(),'搜索')]
    END
    点击    //div[@class='search-tab-text'][contains(text(),'${type}')]
    输入    //input[@placeholder='请输入关键词进行搜索']    ${query}
    Sleep    10
    Page Should Contain    ${result}
    点击    //div[contains(text(),'${yquery}')]
    当前页面可见字符    项目大事记
    点击    //div[contains(text(),'${bquery}')]
    当前页面可见字符    项目人员负荷分析

打开发起项目录入界面
    [Arguments]    ${taskid}
    点击顶部菜单    全部
    点击    //span[contains(text(),'发起项目')]
    当前页面可见    //div[@id='${taskid}']
    点击    //div[@id='${taskid}']
    Sleep    3
    当前页面可见    //*[@id="task-content-content-id"]/app-task-rendering/div

关闭娜娜窗口
    # 判断 iframe 是否存在
    ${iframe_exists}    判断元素是否可见    xpath=//*[@id="nanaIframe"]    3
    Run Keyword If    ${iframe_exists}    Run Keywords    iframe选择    AND    js点击 //img[@class='screen-icon']    AND    Unselect Frame
